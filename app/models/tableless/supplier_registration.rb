class SupplierRegistration < TableLess

  # lead mode only included for testing supplier_profile creation on register
  attr_accessor :company_name, :company_address, :suburb, :suburb_id,
  :email, :email_confirmation, :password, :bank_account_number,
  :firstname, :lastname, :phone, :mobile, :abn_acn, :special_instructions, :bsb_number, :lead_mode, :category_group, :is_team_supplier,
  :is_environmentally_accredited, :is_indigenous_owned, :is_registered_charity, :is_socially_responsible, :is_female_owned, :is_lgbtqi_owned, :is_rainforest_alliance_certified, :is_eco_friendly

  validates :firstname, presence: true
  validates :company_name, presence: true
  validates :email, presence: true, confirmation: true, format: { with: /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i }
  validates :password, presence: true, confirmation: true, length: { minimum: 8 }

end
