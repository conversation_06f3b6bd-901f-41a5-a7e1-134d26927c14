class Reminder < ApplicationRecord

  VALID_FREQUENCIES = %w[weekly fortnightly monthly_by_date monthly_by_weekday].freeze
  VALID_RECIPIENTS = %w[account_manager pantry_manager accounts_team]

  validates :title, presence: true
  validates :message, presence: true
  validates :frequency, presence: true, inclusion: { in: VALID_FREQUENCIES }
  validates :starting_at, presence: true
  validates :recipients, presence: true, inclusion: { in: VALID_RECIPIENTS }

  belongs_to :remindable, polymorphic: true, optional: true

  def formatted_frequency
    case frequency
    when 'weekly'
      "Every #{starting_at.strftime('%A')}"
    when 'fortnightly'
      "Fortnightly on #{starting_at.strftime('%As')}"
    when 'monthly_by_date'
      "#{starting_at.day.ordinalize} of each Month"
    when 'monthly_by_weekday'
      "1st #{starting_at.strftime('%A')} of each Month"
    end
  end

  def sent_emails
    emails = Email.where(fk_id: self.id)
    emails = emails.where(template_name: Admin::Emails::SendReminderEmail::EMAIL_TEMPLATE)
    emails.where.not(sent_at: nil)
  end

end
