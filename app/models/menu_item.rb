# == Schema Information
#
# Table name: menu_items
#
#  id                     :integer          not null, primary key
#  menu_section_id        :integer
#  name                   :string(255)
#  description            :text
#  price                  :decimal(10, 2)
#  minimum_quantity       :integer          default(1)
#  is_vegan               :boolean          default(FALSE)
#  is_vegetarian          :boolean          default(FALSE)
#  is_gluten_free         :boolean          default(FALSE)
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  supplier_profile_id    :integer
#  is_gst_free            :boolean          default(FALSE)
#  weight                 :integer
#  is_hidden              :boolean          default(FALSE)
#  image                  :text
#  freight_type           :string(255)      default("chilled")
#  sku                    :string(255)
#  account_code           :string(255)
#  team_order_only        :boolean
#  sub_quantity           :integer
#  team_order             :boolean
#  archived_at            :datetime
#  is_dairy_free          :boolean          default(FALSE)
#  is_individually_packed :boolean          default(FALSE)
#  stock_quantity         :integer
#  promo_price            :decimal(10, 2)
#

class MenuItem < ActiveRecord::Base

	include WithPricing
	include PgSearch::Model
	pg_search_scope :search_by_keywords,
									against: {
										name: 'A',
										description: 'B'
									},
									using: { tsearch: { any_word: true, dictionary: 'english', prefix: true } }

	pg_search_scope :search_all_by_keywords,
									against: {
										name: 'B',
										description: 'C'
									},
									associated_against: {
										active_serving_sizes: {
											name: 'A'
										}
									},
									using: { tsearch: { any_word: true, dictionary: 'english', prefix: true } }

	VALID_FREIGHT_TYPES = %w[ambient chilled frozen].freeze

	validates :name, presence: true, length: { maximum: 255, message: '- Item name is too long (max 255)' }
	validates :menu_section_id, presence: true
	validates :freight_type, inclusion: { in: VALID_FREIGHT_TYPES }
	validates :price, numericality: { greater_than_or_equal_to: 0 }, on: :update, if: -> { price.present? && serving_sizes.blank? }
	validates :minimum_quantity, numericality: { greater_than_or_equal_to: 1 }, on: :update, allow_nil: true # SUPP-720 Updating menu item can leave minimum_quantity as nil.

	belongs_to :menu_section
	belongs_to :supplier_profile
	has_many :serving_sizes
	has_many :active_serving_sizes, -> { where(archived_at: nil) }, class_name: 'ServingSize', foreign_key: :menu_item_id
	has_many :order_lines
	has_many :rate_cards
	has_many :menu_extra_sections
	has_many :menu_extras, through: :menu_extra_sections
	has_many :direct_menu_extras, class_name: 'MenuExtra'
	has_many :woolworths_store_availabilities, class_name: 'Woolworths::StoreAvailability', dependent: :destroy
	has_many :customer_profile_menu_items

	def image?
		image.present?
	end

	def ci_image
		return nil if image.blank?

		image.remove('image/upload/').split('#').first
	end

	def image_id
		return nil if image.blank?

		image.remove('image/upload/').split('#').first.split('yordar-p/').last
	end

	# used for deprecated menu CSV import
	def is_visible
		!is_hidden
	end

	def is_visible=(visible)
		self.is_hidden = !visible
	end

end
