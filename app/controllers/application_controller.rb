class ApplicationController < ActionController::Base
	include ApplicationHelper
  add_flash_types :info, :error, :warning, :success

	protect_from_forgery
	before_action :set_raven_context

	# http://stackoverflow.com/questions/711418/how-to-prevent-browser-page-caching-in-rails
	# to stop back-button to forms
	def set_cache_buster
		response.headers['Cache-Control'] = 'no-cache, no-store, max-age=0, must-revalidate'
		response.headers['Pragma'] = 'no-cache'
		response.headers['Expires'] = 'Fri, 01 Jan 1990 00:00:00 GMT'
	end

	def see_session
		render json: session
	end

	# converts date string to date obj
	def sanitize_date(date, format = '%Y-%m-%d')
		Date.strptime(date, format)
	end

	# Handles the logic to establish which path we should redirect to (after
	# login) based on the user type (customer, supplier, admin)
	#
	def after_sign_up_path_for(resource)
		after_sign_in_path_for(resource)
	end

	def after_sign_in_path_for(resource, as_url=false)
		# 'resource' is the user object
		# 'resource.profile' is profile
		# 'resource.profile.profileable' is either customer/supplier

    resource_has_admin_access = helpers.can_access_admin_portal?

		# we need to load the customer or supplier profile for this user, if not admin.
	  if !resource_has_admin_access && resource.profile.present?
			session_profile(resource.profile.id)
		end

	  return_path = case
    when session_profile.blank? && !resource_has_admin_access
      as_url ? prismic_root_url : prismic_root_path
    when session['user_return_to'].present?
      session['user_return_to']
    when session_profile.blank? && resource_has_admin_access
			as_url ? admin_url : admin_path
		when session_profile.instance_of?(CustomerProfile)
			as_url ? customer_profile_url : customer_profile_path
		when session_profile.instance_of?(SupplierProfile)
			as_url ? supplier_profile_url : supplier_profile_path
    else
      as_url ? prismic_root_url : prismic_root_path
		end

    session['user_return_to'] = ''

		return_path
	end

private

  def set_raven_context
 	  Raven.user_context(id: current_user.try(:id), name: current_user.try(:name))
	 Raven.extra_context(params: params.to_unsafe_h, url: request.url, referrer: request.referrer, session: loggable_session, logrocket: cookies[:logRocketSession])
  end

  def loggable_session
    loggable_keys = %i[session_id profile_id order_id sign_in_as_admin]
   	@_loggable_keys ||= {}
   	loggable_keys.each{|k| @_loggable_keys[k] = session[k] if session[k].present? }
   	@_loggable_keys
  end

  def is_react_app?
 	  return false if request.headers.blank? || request.headers['HTTP_AUTHORIZATION'].blank?

 	  request.headers['HTTP_AUTHORIZATION'] == "token #{yordar_credentials(:next_app_secret)}"
  end

  def ensure_customer
    unless session_profile.present? && session_profile.profile.is_customer?
      respond_to do |format|
        format.html do
          flash[:error] = 'You don\'t have access to the customer dashboard'
          redirect_to after_sign_in_path_for(current_user)
        end
        format.json { head :forbidden }
      end
    end
  end

  def ensure_supplier
    unless session_profile.present? && session_profile.profile.is_supplier?
      respond_to do |format|
        format.html do
          flash[:error] = 'You don\'t have access to the supplier dashboard'
          redirect_to after_sign_in_path_for(current_user)
        end
        format.json { head :forbidden }
      end
    end
  end

  def ensure_admin
    if !is_admin?
      respond_to do |format|
        format.html do
          flash[:notice] = 'You do not have access to this page!'
          redirect_path = current_user.present? ? after_sign_in_path_for(current_user) : prismic_root_url
          redirect_to redirect_path and return
        end
        format.json do
          render json: { errors: 'You do not have admin access!' }, status: :unprocessable_entity
        end
      end
    end
  end

  def ensure_yordar_admin
    if !is_yordar_admin?
      respond_to do |format|
        format.html do
          flash[:notice] = 'You do not have access to this page!'
          redirect_path = current_user.present? ? after_sign_in_path_for(current_user) : prismic_root_url
          redirect_to redirect_path and return
        end
        format.json do
          render json: { errors: 'You do not have admin access!' }, status: :unprocessable_entity
        end
      end
    end
  end

  def ensure_super_admin
    if current_user.blank? || !current_user.super_admin?
      respond_to do |format|
        format.html do
          flash[:notice] = 'You do not have access to this page!'
          redirect_path = current_user.present? ? after_sign_in_path_for(current_user) : prismic_root_url
          redirect_to redirect_path and return
        end
        format.json do
          render json: { errors: 'You do not have admin access!' }, status: :unprocessable_entity
        end
      end
    end
  end

  def ensure_admin_portal_access
    if !can_access_admin_portal?
      respond_to do |format|
        format.html do
          flash[:notice] = 'You do not have access to this page!'
          redirect_path = current_user.present? ? after_sign_in_path_for(current_user) : prismic_root_url
          redirect_to redirect_path and return
        end
        format.json do
          render json: { errors: 'You do not have admin access!' }, status: :unprocessable_entity
        end
      end
    end
  end

  def save_suburb_cookie(suburb)
    return if suburb.blank?

    cookies[:yordar_suburb_id] = { value: suburb.id, domain: cookie_domain(host: request&.host) }
    cookies[:yordar_suburb_label] = { value: suburb.label, domain: cookie_domain(host: request&.host) }
    cookies[:yordar_suburb] = { value: suburb.name, domain: cookie_domain(host: request&.host) }
    cookies[:yordar_postcode] = { value: suburb.postcode, domain: cookie_domain(host: request&.host) }
    cookies[:yordar_state] = { value: suburb.state, domain: cookie_domain(host: request&.host) }
  end

  def can_redirect_to_next_app?
    rails_subdomain = yordar_credentials(:rails_subdomain)
    next_subdomain = yordar_credentials(:next_app_subdomain)
    next_app_port = yordar_credentials(:next_app_port)
    request.subdomain.present? &&
      rails_subdomain.present? &&
      (next_subdomain.present? || next_app_port.present?) &&
      request.subdomain.include?(rails_subdomain) &&
      request.format == 'html'
  end

end
