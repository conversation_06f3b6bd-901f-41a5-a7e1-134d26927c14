class SupplierRegistrationsController < ApplicationController

  def new
    @registration = SupplierRegistration.new(new_registration_params)
  end

  def create
    @registration = SupplierRegistration.new(supplier_registration_params)
    redirect_or_render = nil
    flash_messages = {}
    if verify_recaptcha
      supplier_registration = Suppliers::Register.new(registration: @registration).call
      if supplier_registration.success?
        flash_messages[:success] = 'Thank you for registering as a supplier. <PERSON>rdar Admin has been notified, you\'ll be contacted shortly.'
        redirect_or_render = new_supplier_registration_path
      else
        error_messages = supplier_registration.errors
        flash_messages[:errors] = error_messages.join('. ')
        redirect_or_render = :new
      end
    else
      error_messages = ['Please check the captcha tickbox and verify that you are human!']
      flash_messages[:warning] = error_messages.join('.')
      redirect_or_render = :new
    end
    respond_to do |format|
      format.html do
        flash = flash_messages
        if redirect_or_render.is_a?(Symbol)
          render redirect_or_render
        else
          redirect_to redirect_or_render and return
        end
      end
      format.json do
        if error_messages.present?
          render json: { errors: error_messages }, status: :unprocessable_entity
        else
          head :ok
        end
      end
    end
  end

protected

  def new_registration_params
    params.permit(:email, :firstname, :lastname)
  end

  def supplier_registration_params
    info_fields = %i[firstname lastname email]
    company_fields = %i[company_name company_address abn_acn bsb_number bank_account_number phone mobile]
    category_fields = %i[category_group is_team_supplier]
    address_fields = %i[suburb suburb_id]
    certification_fields = SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS
    params.require(:supplier_registration).permit(:password, *info_fields, *company_fields, *category_fields, *address_fields, *certification_fields)
  end

end
