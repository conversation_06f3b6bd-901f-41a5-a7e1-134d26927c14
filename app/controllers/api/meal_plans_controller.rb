class API::MealPlansController < ApplicationController

  before_action :ensure_customer
  before_action :fetch_customer
  before_action :fetch_meal_plan, except: :index

  def index
    @meal_plans = @customer.meal_plans.where(archived_at: nil).order(created_at: :asc, id: :asc)
  end

  def create
    meal_plan_creator = MealPlans::Create.new(customer: @customer, meal_plan_params: meal_plan_params).call
    respond_to do |format|
      format.json do
        if meal_plan_creator.success?
          render partial: 'api/meal_plans/meal_plan', locals: { meal_plan: meal_plan_creator.meal_plan }
        else
          render json: { errors: meal_plan_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    @meal_plan_updater = MealPlans::Update.new(customer: @customer, meal_plan: @meal_plan, meal_plan_params: meal_plan_params, update_params: update_params).call
    respond_to do |format|
      format.json do
        if @meal_plan_updater.success?
          render partial: 'api/meal_plans/meal_plan', locals: { meal_plan: @meal_plan_updater.meal_plan }
        else
          render status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    @meal_plan_archiver = MealPlans::Archive.new(customer: @customer, meal_plan: @meal_plan, remove_params: update_params).call
    respond_to do |format|
      format.json do
        if @meal_plan_archiver.success?
          render partial: 'api/meal_plans/meal_plan', locals: { meal_plan: @meal_plan_archiver.meal_plan }
        else
          render status: :unprocessable_entity
        end
      end
    end
  end

  def orders
    page = params[:page].try(:to_i) || 1
    limit = params[:limit].try(:to_i) || 20
    limit = nil if order_list_params[:from_date].present? && order_list_params[:to_date].present?
    lister_options = {
      for_customer: @customer,
      meal_plan: @meal_plan,
      for_duration: 'all',
      with_pagination: { page: page, limit: limit },
    }.merge(order_list_params.to_h.symbolize_keys)
    @orders = Orders::List.new(options: lister_options).call
  end

private

  def fetch_customer
    @customer = session_profile
  end

  def fetch_meal_plan
    @meal_plan = MealPlan.where(id: params[:id] || params[:meal_plan_id]).first
  end

  def meal_plan_params
    params.require(:meal_plan).permit(:kind, :name, :number_of_people, :cpo_id, :gst_free_cpo_id, :department_identity, :delivery_time, :delivery_address_level, :delivery_address, :delivery_suburb_id, :delivery_instruction, :credit_card_id, :reminder_frequency, :admin_notes)
  end

  def update_params
    params.permit(:mode, order_ids: [])
  end

  def order_list_params
    params.permit(:for_date, :from_date, :to_date)
  end
end
