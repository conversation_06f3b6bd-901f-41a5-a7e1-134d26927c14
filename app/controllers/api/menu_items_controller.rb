class API::MenuItemsController < ApplicationController

  before_action :ensure_admin, only: :index
  before_action :fetch_menu_item, only: %i[clone update destroy check_usage]

  # used by rate card creator
  def index
    supplier = SupplierProfile.where(id: params[:supplier_id]).first
    item_lister_options = {
      show_visible: true,
      show_active: true,
      supplier: supplier,
      ignore_custom_menu_sections: true,
      order_by: { name: :asc },
      is_admin: true,
    }
    @menu_items = MenuItems::List.new(includes: [:menu_section], options: item_lister_options).call
    respond_to do |format|
      format.json
    end
  end

  def create
    item_creator = MenuItems::Upsert.new(menu_item_params: menu_item_params, forced: true).call
    respond_to do |format|
      format.json do
        if item_creator.success?
          render partial: 'api/menu_items/menu_item', locals: { menu_item: item_creator.menu_item }
        else
          render json: { errors: item_creator.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def clone
    item_cloner = MenuItems::Clone.new(menu_item: @menu_item).call
    respond_to do |format|
      format.json do
        if item_cloner.success?
          render partial: 'api/menu_items/menu_item', locals: { menu_item: item_cloner.cloned_item }
        else
          render json: { errors: item_cloner.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def update
    item_updater = MenuItems::Upsert.new(menu_item: @menu_item, menu_item_params: menu_item_params).call
    respond_to do |format|
      format.json do
        if item_updater.success?
          render partial: 'api/menu_items/menu_item', locals: { menu_item: item_updater.menu_item }
        else
          render json: { errors: item_updater.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def destroy
    item_archiver = MenuItems::Archive.new(menu_item: @menu_item, forced: params[:forced]).call
    respond_to do |format|
      format.json do
        if item_archiver.success?
          render partial: 'api/menu_items/menu_item', locals: { menu_item: item_archiver.menu_item }
        else
          render json: { errors: item_archiver.errors, warnings: item_archiver.warnings }, status: :unprocessable_entity
        end
      end
    end
  end

  def search
    active_search = search_supplier_params[:for_admin] ? search_supplier_params[:show_archived].blank? : true
    item_lister_options = {
      profile: session_profile,
      show_visible: true,
      show_active: active_search,
      ignore_custom_menu_sections: true,
      suburb: cookie_suburb,
    }.merge(search_params)
    item_lister_options[:suppliers] = search_supplier_params[:supplier_ids].present? ? SupplierProfile.where(id: search_supplier_params[:supplier_ids]) : SupplierProfile.where(is_searchable: true)
    listed_menu_items = MenuItems::List.new(includes: %i[supplier_profile serving_sizes], options: item_lister_options).call
    @menu_items = listed_menu_items.includes(:menu_section).order('menu_sections.weight asc, menu_items.weight asc')
    case 
    when search_supplier_params[:for_supplier_menu].present?
      @markup_override = Suppliers::FetchMarkupOverride.new(supplier: item_lister_options[:suppliers].first, customer: session_profile, required_override: :markup).call
      @favourite_menu_item_ids = session_profile.present? ? session_profile.favourite_menu_items.map(&:menu_item_id) & @menu_items.map(&:id) : []
      render 'api/menu_items/search_for_supplier'
    when search_supplier_params[:for_admin].present?
      render 'api/menu_items/admin_search'
    else
      render 'api/menu_items/search'
    end
  end

  def check_usage
    @item_orders = MenuItems::ListUsageInOrders.new(menu_item: @menu_item, since: Time.zone.now.beginning_of_day).call
  end

private

  def fetch_menu_item
    @menu_item = MenuItem.where(id: params[:id] || params[:menu_item_id]).first
  end

  def menu_item_params
    item_fields = %i[name weight description sku menu_section_id price promo_price supplier_profile_id]
    quantity_fields = %i[stock_quantity minimum_quantity sub_quantity]
    flag_fields = %i[is_gst_free is_individually_packed is_hidden team_order_only team_order]
    dietary_fields = %i[is_vegetarian is_vegan is_gluten_free is_dairy_free is_egg_free is_halal is_kosher is_nut_free]
    image_fields = %i[image remove_image]
    item_params = params.require(:menu_item).permit(*item_fields, *quantity_fields, *flag_fields, *dietary_fields, *image_fields)
    item_params[:image] = nil if !item_params[:image].nil? && item_params[:image].blank?
    item_params
  end

  def search_params
    params.permit(:query, :page, :limit, :thorough_search)
  end

  def search_supplier_params
    params.permit(:for_supplier_menu, :for_admin, :show_archived, supplier_ids: [])
  end

end
