class API::OrdersController < ApplicationController
  include OrdersHelper

  before_action :ensure_customer, only: %i[index destroy]
  before_action :ensure_not_a_supplier, except: %i[confirm_order reject_order]
  before_action :fetch_order, except: %i[create checkout]
  before_action :ensure_order_edit_access, only: :edit
  before_action :check_order_supplier, only: %i[confirm_order reject_order]
  before_action :setup_suburb_from_name
  skip_before_action :verify_authenticity_token, if: :is_react_app?

  def index
    page = params[:page].try(:to_i) || 1
    limit = params[:limit].try(:to_i) || 20
    lister_options = {
      for_customer: session_profile,
      for_duration: params[:for_duration] || 'all',
      with_pagination: { page: page, limit: limit },
      ignore_cancelled_recurrent: order_list_params[:show_past].blank?
    }.merge(order_list_params.to_h.symbolize_keys)
    @orders = Orders::List.new(options: lister_options, includes: %i[supplier_profiles team_supplier_profiles]).call
  end

  # only used by Woolworths delivery Modal
  def create
    order_creator = Orders::Create.new(order_params: new_order_params, suburb: @suburb_from_name, customer: session_profile, cookies: cookies, whodunnit: current_user).call
    if order_creator.success?
      order = order_creator.order
      session_order(order.id) # setup session
      render json: { order: { id: order.id }, is_woolworths_order: order.reload.woolworths_order.present? }
    else
      render json: { errors: order_creator.errors }, status: :unprocessable_entity
    end
  end

  def show
    order = case
    when params[:from_session].present?
      session_order
    else # params[:id].present? || params[:order_id].present?
      Order.where(id: params[:id] || params[:order_id]).first
    end

    # if order does not belong to the current session, start a new order
    if session_profile.present? && order&.customer_profile != session_profile
      order = nil
    end

    supplier_order = Orders::SetupForSupplierMenu.new(session_order: order, cookies: cookies).call
    fetch_meal_plan_for(supplier_order.order) if is_admin?
    render 'api/orders/show', locals: { supplier_order: supplier_order }
  end

  def checkout
    if (order = session_order.presence)
      @checkout_setup = Orders::SetupForCheckout.new(order: order, profile: session_profile, cookies: cookies).call
    else
      render json: { errors: 'No order available' }, status: :unprocessable_entity
    end
  end

  def edit
    edit_access_checker = Orders::CheckEditAccess.new(order: @order, current_user: current_user, profile: session_profile).call
    if !edit_access_checker.success?
      render json: { errors: edit_access_checker.errors, redirect_url: after_sign_in_path_for(current_user, as_url: true) }, status: :unprocessable_entity
    end
  end

  def update
    order_updater = Orders::Update.new(order: @order, order_params: order_params, suburb: @suburb_from_name, profile: session_profile).call
    if order_updater.success?
      render locals: { order: @order, totals: retrieve_totals }
    else
      render json: { errors: order_updater.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    order = @order
    if order.is_team_order?
      order_canceller = TeamOrders::Cancel.new(team_order: order, team_admin: session_profile, notify_attendees: params[:notify_attendees].present?).call
      order = order_canceller.team_order
    else
      order_canceller = Orders::Cancel.new(order: order, mode: params[:cancel_mode]).call
      order = order_canceller.order
    end

    if order_canceller.success?
      render partial: 'api/orders/list/order', locals: { order: order }
    else
      render json: order_canceller.errors, status: :unprocessable_entity
    end
  end

  def validate
    order_validator = Orders::ValidateOrderLines.new(order: @order).call
    if order_validator.success?
      head :ok
    else
      render json: { errors: order_validator.errors, warnings: order_validator.warnings }, status: :unprocessable_entity
    end
  end

  def validate_woolworths_order
    @order_validator = Woolworths::Order::Validate.new(order: @order).call
  end

  def submit
    is_quoted_order = @order.status == 'quoted'
    if %w[quote save-quote save-for-later].include?(quote_params[:mode]) || quote_params[:quote_emails].present?
      order_submitter = Orders::Quote.new(order: @order, order_params: submit_order_params, customer: session_profile, quote_params: quote_params).call
    else
      order_submitter = Orders::Submit.new(order: @order, order_params: submit_order_params, customer: session_profile).call
    end

    if order_submitter.success?
      customer = order_submitter.order.customer_profile
      billing_details_upserter = Customers::BillingDetails::Upsert.new(customer: customer, detail_params: billing_details_params).call
    else
      billing_details_upserter = OpenStruct.new(success?: false, errors: []) # default it to fail, but with no errors
    end

    respond_to do |format|
      format.json do
        if order_submitter.success? && billing_details_upserter.success?
          clean_up_session_orders if session_order&.id == @order.id
          order = order_submitter.order
          redirect_url = is_quoted_order ? post_edit_page_for(order) : nil
          render 'api/orders/success', locals: { order: order, quote_mode: quote_params[:mode], redirect_url: redirect_url }
        else
          render json: { errors: order_submitter.errors + billing_details_upserter.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def amend
    case
    when %w[quote save-quote save-for-later].include?(quote_params[:mode])
      order_amender = Orders::Quote.new(order: @order, order_params: submit_order_params, customer: session_profile, quote_params: quote_params).call
    when @order.status == 'delivered'
      order_amender = Orders::Adjust.new(order: @order, order_params: submit_order_params, customer: session_profile, is_admin: is_yordar_admin?).call
    else
      order_amender = Orders::Amend.new(order: @order, order_params: submit_order_params, mode: params[:mode], customer: session_profile).call
    end

    respond_to do |format|
      format.json do
        if order_amender.success?
          clean_up_session_orders if session_order&.id == @order.id
          render json: { success: true, redirect_url: post_edit_page_for(@order) }
        else
          render json: { errors: order_amender.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def reactivate
    order_reactivator = Orders::Reactivate.new(order: @order, mode: params[:reactivate_mode]).call
    if order_reactivator.success?
      render partial: 'api/orders/list/order', locals: { order: order_reactivator.order }
    else
      render json: order_reactivator.errors, status: :unprocessable_entity
    end
  end

  def check_swipe_card_access
    delivery_at = order_delivery_params[:order_delivery_at].present? ? Time.zone.parse(order_delivery_params[:order_delivery_at]) : @order.delivery_at
    @swipe_access_suppliers = Orders::FetchSwipeCardAccessSuppliers.new(order: @order, delivery_at: delivery_at).call
  end

  def check_closure_dates
    @supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: @order, new_delivery_at: order_delivery_params[:order_delivery_at], suburb_id: order_delivery_params[:suburb_id]).call
  end

  def check_lead_time
    @lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: @order, new_delivery_at: order_delivery_params[:order_delivery_at]).call
  end

  def validate_checkout_date
    @lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: @order, new_delivery_at: order_delivery_params[:order_delivery_at], supplier_ids: order_delivery_params[:supplier_ids]).call
    @supplier_closure_fetcher = Orders::FetchSupplierClosures.new(order: @order, new_delivery_at: order_delivery_params[:order_delivery_at], suburb_id: (order_delivery_params[:suburb_id] || cookies[:yordar_suburb_id]), supplier_ids: order_delivery_params[:supplier_ids]).call
  end

  def check_minimum_spend
    @suppliers_min_spends = Orders::GetMinimumSpends.new(order: @order).call
  end

  def check_supplier_suburb_availability
    suburb = Suburb.where(id: params[:suburb_id]).first
    @availability_fetcher = Orders::FetchSupplierAvailability.new(order: @order, suburb: suburb).call
  end

  def confirm_order
    order_confirmer = Suppliers::ConfirmOrder.new(supplier: session_profile, order: @order).call
    if order_confirmer.success?
      @order_confirmer = order_confirmer
    else
      render json: { errors: order_confirmer.errors }, status: :unprocessable_entity
    end
  end

  def reject_order
    order_rejecter = Suppliers::RejectOrder.new(supplier: session_profile, order: @order).call
    if order_rejecter.success?
      @order_rejecter = order_rejecter
    else
      render json: { errors: order_rejecter.errors }, status: :unprocessable_entity
    end
  end

  def make_recurrent
    order = @order || session_order
    if order.blank?
      order_creator = Orders::Create.new(order_params: order_creation_params, customer: session_profile, cookies: cookies, whodunnit: current_user).call
      if order_creator.success?
        order = order_creator.order
      end
    end
    order_converter = Orders::ConvertToRecurrent.new(order: order, recurrent_params: order_repeat_params).call
    if order_converter.success?
      session_order_ids(order_converter.recurrent_orders.map(&:id) + [order_converter.order.id]) # save to session
      session_order(order_converter.order.id)
      respond_to do |format|
        format.html { redirect_to request.referer }
        format.json do
          suburb = Suburb.where(id: cookies[:yordar_suburb_id]).first
          supplier_order = Orders::SetupForSupplierMenu.new(session_order: order_converter.order, suburb: suburb).call
          render 'api/orders/show', locals: { supplier_order: supplier_order }
        end
      end
    else
      respond_to do |format|
        format.html do
          flash[:warning] = order_converter.errors.join('. ')
          redirect_to request.referer and return
        end
        format.json do
          render json: { errors: order_converter.errors }, status: :unprocessable_entity
        end
      end
    end
  end

  def clone
    order_cloner = Orders::Clone.new(order: @order).call
    if order_cloner.success?
      cloned_order = order_cloner.cloned_order
      session_order(cloned_order.id)
      supplier = cloned_order.order_lines.first.supplier_profile
      save_suburb_cookie(cloned_order.delivery_suburb)
      redirect_to next_app_supplier_show_url(supplier.slug)
    else
      redirect_to after_sign_in_path_for(current_user) and return
    end
  end

  def fetch_delivery_windows
    Woolworths::Order::GetAvailableDeliveryWindows.new(order: @order).call
  end

  def export_pdf
    meal_plan = MealPlan.where(id: pdf_export_params[:meal_plan_id]).first
    orders = Order.where(id: pdf_export_params[:order_ids]).order(delivery_at: :asc, id: :asc)
    if orders.present?
      respond_to do |format|
        format.json do
          document = Documents::Generate::CustomerMealPlan.new(meal_plan: meal_plan, orders: orders, variation: pdf_export_params[:variation]).call
          render json: { name: document.name, url: document.url, version: document.version }
        end
        format.pdf do
          document = Documents::Generate::CustomerMealPlan.new(meal_plan: meal_plan, orders: orders, variation: pdf_export_params[:variation]).call(dry_run: true)
          send_file document.url, filename: "#{document.name}.pdf"
        end
      end
    else
      respond_to do |format|
        format.json do
          render json: { errors: ['Cannot Generate Document without orders'] }, status: :unprocessable_entity
        end
        format.pdf do
          head :bad_request
        end
      end
    end
  end

private

  def fetch_order
    @order = Order.where(id: params[:id] || params[:order_id]).first
  end

  def check_order_supplier
    if session_profile.present? && session_profile.instance_of?(SupplierProfile) && @order.present? && @order.supplier_profiles.where(id: session_profile.id).present?
      # all good
    else
      respond_to do |format|
        format.html { redirect_to prismic_root_url, error: 'You do not have access to this order' }
        format.json { render json: { not_found: true }, status: 404 }
      end
    end
  end

  def order_list_params
    params.permit(:name, :query, :order_variant, :order_type, :show_past, :for_date, :from_date, :to_date, :only_quotes, :meal_plan_id, supplier_ids: [], statuses: [])
  end

  def order_params
    charge_fields = %i[no_delivery_charge charge_to_minimum coupon_code]
    delivery_fields = %i[delivery_address_level delivery_address delivery_suburb_id delivery_instruction delivery_at]
    invoice_fields = %i[invoice_id update_with_invoice]
    order_supplier_fields = %i[id supplier_profile_id delivery_fee_override]
    woolworths_order_fields = %i[id delivery_window_id]
    params.require(:order).permit(*charge_fields, *delivery_fields, *invoice_fields, :is_woolworths_order, order_supplier: order_supplier_fields, associated_woolworths_order_attributes: woolworths_order_fields)
  end

  def new_order_params
    delivery_info_fields = %i[delivery_address_level delivery_address delivery_suburb_id delivery_instruction delivery_at]
    params.require(:order).permit(*delivery_info_fields, :is_home_delivery, :is_woolworths_order)
  end

  def quote_params
    params.permit(:mode, :quote_emails, :quote_message)
  end

  def order_delivery_params
    params.permit(:order_delivery_at, :suburb_id, supplier_ids: [])
  end

  def submit_order_params
    order_fields = %i[name number_of_people skip cpo_id gst_free_cpo_id credit_card_id invoice_individually pantry_manager_id meal_plan_id]
    contact_fields = %i[department_identity contact_name company_name phone]
    delivery_fields = %i[delivery_at delivery_address_level delivery_address delivery_suburb_id delivery_instruction delivery_type]
    woolworths_order_fields = %i[id woolworths_order_id delivery_window_id]
    params.require(:order).permit(*order_fields, *contact_fields, *delivery_fields, associated_woolworths_order_attributes: woolworths_order_fields)
  end

  def billing_details_params
    return {} if params[:billing_details].blank?

    params.require(:billing_details).permit(:suburb_id, :email, :phone, :address, :name)
  end

  # used for creating new order from recurring form (within a meal plan)
  def order_creation_params
    params.permit(:mealUUID)
  end

  def order_repeat_params
    permitted_day_params = %i[sun mon tue wed thu fri sat]
    params.require(:repeat).permit(:frequency, :skip, :copy_all, recurrent_days: [], days: permitted_day_params)
  end

  def retrieve_totals
    Orders::RetrieveTotals.new(order: @order, profile: session_profile).call
  end

  def ensure_not_a_supplier
    if session_profile.present? && session_profile.instance_of?(SupplierProfile)
      respond_to do |format|
        format.html do
          flash[:error] = 'You don\'t have access to the session order'
          redirect_to after_sign_in_path_for(current_user) and return
        end
        format.json { head :forbidden }
      end
    end
  end

  def suburb_params
    params.permit(:suburb, :state, :postcode, :street_address)
  end

  def setup_suburb_from_name
    return if suburb_params.blank?

    @suburb_from_name = Suburbs::FetchForSearch.new(suburb_params: suburb_params, suburb_cookies: cookies, host: request&.host).call
    save_suburb_cookie(@suburb_from_name)
  end

  def pdf_export_params
    params.permit(:meal_plan_id, :variation, order_ids: [])
  end

  def post_edit_page_for(order)
    case
    when (meal_plan = order.meal_plan.presence)
      customer_meal_plans_url(mealUUID: meal_plan.uuid, date: order.delivery_at&.to_s(:date_spreadsheet))
    when order.delivery_at < Time.zone.now.beginning_of_day
      customer_profile_url(show_past: true)
    else
      customer_profile_url
    end
  end

  def ensure_order_edit_access
    return true if session_profile.present? && session_profile.profile.is_customer?

    login_redirect_url = case
    when @order.present? && current_user.blank?
      new_user_session_url(user_return_to: next_app_order_edit_url(@order, **order_edit_params))
    when @order.present? && current_user.present? && session_profile.blank? && has_access_to?(customer: @order.customer_profile) # logged in as CTA
      sign_in_as_customer_url(@order.customer_profile.user, redirect_path: next_app_order_edit_url(@order, **order_edit_params))
    end

    respond_to do |format|
      format.html do
        flash[:error] = 'You are not logged in as a customer!'
        redirect_to current_user.present? ? after_sign_in_path_for(current_user) : new_user_session_url
      end
      format.json do
        if login_redirect_url.present?
          render json: { login_redirect_url: login_redirect_url }, status: :forbidden
        else
          head :forbidden
        end
      end
    end
  end

  def order_edit_params
    params.permit(:finaliseQuote)
  end

  def has_access_to?(customer:)
    return false if customer.blank?

    profileable = current_user&.profile&.profileable
    return false if profileable.blank? || !profileable.profile.is_customer? || !profileable&.company_team_admin

    lister_options = {
      customer: customer,
      for_user: current_user,
      limit: nil
    }
    ::Admin::ListCustomers.new(options: lister_options).call.present?
  end

  def fetch_meal_plan_for(order)
    @meal_plan = order&.meal_plan.presence || (params[:mealUUID].present? && MealPlan.where(uuid: params[:mealUUID]).first)
  end

end
