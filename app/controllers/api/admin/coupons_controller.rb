class API::Admin::CouponsController < API::AdminController

  before_action :ensure_yordar_admin
  before_action :fetch_coupon, only: :update

  def index
    lister_options = {
      page: 1,
      limit: 20,
    }.merge(list_params.to_h.symbolize_keys)
    @coupons = ::Admin::ListCoupons.new(options: lister_options, includes: [:orders]).call
  end

  def create
    coupon = Coupon.new(coupon_params)
    if coupon.save
      render partial: 'api/admin/coupons/coupon', locals: { coupon: coupon }
    else
      render json: { errors: coupon.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @coupon.blank?
      render json: { errors: 'could not find coupon' }, status: :not_found
    end

    if @coupon.update(coupon_params)
      render partial: 'api/admin/coupons/coupon', locals: { coupon: @coupon.reload }
    else
      render json: { errors: @coupon.errors.full_messages }, status: :unprocessable_entity
    end
  end

private
  
  def list_params
    params.permit(:page, :limit, :query)
  end

  def fetch_coupon
    @coupon = Coupon.where(id: (params[:id] || params[:coupon_id])).first
  end

  def coupon_params
    params.require(:coupon).permit(:code, :description, :redemption_limit, :type, :amount, :valid_from, :valid_until)
  end

end