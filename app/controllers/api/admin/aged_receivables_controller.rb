class API::Admin::AgedReceivablesController < API::AdminController

  before_action :ensure_yordar_admin

  def index
    @lister_options = {
      user: current_user,
      sort_by: 'company_name',
    }.merge(list_params.to_h.deep_symbolize_keys)
    @aged_receivables = ::Admin::ListAgedReceivables.new(options: @lister_options).call

    respond_to do |format|
      format.json
      format.csv do
        filename = "aged-receivable-summary-#{Time.zone.now.to_s(:filename)}.csv"
        send_data (render_to_string 'api/admin/aged_receivables/index', layout: false), filename: filename
      end
    end
  end

private

  def list_params
    params.permit(:query, :time_frame)
  end

end