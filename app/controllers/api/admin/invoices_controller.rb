class API::Admin::InvoicesController < API::AdminController

  before_action :fetch_invoice, only: %i[show update]
  before_action :ensure_invoice_access, only: %i[show update]

  def index
    lister_options = {
      page: 1,
      limit: 20,
      for_user: current_user,
    }.merge(list_params.to_h.symbolize_keys)
    @invoices = ::Admin::ListInvoices.new(options: lister_options, includes: [orders: { customer_profile: :company }]).call
  end

  def show; end

  def update
    if @invoice.blank?
      render json: { errors: 'could not find invoice' }, status: :not_found
    end

    if @invoice.update(invoice_params)
      render partial: 'api/admin/invoices/invoice', locals: { invoice: @invoice.reload }
    else
      render json: { errors: @invoice.errors.full_messages }, status: :unprocessable_entity
    end
  end

private
  
  def list_params
    params.permit(:page, :limit, :query, :favourites_only, :payment_status)
  end

  def fetch_invoice
    @invoice = Invoice.where(id: params[:id] || params[:invoice_id]).first
  end

  def invoice_params
    params.require(:invoice).permit(:do_not_notify)
  end

  def ensure_invoice_access
    adminable_invoice = nil
    if @invoice.present?
      lister_options = { for_user: current_user, invoice: @invoice }
      invoices = ::Admin::ListInvoices.new(options: lister_options).call
      adminable_invoice = invoices.first
    end
    if adminable_invoice.blank?
      respond_to do |format|
        format.json do
          render json: { errors: 'Do not have access to this Invoice!' }, status: :unprocessable_entity
        end
      end
    end
  end

end