class API::Admin::RemindersController < API::AdminController

  before_action :ensure_admin
  before_action :fetch_reminder, only: %i[show update destroy]

  def index
    lister_options = {
      page: 1,
      limit: 20,
      active_only: true,
    }.merge(list_params.to_h.symbolize_keys)
    @reminders = ::Admin::ListReminders.new(options: lister_options, includes: [:remindable]).call
  end

  def show
    render partial: 'api/admin/reminders/reminder', locals: { reminder: @reminder, with_emails: true }
  end

  def create
    reminder = Reminder.new(reminder_params)
    if reminder.save
      render partial: 'api/admin/reminders/reminder', locals: { reminder: reminder }
    else
      render json: { errors: reminder.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @reminder.blank?
      render json: { errors: 'could not find reminder' }, status: :not_found
    end

    if @reminder.update(reminder_params)
      render partial: 'api/admin/reminders/reminder', locals: { reminder: @reminder.reload }
    else
      render json: { errors: @reminder.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def destroy
    case
    when @reminder.blank?
      render json: { errors: 'could not find reminder' }, status: :not_found
    when @reminder.sent_emails.present?
      @reminder.update(active: false)
      render partial: 'api/admin/reminders/reminder', locals: { reminder: @reminder.reload }
    else
      @reminder.destroy
      head :ok
    end
  end

private

  def list_params
    params.permit(:page, :limit, :query)
  end

  def fetch_reminder
    @reminder = Reminder.where(id: (params[:id] || params[:reminder_id])).first
  end

  def reminder_params
    params.require(:reminder).permit(:title, :message, :frequency, :starting_at, :remindable_type, :remindable_id, :recipients)
  end

end