class API::Admin::PromotionsController < API::AdminController

  before_action :ensure_yordar_admin
  before_action :fetch_promotion, only: :update

  def index
    lister_options = {
      page: 1,
      limit: 20,
    }.merge(list_params.to_h.symbolize_keys)
    @promotions = ::Admin::ListPromotions.new(options: lister_options).call
  end

  def create
    promotion = Promotion.new(promotion_params)
    if promotion.save
      render partial: 'api/admin/promotions/promotion', locals: { promotion: promotion }
    else
      render json: { errors: promotion.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def update
    if @promotion.blank?
      render json: { errors: 'could not find promotion' }, status: :not_found
    end

    if @promotion.update(promotion_params)
      render partial: 'api/admin/promotions/promotion', locals: { promotion: @promotion.reload }
    else
      render json: { errors: @promotion.errors.full_messages }, status: :unprocessable_entity
    end
  end

private
  
  def list_params
    params.permit(:page, :limit, :query)
  end

  def fetch_promotion
    @promotion = Promotion.where(id: (params[:id] || params[:promotion_id])).first
  end

  def promotion_params
    params.require(:promotion).permit(:name, :description, :kind, :amount, :valid_from, :valid_until, :category_restriction)
  end

end