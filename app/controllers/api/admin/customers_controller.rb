class API::Admin::CustomersController < API::AdminController

  before_action :fetch_customer, only: %i[update mark_as_favourite]
  before_action :fetch_customer_by_uuid, only: %i[send_invitation_link invite_customer]

  def index
    lister_options = {
      page: 1,
      limit: 20,
      for_user: current_user,
      order_by: customer_sort_option,
    }.merge(list_params.to_h.symbolize_keys)

    # Fetch all customers based on the list options
    all_customers = ::Admin::ListCustomers.new(options: lister_options, includes: %i[company user]).call

    # un-shift current user customer to top of list if present
    if (current_user_customer = all_customers.find {|customer| customer.user == current_user }.presence)
      @customers = [current_user_customer] + (all_customers - [current_user_customer])
    else
      @customers = all_customers
    end
    @favourite_customer_ids = current_user&.favourite_customer_profile_ids
  end

  def update
    @customer.user.update(customer_user_params) if customer_user_params.present?
    if @customer.update(customer_params)
      render partial: 'api/admin/customers/customer', locals: { customer: @customer.reload }
    else
      render json: { errors: @customer.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def mark_as_favourite
    customer_favouriter = ::Admin::FavouriteCustomer.new(user: current_user, customer: @customer).call
    if customer_favouriter.success?
      render partial: 'api/admin/customers/customer', locals: { customer: @customer.reload }
    else
      render json: { errors: customer_favouriter.errors }, status: :unprocessable_entity
    end
  end

  def send_invitation_link
    case params[:invite_type]
    when 'customer'
      Customers::Emails::SendCustomerInvitationLinkEmail.new(customer: @customer).delay(queue: :notifications).call
    when 'admin'
      # do nothing
    else
      OpenStruct.new(success?: false, errors: ['Invalid invite type'])
    end
    head :ok
  end

  def invite_customer
    invite_sender = case params[:invite_type]
    when 'customer'
      Customers::Emails::SendCustomerInviteEmail.new(customer: @customer, invite_params: invite_params).call
    when 'admin'
      Customers::Emails::SendCustomerAdminInviteEmail.new(customer: @customer, invite_params: invite_params).call
    else
      OpenStruct.new(success?: false, errors: ['Invalid invite type'])
    end
    if invite_sender.success?
      head :ok
    else
      render json: { errors: invite_sender.errors }, status: :unprocessable_entity
    end
  end

  def add_adminable_customer
    company_team_admin = CustomerProfile.where(uuid: params[:customer_id]).first
    customer_adder = Customers::AddNewAdminableCustomer.new(customer_params: new_customer_params, company_team_admin: company_team_admin).call
    if customer_adder.success?
      customer = customer_adder.customer
      render partial: 'api/admin/customers/customer', locals: { customer: customer }
    else
      render json: { errors: customer_adder.errors }, status: :unprocessable_entity
    end
  end

private

  def list_params
    params.permit(:page, :limit, :query, :favourites_only, :my_customers)
  end

  def fetch_customer
    @customer = CustomerProfile.where(id: params[:id] || params[:customer_id]).first
  end

  def fetch_customer_by_uuid
    @customer = CustomerProfile.where(uuid: params[:id] || params[:customer_id]).first
  end

  def customer_params
    params.require(:customer_profile).permit(:team_admin, :company_team_admin)
  end

  def customer_user_params
    params.require(:customer_profile).permit(:admin)
  end

  def invite_params
    params.permit(:first_name, :last_name, :email)
  end

  def new_customer_params
    params.require(:customer).permit(:firstname, :lastname)
  end

  def customer_sort_option
    case
    when is_company_team_admin?
      { customer_name: :asc }
    when list_params[:favourites_only].present?
      { customer_name: :asc }
    when list_params[:query].present?
      { customer_name: :asc }
    else
      { created_at: :desc }
    end
  end

end