class API::Admin::<PERSON>minsController < API::AdminController

  before_action :ensure_yordar_admin
  before_action :fetch_admin, only: :update

  def index
    lister_options = {
      page: 1,
      limit: 20,
    }.merge(list_params.to_h.symbolize_keys)

    @admins = ::Admin::ListAdmins.new(options: lister_options, includes: [:customer_profile]).call
  end

  def update
    if @admin.update(admin_params)
      render partial: 'api/admin/admins/admin', locals: { admin: @admin, kind: 'yordar_admin' }
    else
      render json: { errors: @admin.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def pantry_manager_spends
    @pantry_manager_spends = ::Admin::Reports::FetchPantryManagerSpends.new(options: spend_params).call
    respond_to do |format|
      format.json
      format.csv do
        document = Documents::Generate::StaffingSpend.new(staffing_spends: @pantry_manager_spends).call(dry_run: true)
        send_file document.url, filename: "#{document.name}.csv"
      end
    end
  end

  def send_staffing_logs
    notification_time = params[:date].present? ? Time.zone.parse(params[:date]) : Time.zone.now
    notification_time += 1.week if notification_time.strftime('%V').to_i % 2 != 1 # always send the last week of the billing cycle
    notifications_sender = ::Admin::Notifications::SendStaffingLogs.new(time: notification_time, type: 'accounts').call
    if notifications_sender.success? && notifications_sender.notified_accounts_team
      head :ok
    else
      render json: { errors: notifications_sender.errors }, status: :unprocessable_entity
    end
  end

private

  def list_params
    params.permit(:page, :limit, :kind, :query)
  end

  def fetch_admin
    @admin = User.where(id: params[:id]).first
  end

  def admin_params
    params.require(:user).permit(:super_admin, :admin, :can_access_suppliers)
  end

  def spend_params
    params.permit(:from_date, :to_date)
  end

end