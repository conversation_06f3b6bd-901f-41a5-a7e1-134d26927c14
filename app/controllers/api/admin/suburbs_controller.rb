class API::Admin::SuburbsController < API::AdminController

  before_action :ensure_yordar_admin
  before_action :fetch_suburb, only: :update
  before_action :ensure_suburb, only: :update

  def index
    lister_options = {
      page: 1,
      limit: 20,
      term: list_params[:query],
      order_by: { country_code: :asc, name: :asc },
    }.merge(list_params.to_h.deep_symbolize_keys)
    @suburbs = Suburbs::List.new(options: lister_options, includes: [:deliverable_suburbs]).call
  end

  def create
    suburb_creator = Suburbs::Upsert.new(suburb_params: suburb_params).call
    if suburb_creator.success?
      render partial: 'api/admin/suburbs/suburb', locals: { suburb: suburb_creator.suburb }
    else
      render json: { errors: suburb_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    suburb_updator = Suburbs::Upsert.new(suburb: @suburb, suburb_params: suburb_params).call
    if suburb_updator.success?
      render partial: 'api/admin/suburbs/suburb', locals: { suburb: suburb_updator.suburb }
    else
      render json: { errors: suburb_updator.errors }, status: :unprocessable_entity
    end
  end

private
  
  def list_params
    params.permit(:page, :limit, :query)
  end

  def fetch_suburb
    @suburb = Suburb.where(id: (params[:id] || params[:suburb_id])).first
  end

  def suburb_params
    params.require(:suburb).permit(:name, :state, :postcode, :latitude, :longitude, :country_code)
  end

  def ensure_suburb
    if @suburb.blank?
      render json: { errors: ['Could not find suburb' ]}, status: :not_found and return
    end
  end

end