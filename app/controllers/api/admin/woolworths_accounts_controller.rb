class API::Admin::WoolworthsAccountsController < API::AdminController

  before_action :authenticate_user!
  before_action :ensure_super_admin
  before_action :fetch_account, only: [:update]

  def index
    @woolworths_accounts = Woolworths::Account.all.order(id: :asc)
  end

  def create
    woolworths_account = Woolworths::Account.new(account_params)
    if woolworths_account.save!
      render partial: 'api/admin/woolworths_accounts/woolworths_account', locals: { woolworths_account: woolworths_account }
    else
      render json: woolworths_account.errors.full_messages, status: :unprocessable_entity
    end
  end

  def update
    if @woolworths_account.present?
      params[:detach_from_order_id].present? ? detach_woolworths_account_from_order : update_woolworths_account
    else
      render json: { errors: 'not_found' }, status: :not_found
    end
  end

private

  def detach_woolworths_account_from_order
    woolworths_order = Woolworths::Order.where(account: @woolworths_account, order_id: params[:detach_from_order_id]).first
    if woolworths_order.update(account_in_use: false)
      render partial: 'api/admin/woolworths_accounts/woolworths_account', locals: { woolworths_account: @woolworths_account }
    else
      render json: woolworths_order.errors.full_messages, status: :unprocessable_entity
    end
  end

  def update_woolworths_account
    if @woolworths_account.update(account_params)
      render partial: 'api/admin/woolworths_accounts/woolworths_account', locals: { woolworths_account: @woolworths_account }
    else
      render json: @woolworths_account.errors.full_messages, status: :unprocessable_entity
    end
  end

  def fetch_account
    @woolworths_account = Woolworths::Account.where(id: params[:id]).first
  end

  def account_params
    params.require(:woolworths_account).permit(:email, :password, :active)
  end

end