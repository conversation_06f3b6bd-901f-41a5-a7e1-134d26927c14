class API::Admin::HolidaysController < API::AdminController

  before_action :ensure_yordar_admin
  before_action :fetch_holiday, only: %i[update destroy]
  before_action :ensure_holiday, only: %i[update destroy]

  def index
    lister_options = {
      page: 1,
      limit: 20,
      order_by: { on_date: :desc },
    }.merge(list_params.to_h.deep_symbolize_keys)
    @holidays = Holidays::List.new(options: lister_options).call
  end

  def create
    holiday_creator = Holidays::Upsert.new(holiday_params: holiday_params).call
    if holiday_creator.success?
      render partial: 'api/admin/holidays/holiday', locals: { holiday: holiday_creator.holiday }
    else
      render json: { errors: holiday_creator.errors }, status: :unprocessable_entity
    end
  end

  def update
    holiday_updator = Holidays::Upsert.new(holiday: @holiday, holiday_params: holiday_params).call
    if holiday_updator.success?
      render partial: 'api/admin/holidays/holiday', locals: { holiday: holiday_updator.holiday }
    else
      render json: { errors: holiday_updator.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    if @holiday.destroy
      head :ok
    else
      render json: { errors: @holiday.errors.full_messages }, status: :unprocessable_entity
    end
  end

private
  
  def list_params
    params.permit(:page, :limit, :query)
  end

  def fetch_holiday
    @holiday = Holiday.where(id: (params[:id] || params[:holiday_id])).first
  end

  def holiday_params
    params.require(:holiday).permit(:name, :description, :image, :color, :state, :on_date, :push_to, :effective_from, :effective_to)
  end

  def ensure_holiday
    if @holiday.blank?
      render json: { errors: ['Could not find holiday' ]}, status: :not_found and return
    end
  end

end