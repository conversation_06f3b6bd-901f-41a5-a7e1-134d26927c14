module OrdersHelper

  def order_display_name(order)
    if is_admin? && order.woolworths_order.present?
      order.woolworths_order_name
    else
      order.name
    end
  end

  def docket_totals(order:, attendee: nil)
    totals = OpenStruct.new
    if order.is_team_order? && attendee.present?
      attendee_totals = Orders::CalculateCustomerTotals.new(order: order, attendee: attendee).call
      totals.subtotal = attendee_totals.subtotal
      totals.delivery = attendee_totals.delivery
      totals.gst = attendee_totals.gst
      totals.total = attendee_totals.total
    else
      totals.subtotal = order.customer_subtotal
      totals.delivery = order.customer_delivery
      totals.gst = order.customer_gst
      totals.topup = order.customer_topup
      totals.surcharge = order.customer_surcharge
      totals.total = order.customer_total
    end
    totals
  end

  def checkout_google_webpack_config(order:)
    if is_admin?
      {
        as_street_address: true,
        set_suburb: 'input[name="order[delivery_suburb_id]"]',
      }
    else
      {
        as_street_address: true,
        restricted_suburb_id: order.delivery_suburb_id,
        restricted_suburb_label: order.delivery_suburb.try(:label),
      }
    end
  end

  def can_edit?(order:)
    case
    when order.is_event_order?
      false
    when is_yordar_admin? && order.status == 'delivered'
      true
    when is_admin? && order.status == 'delivered' && order.invoice_id.blank?
      true
    when %w[delivered saved].include?(order.status)
      false
    when order.woolworths_order.present? && !is_admin?
      false
    else
      true
    end
  end

end
