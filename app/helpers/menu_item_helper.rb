module MenuItemHelper

  DIETARY_PRFERENCES = {
    is_vegetarian: {
      letter: 'V',
      label: 'Vegetarian'
    },
    is_vegan: {
      letter: 'VE',
      label: 'Vegan'
    },
    is_gluten_free: {
      letter: 'GF',
      label: 'Gluten Free'
    },
    is_dairy_free: {
      letter: 'DF',
      label: 'Dairy Free'
    },
    is_individually_packed: {
      letter: 'IP',
      label: 'Individually Packed'
    },
    is_egg_free: {
      letter: 'EF',
      label: 'Egg Free'
    },
    is_halal: {
      letter: 'H',
      label: 'Halal'
    },
    is_kosher: {
      letter: 'K',
      label: 'Kosher'
    },
    is_nut_free: {
      letter: 'NF',
      label: 'Nut Free'
    },
  }.freeze

  def dietary_preferences(menu_item:, for_admin: false)
    preferences = DIETARY_PRFERENCES.dup
    if for_admin == true
      preferences.merge!({
        team_order: {
          letter: 'T',
          label: 'Team Order'
        },
        team_order_only: {
          letter: 'To',
          label: 'Team Order Only'
        },
        is_hidden: {
          letter: 'H',
          label: 'Hidden'
        },
      })
    end
    preferences.select do |field, _|
      menu_item.send(field).present?
    end
  end

  def item_letter_icons(menu_item:, for_admin: false)
    dietary_preferences(menu_item: menu_item, for_admin: for_admin).map do |field, preference|
      content_tag(:span, preference[:letter],
        class: "letter-icon tooltip #{field}",
        title: preference[:label],
        data: { tooltip: true, tooltip_class: 'letter-icon__tooltip' }
      )
    end.join(' ')
  end

  def menu_item_dietary_options
    DIETARY_PRFERENCES.map do |field, info|
      [field, "#{info[:label]}?"]
    end.to_h
  end

  def menu_item_misc_options
    flags = {
      is_hidden: 'Hide this?',
      is_individually_packed: 'Individually Packed?',
      is_gst_free: 'GST free?',
    }
    if is_admin?
      flags = flags.merge({
        team_order: 'Team Order',
        team_order_only: 'Team Order Only',
      })
    end
    flags
  end

  def extras_section_options
    (1..4).to_a.map{|num| "Section #{num}" }
  end

  def pricing_within_budget(budget:, menu_item:, serving_sizes: [], rate_cards: [], markup_override: nil)
    return true if budget.blank? || menu_item.blank?

    min_rate_card_price = rate_cards.map{|card| card.price_inc_gst(gst_country: request_country_code) }.min
    if serving_sizes.present?
      min_serving_price = serving_sizes.map{|serving_size| serving_size.markup_price(gst_country: request_country_code, override: markup_override) || 0 }.min
      min_serving_price = [min_serving_price, min_rate_card_price].min if min_rate_card_price.present?
      min_serving_price <= budget.to_f
    else
      min_item_price = menu_item.markup_price(gst_country: request_country_code, override: markup_override)
      min_item_price = [min_item_price, min_rate_card_price].min if min_rate_card_price.present?
      min_item_price <= budget.to_f
    end
  end

  def item_search_options_for(menu_sections:)
    menu_items = MenuItem.where(menu_section_id: menu_sections.pluck(:id), archived_at: nil)
    menu_items = menu_items.where.not(supplier_profile_id: nil)
    menu_items = menu_items.joins(:menu_section)
    menu_items = menu_items.order('menu_sections.weight asc, menu_items.name')
    grouped_menu_items = menu_items.group_by(&:menu_section)
    grouped_menu_items.map do |section, section_items|
      [
        section.name,
        section_items.map{|item| [item.name, item.id] }
      ]
    end
  end

end
