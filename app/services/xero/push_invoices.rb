# pushes both Customer Invoices and Supplier Invoices
class Xero::PushInvoices

  # Xero API limits
  # Minute Limit: 60 calls in a rolling 60 second window
  # Daily Limit: 5000 calls in a rolling 24 hour window

  GROUP_THRESHOLD = 5
  WAIT_TIMER = 10 # seconds

  def initialize(time: Time.zone.now, retrial: false, verbose: false)
    @time = time
    @retrial = retrial
    @verbose = verbose
    @result = Result.new
  end

  def call
    invoices_to_push.in_groups_of(GROUP_THRESHOLD).each do |grouped_invoices|
      grouped_invoices.each do |invoice|
        push_invoice_to_xero(invoice: invoice)
        sleep(WAIT_TIMER) if !Rails.env.test? # wait after pushing every invoice
      end
      sleep(WAIT_TIMER) if !Rails.env.test? # wait after puching a group of invoices
    end
    result
  end

private

  attr_reader :time, :retrial, :verbose, :result

  def invoices_to_push
    (customer_invoices + supplier_invoices).flatten(1).compact
  end

  def customer_invoices
    invoices = ::Invoice.where(pushed_to_xero: false, status: 'confirmed')
    invoices = invoices.order(created_at: :desc)
    invoices = invoices.joins(:orders)
    invoices = invoices.where.not(orders: { customer_profile: CustomerProfile.joins(:user).where(users: { xero_push_fault: true }) })
    invoices = invoices.where.not(orders: { customer_profile: CustomerProfile.where(id: yordar_credentials(:yordar, :non_invoicing_customer_id)) })
    invoices = invoices.where(orders: { split_order_id: nil })
    invoices = invoices.where('invoices.created_at < ?', time.beginning_of_day) if retrial
    invoices.distinct
  end

  def supplier_invoices
    invoices = ::SupplierInvoice.where(pushed_to_xero: false).order(created_at: :desc)
    invoices = invoices.joins(:orders)
    invoices = invoices.where('supplier_invoices.created_at < ?', time.beginning_of_day) if retrial
    invoices.distinct
  end

  def push_invoice_to_xero(invoice:)
    return if invoice.blank?

    puts "★★★ Pushing invoice #{invoice.number}" if verbose
    if invoice.is_a?(SupplierInvoice)
      invoice_uploader = Xero::API::UploadSupplierInvoice.new(invoice: invoice).call
    else
      invoice_uploader = Xero::API::UploadCustomerInvoice.new(invoice: invoice).call
    end
    if invoice_uploader.success?
      result.pushed_invoices << invoice
      print 'i-' if verbose
    else
      result.errored_invoices << invoice
      result.errors += invoice_uploader.errors
    end
  end

  class Result
    attr_accessor :pushed_invoices, :errored_invoices, :errors

    def initialize
      @pushed_invoices = []
      @errored_invoices = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
