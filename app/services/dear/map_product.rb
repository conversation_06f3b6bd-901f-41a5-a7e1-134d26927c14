class Dear::MapProduct

  DIETARY_FIELDS = {
    'V' => :is_vegan,
    'VEG' => :is_vegetarian,
    'GF' => :is_gluten_free,
    'DF' => :is_dairy_free,
    'EF' => :is_egg_free,
    'H' => :is_halal,
    'K' => :is_kosher,
    'NF' => :is_nut_free,
  }.freeze

  def initialize(api_product:, supplier: nil)
    @api_product = api_product
    @supplier = supplier
    @mapped_product = OpenStruct.new
  end

  def call
    map_identifier
    map_description
    map_pricing
    map_dietary
    map_attachments

    mapped_product
  end

private

  attr_reader :api_product, :supplier, :mapped_product

  def map_identifier
    mapped_product.sku = api_product[:SKU]
  end

  def map_description
    mapped_product.name = api_product[:Name]
    mapped_product.description = api_product[:Description] || api_product[:ShortDescription]
  end

  def map_pricing
    price_tier = (dear_account.present? && dear_account.price_tier.presence) || 'PriceTier1'
    mapped_product.price = api_product[price_tier.to_sym]
  end

  def map_dietary
    dietary_attribute = dear_account.present? && dear_account.dietary_attribute.presence
    dietary_data = (dietary_attribute.present? && api_product[dietary_attribute.to_sym].presence) || ''
    dietaries = dietary_data.split(',').map(&:strip).map(&:upcase)
    DIETARY_FIELDS.each do |key, field|
      mapped_product[field] = dietaries.include?(key)
    end
  end

  def map_attachments
    if api_product[:Attachments].blank?
      mapped_product.attachments = []
    else
      mapped_product.attachments = api_product[:Attachments].map do |attachment|
        OpenStruct.new(
          default: attachment[:IsDefault],
          url: attachment[:DownloadUrl],
          type: attachment[:ContentType],
          name: attachment[:FileName]
        )
      end
    end
  end

  def dear_account
    @_dear_account ||= supplier.present? && supplier.dear_account
  end

end
