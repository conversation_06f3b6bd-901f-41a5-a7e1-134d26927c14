class Notifications::Base

  class NotificationError < StandardError
    def initialize(message = '')
        super(message)
    end
  end

private

  def app_host
    host = Rails.env.production? ? 'https://' : 'http://'
    host += yordar_credentials(:default_host)
    host += ':3000' if Rails.env.development?
    host
  end

  def next_app_host
    return app_host if yordar_credentials(:next_app_subdomain).blank?

    app_host.gsub('app', yordar_credentials(:next_app_subdomain))
  end

  def url_helper
    @_url_helper ||= Rails.application.routes.url_helpers
  end

  def cloudinary_image(image, options: {})
    return nil if image.blank?

    image = image.remove('image/upload/').split('#').first
    image_options = [{ width: 200, height: 200, crop: :fill }, options].inject(&:merge)
    Cloudinary::Utils.cloudinary_url(image, image_options)
  end

  def log_errors(exception: nil, message: '', sentry: false, error_objects: {})
    Rails.logger.error message if message.present?
    if exception.present?
      Rails.logger.error exception.inspect
      Rails.logger.error exception.backtrace.join('\n') if exception.backtrace.present?
    end
    if sentry
      Raven.capture_exception(exception,
        message: message,
        extra: error_objects,
        transaction: self.class.name
      )
    end
  end

  def sanitized_currency_for(value, default: nil)
    value.present? && value > 0 ? number_to_currency(value) : default
  end

  def account_manager_data_for(customer)
    return [] if customer.blank?

    account_managers = Customers::FetchManagers.new(customer: customer, type: 'account_manager').call
    account_managers.map do |account_manager|
      {
        name: account_manager.name,
        email: account_manager.email,
        phone: account_manager.contact_phone,
        avatar_url: cloudinary_image(account_manager.profile&.avatar, options: { width: 300, height: 300 })
      }
    end
  end

  class Result
    attr_accessor :sent_notification, :errors

    def initialize
      @sent_notification = nil
      @errors = []
    end

    def success?
      errors.blank? && sent_notification.present?
    end
  end

end
