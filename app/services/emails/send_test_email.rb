class Emails::SendTestEmail

  def initialize(template:, variation: nil)
    @template = template
    @variation = variation
    @result = Result.new(templates: template_names)
  end

  def call
    return if !Rails.env.development?

    template_names.each do |template_name|
      sent_email = call_method_for(template_name)

      if sent_email.present?
        result.sent_templates << template_name
        result.sent_emails << sent_email
      else
        result.errored_templates << template_name
      end
    end

    puts "sent #{result.sent_templates.size} / #{result.templates.size} emails"

    result
  end

private

  attr_reader :template, :variation, :result

  def template_names
    @_template_names ||= case template
    when 'all'
      puts 'Sending all emails'
      EmailTemplate::VALID_TEMPLATE_NAMES - ['yordar-deprecated_email_template']
    when 'customers'
      puts 'Sending customer emails'
      EmailTemplate::CUSTOMER_EMAIL_TEMPLATES
    when 'suppliers'
      puts 'Sending supplier emails'
      EmailTemplate::SUPPLIER_EMAIL_TEMPLATES
    when 'team-orders'
      puts 'Sending team order emails'
      EmailTemplate::TEAM_ORDER_EMAIL_TEMPLATES
    when 'users'
      puts 'Sending user related emails'
      EmailTemplate::USER_EMAIL_TEMPLATES
    when 'admins'
      puts 'Sending admin emails'
      EmailTemplate::ADMIN_EMAIL_TEMPLATES
    else
      [template]
    end
  end

  def call_method_for(template_name)
    email_method = "send_#{template_name.underscore}_email"
    if respond_to?(email_method, true)
      send(email_method)
    else
      puts "Could not find method for template name - #{template_name} (method name: #{email_method})"
    end
  end

  def customer
    @_customer ||= CustomerProfile.all.sample
  end

  def supplier
    @_supplier ||= SupplierProfile.where(is_searchable: true).all.sample
  end

  def user
    [customer, supplier].sample.user
  end

  def order(status: 'delivered', variant: 'general', type: 'one-off')
    @_order ||= Order.where(status: status, order_variant: variant, order_type: type).last(100).sample
  end

  def invoice
    @_invoice ||= Invoice.all.sample
  end

  def team_order(status: 'delivered')
    @_team_order ||= Order.where(order_variant: 'team_order', status: status).last(100).sample
  end

  def package_team_order(variant: 'team_order')
    @_package_team_order ||= Order.where(order_variant: variant).joins(:team_order_detail).where.not(team_order_details: { budget: nil }).where.not(team_order_details: { package_id: nil }).last(100).sample
  end

  def team_order_attendee(status: %w[invited pending ordered])
    @_team_order_attendee ||= team_order.team_order_attendees.where(status: status).sample
  end

  def order_customer
    @_order_customer ||= order.customer_profile
  end

  def order_supplier
    @_order_supplier ||= order.supplier_profiles.sample
  end

  # Customer Emails
  def send_customer_welcome_email
    puts "send_customer_welcome_email #{customer.name}"
    Customers::Emails::SendWelcomeEmail.new(customer: customer).call
  end

  def send_customer_customer_quote_email
    customer_quote = variation.present? ? CustomerQuote.find(variation) : CustomerQuote.last(200).sample
    puts "send_customer_customer_quote_email #{customer_quote.kind} - #{customer_quote.id}"
    Customers::Emails::SendCustomerQuoteEmail.new(quote: customer_quote).call
  end

  def send_customer_new_order_email
    customer_order = variation.present? ? Order.find(variation) : order
    puts "send_customer_new_order_email #{customer_order.id} - #{customer_order.customer_profile.name}"
    Customers::Emails::SendNewOrderEmail.new(order: customer_order, customer: customer_order.customer_profile).call
  end

  def send_customer_order_confirmed_email
    # comment out return if confirmation_already_sent? || !preferred_notification?
    customer_order = variation.present? ? Order.find(variation) : order
    puts "send_customer_order_confirmed_email #{customer_order.id} - #{customer_order.customer_profile.name}"
    Customers::Emails::SendOrderConfirmationEmail.new(order: customer_order, customer: customer_order.customer_profile).call
  end

  def send_customer_order_quote_email
    quoted_order = order(status: 'quoted')
    quoted_order ||= order
    order_customer = quoted_order.customer_profile
    puts "send_customer_order_quote_email #{quoted_order.id} - #{order_customer.name}"
    quote_emails = variation.present? ? variation : nil
    quote_message = [Faker::Lorem.sentences(number: 5).join(' '), nil].sample
    Customers::Emails::SendOrderQuoteEmail.new(order: quoted_order, customer: order_customer, quote_emails: quote_emails, quote_message: quote_message).call
  end

  def send_customer_event_order_quote_email
    event_order = order(variant: 'event_order')
    puts "send_customer_event_order_quote_email #{event_order.id} - #{event_order.customer_profile.name}"
    quote_emails = 2.times.map{|_| Faker::Internet.email }.join('; ')
    quote_message = [Faker::Lorem.sentences(number: 5).join(' '), Faker::Lorem.sentence, ''].sample
    Customers::Emails::SendEventOrderQuoteEmail.new(order: event_order, customer: event_order.customer_profile, quote_emails: quote_emails, quote_message: quote_message).call
  end

  def send_customer_initial_closure_email
    customer = CustomerProfile.joins(:orders).where.not(orders: { id: nil }).where.not(orders: { name: [nil, ''] }).where.not(orders: { delivery_at: nil }).where(orders: { status: 'delivered' }).sample
    orders = customer.orders.order(:delivery_at).last(5)
    puts "send_customer_initial_closure_email #{customer.id} - #{orders.map(&:id).join(', ')}"
    Customers::Emails::SendInitialClosureEmail.new(customer: customer, orders: orders).call
  end

  def send_customer_final_closure_email
    customer = CustomerProfile.joins(:orders).where.not(orders: { id: nil }).where.not(orders: { name: [nil, ''] }).where.not(orders: { delivery_at: nil }).where(orders: { status: 'delivered', order_type: 'recurrent' }).sample
    return if customer.blank?

    orders = customer.orders.where(order_type: 'recurrent').order(:delivery_at).last(5)
    puts "send_customer_final_closure_email #{customer.name} - #{orders.map(&:id)}"
    Customers::Emails::SendFinalClosureEmail.new(customer: customer, orders: orders).call
  end

  def send_customer_invoice_tax_receipt_email
    invoice = variation.present? ? Invoice.where(number: variation).first : Invoice.where(payment_status: 'paid').sample
    customer = invoice.invoice_orders.first.customer_profile
    puts "send_customer_invoice_tax_receipt_email #{customer.name} - INV##{invoice.id}"
    Customers::Emails::SendInvoiceReceiptEmail.new(customer: customer, invoice: invoice).call
  end

  def send_customer_order_invoice_email
    # comment out return result if !can_send_email? || !preferred_notification?
    customer = invoice.invoice_orders.first.customer_profile
    puts "send_customer_order_invoice_email #{customer.name} - INV##{invoice.id}"
    Customers::Emails::SendOrderInvoiceEmail.new(customer: customer, invoice: invoice, documents: invoice.documents).call
  end

  def send_customer_overdue_invoice_email
    invoices = Invoice.where(pushed_to_xero: true).where('due_at < ?', Time.zone.now).order(due_at: :desc).first(20).sample(7)
    customer = invoices.sample.invoice_orders.first.customer_profile
    puts "send_customer_overdue_invoice_email #{customer.name} - #{invoices.size}"
    kind = variation.present? ? variation : Invoices::Notifications::SendOverdueNotifications::DAY_THRESHOLDS.keys.sample
    Customers::Emails::SendOverdueInvoiceEmail.new(customer: customer, invoices: invoices, kind: kind).call
  end

  def send_customer_order_review_invitation_email
    puts "send_customer_order_review_invitation_email ##{order.id} - #{order_customer.name}"
    Customers::Emails::SendOrderReviewEmail.new(order: order, customer: order_customer).call
  end

  def send_customer_order_summary_email
    # comment email_already_sent? || !preferred_notification?
    confirmed_order = order(status: 'confirmed')
    puts "send_customer_order_summary_email summary day @ #{confirmed_order.delivery_at}"
    Customers::Emails::SendOrdersSummaryEmail.new(customer: confirmed_order.customer_profile, summary_day: confirmed_order.delivery_at, customer_orders: [confirmed_order]).call
  end

  def send_customer_loading_dock_request_email
    loading_dock_order = variation.present? ? Order.find(variation) : order
    order_customer = order.customer_profile
    puts "send_customer_loading_dock_request_email ##{loading_dock_order.id} - #{order_customer.name}"
    Customers::Emails::SendLoadingDockRequestEmail.new(customer: order_customer, orders: [loading_dock_order, loading_dock_order]).call
  end

  def send_customer_meal_plan_reminder_email
    meal_plan = MealPlan.joins(:orders).sample
    delivery_at = meal_plan.orders.where(status: MealPlans::Notifications::SendOrderReminders::CURRENT_ORDER_STATUSES).order(:delivery_at).last.delivery_at
    dates = {
      from_date: delivery_at.beginning_of_week,
      to_date: delivery_at.end_of_week,
    }
    frequency = MealPlan::VALID_REMINDER_FREQUENCIES.sample
    puts "send_customer_meal_plan_reminder_email ##{meal_plan.id} - #{frequency}"
    MealPlans::Emails::SendOrderReminderEmail.new(meal_plan: meal_plan, dates: dates, frequency: frequency).call
  end

  def send_yordar_reminder_email
    reminder = Reminder.all.sample
    puts "send_yordar_reminder_email ##{reminder.id} - #{reminder.remindable&.name}"
    Admin::Emails::SendReminderEmail.new(reminder: reminder).call
  end

  def send_customer_pending_orders_email
    puts "send_customer_pending_orders_email #{order.id} - #{order_customer.name}"
    Customers::Emails::SendPendingOrdersEmail.new(customer: order_customer, pending_orders: [order, order], delivery_on: order.delivery_at).call
  end

  def send_customer_public_holiday_orders_email
    holiday = Holiday.all.sample
    puts "send_customer_public_holiday_orders_email #{order.id} - #{order_customer.name} - #{holiday.name}"
    Customers::Emails::SendPublicHolidayOrdersEmail.new(customer: order_customer, holiday: holiday, handled_orders: [order, order]).call
  end

  def send_customer_company_customer_invitation_email
    invite_params = { first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: Faker::Internet.email }
    puts "send_customer_company_customer_invitation_email #{customer.name}"
    Customers::Emails::SendCustomerInviteEmail.new(customer: customer, invite_params: invite_params).call
  end

  def send_customer_company_customer_invitation_link_email
    # comment return result if email_already_sent?
    puts "send_customer_company_customer_invitation_link_email to #{customer.name}"
    Customers::Emails::SendCustomerInvitationLinkEmail.new(customer: customer).call
  end

  def send_customer_admin_invitation_email
    invite_params = { first_name: Faker::Name.first_name, last_name: Faker::Name.last_name, email: Faker::Internet.email }
    puts "send_customer_admin_invitation_email #{customer.name}"
    Customers::Emails::SendCustomerAdminInviteEmail.new(customer: customer, invite_params: invite_params).call
  end

  def send_customer_company_team_admin_request_email
    puts "send_customer_company_team_admin_request_email #{customer.name}"
    Customers::Emails::SendCompanyTeamAdminRequestEmail.new(customer: customer, message: Faker::Lorem.sentence).call
  end

  def send_customer_company_team_admin_welcome_email
    puts "send_customer_company_team_admin_welcome_email #{customer.name}"
    Customers::Emails::SendCompanyTeamAdminWelcomeEmail.new(customer: customer).call
  end

  # Team Order Emails
  def send_customer_team_admin_request_email
    puts "send_customer_team_admin_request_email #{customer.name}"
    TeamOrders::Emails::SendNewTeamAdminRequestEmail.new(customer: customer).call
  end

  def send_customer_team_admin_welcome_email
    puts "send_customer_team_admin_welcome_email #{customer.name}"
    Customers::Emails::SendTeamAdminWelcomeEmail.new(customer: customer).call
  end

  def send_customer_new_team_order_email
    puts "send_customer_new_team_order_email ##{team_order.id}"
    TeamOrders::Emails::SendAdminNewOrderEmail.new(team_order: team_order).call
  end

  def send_customer_new_team_order_package_email
    puts "send_customer_new_team_order_package_email package_id: #{package_team_order.id}"
    TeamOrders::Emails::SendAdminNewPackageOrderEmail.new(package_orders: [package_team_order, package_team_order, package_team_order]).call
  end

  def send_customer_team_order_submission_email
    order = variation.present? ? Order.find(variation) : team_order
    puts "send_customer_team_order_submission_email ##{order.id}"
    TeamOrders::Emails::SendAdminOrderSubmissionEmail.new(team_order: order).call
  end

  def send_team_order_admin_registration_email
    order = variation.present? ? Order.find(variation) : team_order
    puts "send_team_order_admin_registration_email #{order.id} => #{order.name}"
    TeamOrders::Emails::SendAdminRegistrationEmail.new(team_order: order, team_admin: order.customer_profile).call
  end

  def send_team_order_admin_anonymous_attendees_notification_email
    # need to comment out return if !preferred_notification? || !can_notify?
    # also change anonymous_attendees condition to .where(anonymous: *false*, status..
    puts "send_team_order_admin_anonymous_attendees_notification_email ##{team_order.id}"
    TeamOrders::Emails::SendAdminAnonymousAttendeesEmail.new(team_order: team_order).call
  end

  def send_team_order_admin_cutoff_notification_email
    # need to comment out return if !can_notify? || !preferred_notification?
    puts "send_team_order_admin_cutoff_notification_email ##{team_order.id}"
    cutoff_time = variation.present? ? variation : %w[30m 2hr].sample
    TeamOrders::Emails::SendAdminCutOffEmail.new(team_order: team_order, cutoff_time: cutoff_time).call
  end

  def send_team_order_admin_team_order_extension_email
    recurring_team_order = package_team_order(variant: 'recurring_team_order')
    puts "send_team_order_admin_team_order_extension_email #{recurring_team_order.id} - #{recurring_team_order.delivery_at + 2.weeks}"
    is_final_reminder = variation.present? && variation == 'final' || [true, false].sample
    TeamOrders::Emails::SendAdminRecurringTeamOrderExtensionEmail.new(team_order: recurring_team_order, extension_week: (recurring_team_order.delivery_at + 2.weeks), final_reminder: is_final_reminder).call
  end

  def send_team_order_attendee_cutoff_notification_email
    # comment out return if !can_notify? || !preferred_notification?
    puts "send_team_order_attendee_cutoff_notification_email #{team_order.id} #{team_order_attendee.id}"
    cutoff_time = variation.present? ? variation : %w[24hr 30m 2hr 4hr].sample
    TeamOrderAttendees::Emails::SendCutOffEmail.new(team_order: team_order, team_order_attendee: team_order_attendee, cutoff_time: cutoff_time).call
  end

  def send_team_order_attendee_delivery_notification_email
    # comment out return if !can_notify? || !preferred_notification?
    attendee = team_order_attendee(status: %w[pending ordered])
    puts "send_team_order_attendee_delivery_notification_email #{team_order.id} #{attendee.id}"
    TeamOrderAttendees::Emails::SendDeliveryReminder.new(team_order: team_order, team_order_attendee: attendee).call
  end

  def send_team_order_attendee_invite_email
    # comment out return if team_order.blank? || team_order.team_supplier_profiles.blank? || !preferred_notification?
    puts "send_team_order_attendee_invite_email - #{team_order.id} #{team_order_attendee.id}"
    TeamOrderAttendees::Emails::SendInviteEmail.new(team_order_attendee: team_order_attendee).call
  end

  def send_team_order_attendee_package_invite_email
    # comment out return if package_order.blank? || !preferred_notification?
    puts "send_team_order_attendee_package_invite_email #{team_order_attendee.id}"
    TeamOrderAttendees::Emails::SendPackageInviteEmail.new(package_order_attendee: team_order_attendee, package_orders: [team_order, team_order]).call
  end

  def send_team_order_attendee_order_cancelled_email
    # comment out return if team_order.blank? || !preferred_notification?
    puts "send_team_order_attendee_order_cancelled_email ##{team_order.id} - #{team_order_attendee.id}"
    TeamOrderAttendees::Emails::SendOrderCancelledEmail.new(team_order_attendee: team_order_attendee).call
  end

  def send_team_order_attendee_order_checkout_email
    attendee = team_order_attendee(status: %w[pending ordered])
    puts "send_team_order_attendee_order_checkout_email #{team_order.id} - #{attendee.id}"
    TeamOrderAttendees::Emails::SendCheckoutEmail.new(team_order_attendee: attendee).call
  end

  def send_team_order_attendee_removed_notification_email
    # comment out return if team_order.blank? || !preferred_notification?
    puts "send_team_order_attendee_removed_notification_email #{team_order_attendee.id}"
    TeamOrderAttendees::Emails::SendRemovedFromOrderEmail.new(team_order_attendee: team_order_attendee).call
  end

  # Supplier Emails
  def send_supplier_welcome_email
    puts "send_supplier_welcome_email #{supplier.name}"
    Suppliers::Emails::SendWelcomeEmail.new(supplier: supplier).call
  end

  def send_supplier_contactless_delivery_info_email
    puts "send_supplier_contactless_delivery_info_email #{supplier.name}"
    Suppliers::Emails::SendContactlessDeliveryInfo.new(supplier: supplier).call
  end

  def send_supplier_initial_closure_email
    puts "send_supplier_initial_closure_email #{supplier.name}"
    Suppliers::Emails::SendInitialClosureEmail.new(supplier: supplier).call
  end

  def send_supplier_final_closure_email
    puts "send_supplier_final_closure_email #{supplier.name}"
    Suppliers::Emails::SendFinalClosureEmail.new(supplier: supplier, orders: [order, order]).call
  end

  def send_supplier_new_order_email
    supplier_order = variation.present? ? Order.find(variation) : order(type: %w[one-off recurrent].sample)
    order_supplier = supplier_order.supplier_profiles.first
    puts "send_supplier_new_order_email #{supplier_order.id} - #{order_supplier.name}"
    Suppliers::Emails::SendNewOrderEmail.new(order: supplier_order, supplier: order_supplier).call
  end

  def send_supplier_new_team_order_email
    supplier_order = variation.present? ? Order.find(variation) : team_order
    team_order_supplier = supplier_order.team_supplier_profiles.first
    puts "send_supplier_new_team_order_email #{supplier_order.id} - #{team_order_supplier.name}"
    Suppliers::Emails::SendNewTeamOrderEmail.new(supplier: team_order_supplier, team_order: supplier_order).call
  end

  def send_supplier_order_changed_email
    supplier_order = variation.present? ? Order.find(variation) : order(status: 'amended', type: %w[one-off recurrent].sample)
    order_supplier = supplier_order.supplier_profiles.first
    detail_changes = Orders::GetOrderDetailChanges.new(order: supplier_order).call
    item_changes = Orders::GetOrderItemChanges.new(order: supplier_order, supplier: order_supplier).call

    puts "send_supplier_order_changed_email #{supplier_order.id} - #{order_supplier.name}"
    Suppliers::Emails::SendOrderAmendedEmail.new(supplier: order_supplier, order: supplier_order, detail_changes: detail_changes, item_changes: item_changes).call
  end

  def send_supplier_order_adjusted_email
    supplier_order = variation.present? ? Order.find(variation) : order(status: 'delivered', type: %w[one-off recurrent].sample)
    order_supplier = supplier_order.supplier_profiles.first
    item_changes = Orders::GetOrderItemChanges.new(order: supplier_order, supplier: order_supplier).call

    puts "send_supplier_order_adjusted_email #{supplier_order.id} - #{order_supplier.name}"
    Suppliers::Emails::SendOrderAdjustedEmail.new(supplier: order_supplier, order: supplier_order, item_changes: item_changes).call
  end

  def send_supplier_order_cancelled_email
    puts "send_supplier_order_cancelled_email #{order.id} - #{order_supplier.name}"
    cancellation_mode = variation.present? ? variation : %w[one-off subsequent related].sample
    Suppliers::Emails::SendOrderCancelledEmail.new(supplier: order_supplier, orders: [order, order], mode: cancellation_mode).call
  end

  def send_supplier_order_reactivated_email
    standing_order = order(type: 'recurrent')
    puts "send_supplier_order_reactivated_email #{standing_order.id} - #{order_supplier.name}"
    reactivate_mode = variation.present? ? variation : %w[one-off subsequent].sample
    Suppliers::Emails::SendOrderReactivatedEmail.new(supplier: order_supplier, orders: [standing_order, standing_order], mode: reactivate_mode).call
  end

  def send_supplier_order_skipped_email
    holiday = Holiday.all.sample
    puts "send_supplier_order_skipped_email #{order.id} - #{order_supplier.name} - #{holiday.name}"
    Suppliers::Emails::SendPublicHolidayOrdersEmail.new(supplier: order_supplier, holiday: holiday, handled_orders: [order, order]).call
  end

  def send_supplier_order_summary_email
    # comment out preffered notification
    # add return [] to attachments_data
    summary_order = order(status: 'confirmed')
    supplier = summary_order.supplier_profiles.sample
    order_lines = summary_order.order_lines
    puts "send_supplier_order_summary_email #{supplier.id} - #{summary_order.delivery_at}"
    summary_type = variation.present? ? variation : %w[daily morning].sample
    Suppliers::Emails::SendOrdersSummaryEmail.new(supplier: supplier, order_lines: order_lines, summary_day: summary_order.delivery_at, summary_type: summary_type).call
  end

  def send_supplier_orders_reminder_email
    # add return [] to attachments_data
    reminder_order = order(status: 'confirmed')
    supplier = reminder_order.supplier_profiles.sample
    order_lines = reminder_order.order_lines
    puts "send_supplier_orders_reminder_email #{supplier.id} - #{reminder_order.delivery_at}"
    Suppliers::Emails::SendOrdersReminderEmail.new(supplier: supplier, order_lines: order_lines, summary_day: reminder_order.delivery_at).call
  end

  def send_supplier_recurring_order_reminder_email
    # add return [] to attachments_data
    reminder_order = order(status: 'confirmed')
    supplier = reminder_order.supplier_profiles.sample
    puts "send_supplier_recurring_order_reminder_email #{supplier.id} - #{reminder_order.delivery_at}"
    Suppliers::Emails::SendRecurringOrderReminderEmail.new(supplier: supplier, order: reminder_order).call
  end

  def send_supplier_purchase_order_summary_email
    supplier_invoice = SupplierInvoice.last(200).sample
    supplier = supplier_invoice.supplier_profile
    rgi_document = supplier_invoice.documents.where(kind: 'recipient_generated_invoice').sample
    puts "send_supplier_purchase_order_summary_email - #{supplier.name}"
    Suppliers::Emails::SendPurchaseOrderSummaryEmail.new(supplier: supplier, invoice: supplier_invoice, rgi_document: rgi_document).call
  end

  def send_supplier_review_notification_email
    puts "send_supplier_review_notification_email #{order_supplier.name}"
    Suppliers::Emails::SendOrderReviewsEmail.new(supplier: order_supplier, time: order.delivery_at, order_reviews: [order, order]).call
  end

  def send_supplier_loading_dock_email
    loading_dock_order = variation.present? ? Order.find(variation) : order
    order_supplier = loading_dock_order.supplier_profiles.sample
    puts "send_supplier_loading_dock_email #{order_supplier.name}"
    Suppliers::Emails::SendLoadingDockEmail.new(order: loading_dock_order, supplier: order_supplier).call
  end

  def send_supplier_team_order_created_email
    team_order_supplier = team_order.team_supplier_profiles.last
    puts "send_supplier_team_order_created_email #{team_order.id} - #{team_order_supplier.name}"
    Suppliers::Emails::SendTeamOrderHeadsUpEmail.new(team_order: team_order, supplier: team_order_supplier).call
  end

  def send_supplier_team_order_cutoff_email
    team_order_supplier = team_order.team_supplier_profiles.last
    cutoff_time = variation.present? ? variation : %w[4hr day].sample
    puts "send_supplier_team_order_cutoff_email #{team_order.id} #{cutoff_time} - #{team_order_supplier.name} "
    Suppliers::Emails::SendTeamOrderCutoffEmail.new(team_order: team_order, supplier: team_order_supplier, cutoff_time: cutoff_time).call
  end

  # Users

  def send_user_confirm_account_email
    puts "send_user_confirm_account_email #{user.email}"
    Users::Emails::SendAccountConfirmationEmail.new(user: user).call
  end

  def send_user_lost_password_email
    puts "send_user_lost_password_email #{user.email}"
    Users::Emails::SendResetPasswordEmail.new(user: user, token: 'reset-token').call
  end

  # Admin emails
  def send_admin_customer_quote_email
    customer_quote = variation.present? ? CustomerQuote.find(variation) : CustomerQuote.last(200).sample
    puts "send_admin_customer_quote_email #{customer_quote.kind} - #{customer_quote.id}"
    Admin::Emails::SendCustomerQuoteEmail.new(quote: customer_quote).call
  end

  def send_admin_rejected_order_email
    puts "send_admin_rejected_order_email #{order.id} - #{order_supplier.name}"
    Orders::Emails::SendOrderRejectedAdminEmail.new(order: order, supplier: order_supplier).call
  end

  def send_admin_rejected_order_quote_email
    quoted_order = order(status: 'quoted')
    quoted_order ||= order
    order_customer = quoted_order.customer_profile
    puts "send_admin_rejected_order_quote_email #{quoted_order.id} - #{order_customer.name}"
    Admin::Emails::SendRejectedOrderQuoteEmail.new(order: quoted_order).call
  end

  def send_admin_order_charge_failed_email
    failed_order = variation.present? ? Order.where(id: variation).first : Order.where.not(credit_card_id: 1).where.not(customer_profile_id: nil).last(100).sample
    card_error = OpenStruct.new(
      message: 'Your card was declined.',
      code: 'card_declined',
      decline_code: %w[fraudulent insufficient_funds].sample
    )
    puts "send_admin_order_charge_failed_email #{failed_order.id} - #{failed_order.customer_profile.name}"
    Orders::Emails::SendOrderChargeFailedEmail.new(order: failed_order, card_error: card_error).call
  end

  def send_admin_woolworths_checkout_failed_email
    failed_order = variation.present? ? Order.where(id: variation).first : Order.where(status: 'delivered').joins(:woolworths_order).where.not(woolworths_orders: { delivery_window_text: [nil, ''] }).last(100).sample
    puts "send_admin_woolworths_checkout_failed_email #{failed_order.id} - #{failed_order.customer_profile.name}"
    Admin::Emails::SendFailedWoolworthsCheckoutEmail.new(order: failed_order).call
  end

  def send_yordar_failed_stripe_payment_email
    mock_stripe_event = OpenStruct.new(
      id: 'evt_test_webhook',
      type: 'charge.failed',
      data: OpenStruct.new(
        object: OpenStruct.new(
          id: rand(1_231_231_231_231),
          currency: 'aud',
          amount: 10_000,
          description: Faker::Lorem.sentence(word_count: 5),
          failure_code: 411,
          failure_message: Faker::Lorem.sentence(word_count: 5)
        )
      )
    )
    puts "send_yordar_failed_stripe_payment_email - #{mock_stripe_event.data.object.id}"
    Admin::Emails::SendFailedStripePaymentEmail.new(event: mock_stripe_event).call
  end

  def send_yordar_managed_order_email
    managed_order = variation.present? ? Order.find(variation) : order
    puts "send_yordar_managed_order_email - Order ##{managed_order.id}"
    Admin::Emails::SendManagedOrderEmail.new(order: managed_order, account_manager: managed_order.customer_profile).call
  end

  def send_yordar_bank_details_changed_email
    puts "send_yordar_bank_details_changed_email - Supplier #{supplier.id}"
    previous_bank_details = {
      bsb_number: "#{supplier.bsb_number}-OLD",
      bank_account_number: "#{supplier.bank_account_number}-OLD"
    }
    Admin::Emails::SendBankDetailChangedEmail.new(supplier: supplier, previous_details: previous_bank_details).call
  end

  def send_yordar_failed_xero_invoices_email
    invoices = Invoice.where(id: Invoice.all.select(:id).sample(2))
    puts "send_yordar_failed_xero_invoices_email - Invoice ##{invoices.map(&:id)}"
    Admin::Emails::SendFailedXeroInvoicesEmail.new(invoices: invoices).call
  end

  def send_yordar_new_supplier_registration_email
    puts "send_yordar_new_supplier_registration_email - #{supplier.name}"
    category_group = %w[catering-services kitchen-supplies].sample
    registration = SupplierRegistration.new({
      category_group: category_group,
      is_team_supplier: [true, false].sample
    })
    Admin::Emails::SendSupplierRegistrationEmail.new(supplier: supplier, registration: registration).call
  end

  def send_yordar_order_review_summary_email
    # comment out return if email_already_sent? || recent_order_reviews.blank?
    recent_reviews = OrderReview.all.sample(2)
    puts "send_yordar_order_review_summary_email - #{recent_reviews.first.created_at}"
    Admin::Emails::SendOrderReviewsSummaryEmail.new(time: recent_reviews.first.created_at, reviews: recent_reviews).call
  end

  def send_yordar_pending_orders_email
    recent_orders = Order.where(status: %w[delivered quoted], order_variant: %w[general event_order]).last(200).sample(2)
    pending_orders = Order.where(id: recent_orders.select(&:id))
    puts "send_yordar_pending_orders_email #{pending_orders.map(&:id)}"
    Admin::Emails::SendPendingOrdersEmail.new(pending_orders: pending_orders, delivery_on: order.delivery_at).call
  end

  def send_yordar_rake_task_errors_email
    error = {
        task: 'Task Name',
        error_list: ['error message 1', 'error message 2']
      }
    errors = [error, error]
    puts 'send_yordar_rake_task_errors_email'
    Admin::Emails::SendRakeTaskErrorsEmail.new(errors: errors).call
  end

  def send_yordar_rate_card_amended_email
    rate_card = RateCard.last(300).sample
    updated_orders = 2.times.map{|_| MenuItems::UpdateFutureOrderLines::OrderUpdate.new(order.id, order.name, order.customer_profile.name, ActionController::Base.helpers.number_to_currency(rand(101..200)), ActionController::Base.helpers.number_to_currency(rand(201..300))) }
    puts "send_yordar_rate_card_amended_email - #{rate_card.id}"
    Admin::Emails::SendRateCardAmendedEmail.new(rate_card: rate_card, updated_orders: updated_orders).call
  end

  def send_yordar_menu_item_amended_email
    menu_item = MenuItem.last(300).sample
    serving_size = menu_item.serving_sizes.first
    updated_orders = 2.times.map{|_| MenuItems::UpdateFutureOrderLines::OrderUpdate.new(order.id, order.name, order.customer_profile.name, ActionController::Base.helpers.number_to_currency(rand(101..200)), ActionController::Base.helpers.number_to_currency(rand(201..300))) }
    puts "send_yordar_menu_item_amended_email - #{menu_item.id}"
    Admin::Emails::SendMenuItemAmendedEmail.new(menu_item: menu_item, serving_size: serving_size, updated_orders: updated_orders).call
  end

  def send_yordar_supplier_menu_export_email
    mock_csv_data = CSV.generate({}) do |csv|
      csv << %w[header1 header2]
      csv << %w[row11 row12]
      csv << %w[row21 row22]
    end
    puts "send_yordar_supplier_menu_export_email - #{supplier.name}"
    Suppliers::Emails::SendExportedMenuCsv.new(supplier: supplier, csv_data: mock_csv_data).call
  end

  def send_yordar_order_confirmation_check_email
    orders = Order.where(status: 'confirmed', order_type: 'one-off').last(200).sample(2)
    confirmed_orders = Order.where(id: orders.select(&:id))
    puts 'send_yordar_order_confirmation_check_email'
    Orders::Emails::SendConfirmationCheckEmail.new(auto_confirmed_orders: confirmed_orders).call
  end

  def send_yordar_staffing_schedule_email
    week_options = {
      from_date: Time.zone.now.beginning_of_week,
      to_date: Time.zone.now.end_of_week,
      active_orders_only: true
    }
    staffing_spends = Admin::Reports::FetchPantryManagerSpends.new(options: week_options).call
    if variation.present?
      staffing_spend = staffing_spends.detect{|spend| spend.manager.name == variation }
    else
      staffing_spend = staffing_spends.select{|spend| spend.manager.present? && spend.orders.present? }.sample
    end
    puts "send_yordar_staffing_schedule_email - #{staffing_spend.manager.name}"
    Admin::Emails::SendStaffingScheduleEmail.new(pantry_manager: staffing_spend.manager, orders: staffing_spend.orders, week: week_options).call
  end

  def send_yordar_staffing_log_email
    fortnight_options = {
      from_date: (Time.zone.now - 1.week).beginning_of_week,
      to_date: Time.zone.now.end_of_week,
      active_orders_only: true
    }
    staffing_spends = Admin::Reports::FetchPantryManagerSpends.new(options: fortnight_options).call
    if variation.present?
      staffing_spend = staffing_spends.detect{|spend| spend.manager.name == variation }
    else
      staffing_spend = staffing_spends.select{|spend| spend.manager.present? && spend.orders.present? }.sample
    end
    puts "send_yordar_staffing_log_email - #{staffing_spend.manager.name}"
    Admin::Emails::SendStaffingLogEmail.new(pantry_manager: staffing_spend.manager, orders: staffing_spend.orders, fortnight: fortnight_options).call
  end

  def send_yordar_staffing_log_accounts_email
    fortnight_options = {
      from_date: (Time.zone.now - 1.week).beginning_of_week,
      to_date: Time.zone.now.end_of_week,
      active_orders_only: true
    }
    staffing_spends = Admin::Reports::FetchPantryManagerSpends.new(options: fortnight_options).call
    staffing_spends = staffing_spends.select do |spend|
      spend.manager.present? && spend.orders.present?
    end
    puts 'send_yordar_staffing_log_accounts_email'
    Admin::Emails::SendStaffingLogAccountsEmail.new(staffing_spends: staffing_spends, fortnight: fortnight_options).call
  end

  class Result
    attr_accessor :sent_emails, :sent_templates, :errored_templates
    attr_reader :templates

    def initialize(templates:)
      @templates = templates
      @sent_emails = []
      @sent_templates = []
      @errored_templates = []
    end

    def success?
      errored_templates.blank? && sent_templates.size == templates.size
    end

  end

end
