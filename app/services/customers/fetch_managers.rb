class Customers::FetchManagers

  def initialize(customer:, type:)
    @customer = customer
    @type = type || 'account_manager'
  end

  def call
    (managers_for(adminable_customer: customer) +
      customer_admins.map do |customer_admin|
        managers_for(adminable_customer: customer_admin)
      end.flatten).uniq
  end

private

  attr_reader :customer, :type

  def managers_for(adminable_customer:)
    adminable_customer.access_permissions_as_customer.where(scope: type, active: true).includes(:admin).map(&:admin)
  end

  def customer_admins
    customer.access_permissions_as_customer.where(scope: %w[company_team_admin pantry_manager], active: true).map(&:admin)
  end
end