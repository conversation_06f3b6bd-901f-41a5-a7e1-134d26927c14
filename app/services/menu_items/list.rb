class MenuItems::List

  QUERY_MAPS = {
    'hotdog' => ['hotdog', 'hot dog', 'hut dog'],
    'hot dog' => ['hotdog', 'hot dog', 'hut dog']
  }.freeze

  def initialize(options: {}, includes: [])
    @options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
    @includes = includes
  end

  def call
    @menu_items = base_menu_items
    filter_by_menu_sections if menu_sections.present?
    filter_out_custom_menu_sections if options[:ignore_custom_menu_sections].present?
    filter_for_home_deliveries
    filter_by_suppliers if suppliers.present? && !needs_availability_check?
    filter_by_visiblity if options[:show_visible].present?
    filter_by_active if options[:show_active].present?
    filter_by_order_type if options[:order_type].present?
    filter_by_query if options[:query].present?
    filter_by_stock_quantity if options[:show_in_stock].present?
    filter_by_woolworths_availability if needs_availability_check?
    filter_by_favourites if options[:show_favourites].present?
    filter_by_meal_plan_categories if options[:mealUUID].present?
    filter_by_customer_visible_menu_sections
    order_items if options[:order_by].present?
    if options[:page].present?
      paginate_items
    elsif options[:limit].present?
      limit_items
    end

    @menu_items
  end

private

  attr_reader :includes
  attr_accessor :options, :menu_items

  def filter_by_menu_sections
    @menu_items = menu_items.where(menu_section: menu_sections).where(supplier_profile_id: menu_sections.map(&:supplier_profile_id).uniq)
  end

  def filter_out_custom_menu_sections
    @menu_items = menu_items.joins(:menu_section).merge(MenuSection.where.not('menu_sections.name ilike ?', 'custom'))
  end

  def filter_for_home_deliveries
    menu_section_condition = case
    when options[:is_home_delivery].present?
      MenuSection.joins(:categories).where(categories: { group: 'home-deliveries' })
    else
      MenuSection.includes(:categories).references(:categories).where('categories.id is NULL OR categories.group != ?', 'home-deliveries')
    end
    @menu_items = menu_items.joins(:menu_section).merge(menu_section_condition)
  end

  def filter_by_suppliers
    @menu_items = menu_items.where(supplier_profile: suppliers)
  end

  def filter_by_visiblity
    @menu_items = menu_items.where(is_hidden: false).joins(:menu_section).where(menu_sections: { is_hidden: false })
  end

  def filter_by_active
    @menu_items = menu_items.where(archived_at: nil).joins(:menu_section).where(menu_sections: { archived_at: nil })
  end

  def filter_by_order_type
    @menu_items = case options[:order_type]
    when 'normal'
      menu_items.where(team_order_only: [false, nil])
    when 'team_order'
      menu_items.where('team_order_only = :truthy OR team_order = :truthy', truthy: 'true')
    end
  end

  def filter_by_query
    case
    when options[:thorough_search]
      @menu_items = menu_items.search_by_keywords(options[:query])
    when (map_key = QUERY_MAPS.keys.detect{|query| options[:query].downcase.include?(query) }.presence)
      @menu_items = menu_items.left_outer_joins(:active_serving_sizes).where('menu_items.name ~* :query OR serving_sizes.name ~* :query', query: QUERY_MAPS[map_key].join('|'))
    else
      item_arel = MenuItem.arel_table
    serving_arel = ServingSize.arel_table
      item_name = item_arel[:name].matches("%#{options[:query]}%")
      item_description = item_arel[:description].matches("%#{options[:query]}%")
      serving_name = serving_arel[:name].matches("%#{options[:query]}%")
      @menu_items = menu_items.left_outer_joins(:active_serving_sizes).where(item_name.or(item_description).or(serving_name))
    end
  end

  def needs_availability_check?
    false
    # options[:suburb].present? && suppliers.present? && suppliers.detect(&:woolworths?).present?
  end

  def filter_by_woolworths_availability
    woolworths_store_id = Rails.configuration.woolworths.fulfilment_stores.mapped_stores.detect do |_, details|
      details[:state] == options[:suburb].state
    end&.first
    if suppliers.size == 1
      @menu_items = menu_items.joins(:woolworths_store_availabilities).where(woolworths_store_availabilities: { store_id: woolworths_store_id })
    else
      item_arel = MenuItem.arel_table
      availability_arel = Woolworths::StoreAvailability.arel_table
      availaibility_condition = availability_arel[:store_id].eq(woolworths_store_id)
      woolworths_condition = item_arel[:supplier_profile_id].eq(yordar_credentials(:woolworths, :supplier_profile_id))
      non_woolworths_condition = item_arel[:supplier_profile_id].in(suppliers.map(&:id) - [yordar_credentials(:woolworths, :supplier_profile_id)])
      @menu_items = menu_items.preload(:woolworths_store_availabilities).includes(:woolworths_store_availabilities).where(non_woolworths_condition.or(woolworths_condition.and(availaibility_condition)))
    end
  end

  def filter_by_stock_quantity
    arel = MenuItem.arel_table
    non_zero_condition = arel[:stock_quantity].not_eq(0)
    nil_condition = arel[:stock_quantity].eq(nil)
    @menu_items = menu_items.where(non_zero_condition.or(nil_condition))
  end

  def filter_by_favourites
    if profile_is_customer?
      @menu_items = menu_items.joins(:customer_profile_menu_items).merge(CustomerProfileMenuItem.where(customer_profile_id: options[:profile])).order('customer_profile_menu_items.created_at DESC')
    else
      @menu_items = menu_items.none
    end
  end

  def filter_by_meal_plan_categories
    @menu_items = menu_items.joins(:menu_section).merge(MenuSection.where(archived_at: nil).includes(:categories).where(categories: { slug: Category::MEAL_PLAN_CATEGORY_SLUGS }))
  end

  def filter_by_customer_visible_menu_sections
    case
    when options[:for_cache].present? || options[:is_admin].present?
     # do not filter by visibility as admins can see everything
    when profile_is_customer?
      @menu_items = menu_items.joins(:menu_section).merge(MenuSection.includes(:companies).where(companies: { id: [nil, options[:profile].try(:company_id)] }))
    else
      @menu_items = menu_items.joins(:menu_section).merge(MenuSection.includes(:companies).where(companies: { id: nil }))
    end
  end

  def base_menu_items
    MenuItem.all.includes(includes)
  end

  def menu_sections
    sections = options[:menu_sections].presence
    sections ||= MenuSection.where(id: options[:menu_section].id) if options[:menu_section].present?
    sections ||= options[:supplier].menu_sections if options[:supplier].present?
    sections
  end

  def profile_is_customer?
    options[:profile].present? && options[:profile].profile.present? && options[:profile].profile.is_customer?
  end

  def order_items
    @menu_items = menu_items.order(options[:order_by])
  end

  def paginate_items
    page = options[:page] || 1
    limit = options[:limit] || 20
    @menu_items = menu_items.page(page).per(limit)
  end

  def limit_items
    @menu_items = menu_items.limit(options[:limit])
  end

  def suppliers
    (options[:suppliers].presence || [options[:supplier]]).compact
  end

  def default_options
    {
      menu_sections: [],
      menu_section: nil,
      ignore_custom_menu_sections: false,
      is_home_delivery: false,
      supplier: nil,
      suppliers: [],
      profile: nil,
      is_admin: false,
      for_cache: false,
      mealUUID: nil,
      suburb: nil,
      show_visible: false,
      show_active: false,
      show_in_stock: false,
      show_favourites: false,
      thorough_search: false,
      order_type: nil,
      query: nil,
      order_by: nil,
      page: nil,
      limit: nil,
    }
  end

end
