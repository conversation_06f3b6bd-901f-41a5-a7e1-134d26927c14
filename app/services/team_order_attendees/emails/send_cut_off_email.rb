class TeamOrderAttendees::Emails::SendCutOffEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-attendee-cutoff_notification'.freeze
  REMAINING_TIME_LABEL = {
    '24hr' => 'approx 24 hours',
    '4hr' => '4 hours',
    '2hr' => '2 hours',
    '30m' =>  '30 minutes'
  }.freeze

  def initialize(team_order:, team_order_attendee:, cutoff_time:)
    @team_order = team_order
    @team_order_attendee = team_order_attendee
    @cutoff_time = cutoff_time
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
    @notification_variation = team_order.is_recurring_team_order? ? "#{cutoff_time}-recurring" : cutoff_time
  end

  def call
    return if !can_notify? || !preferred_notification?

    begin
      send_email
    rescue => exception
      Rails.logger.error "Failed to send team order #{cutoff_time} cutoff reminder to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      Rails.logger.error exception.inspect
      Rails.logger.error exception.backtrace.join('\n')
    end
  end

private

  attr_reader :team_order, :team_order_attendee, :cutoff_time

  def can_notify?
    team_order.present? && team_order_attendee.respond_to?(notification_attribute) && team_order_attendee.send(notification_attribute).blank?
  end

  def team_admin
    @_team_admin ||= team_order.present? ? team_order.customer_profile : nil
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: team_order_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      mark_attendee_notified
      Rails.logger.info "Team order #{cutoff_time} cutoff reminder sent to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    "Yordar: #{remaining_time} remaining until you can no longer order"
  end

  def email_options
    {
      fk_id: team_order_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      team_order: deep_struct(team_order_data),
      attendee: deep_struct(attendee_data),

      cutoff_time: cutoff_time,
      remaining_time: remaining_time,

      header_color: :purple
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      supplier: supplier_data,
      delivery_at: team_order.delivery_at.to_s(:full),
      delivery_day: team_order.delivery_at.to_s(:date_verbose),
      delivery_address: team_order.delivery_address_arr.join(',<br/>'),
      budget: number_to_currency(team_order.team_order_budget, precision: 2),
      hide_budget: team_order.hide_budget
    }
  end

  def supplier_data
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def attendee_data
    {
      name: team_order_attendee.first_name,
      status: team_order_attendee.status,
      order_url: url_helper.next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code, host: next_app_host, tld_length: 2),
      unsubscribe_url: url_helper.team_order_attendee_unsubscribe_url(code: team_order_attendee.uniq_code, confirm: 'true', host: app_host)
    }
  end

  def remaining_time
    REMAINING_TIME_LABEL[cutoff_time]
  end

  def notification_attribute
    "cutoff_#{cutoff_time}_reminder"
  end

  def mark_attendee_notified
    team_order_attendee.update(Hash[notification_attribute.to_sym, Time.zone.now])
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order_attendee.id}-#{cutoff_time}"
  end

end


