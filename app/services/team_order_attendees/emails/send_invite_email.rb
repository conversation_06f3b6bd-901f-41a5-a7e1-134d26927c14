class TeamOrderAttendees::Emails::SendInviteEmail < Notifications::WithPreference
  include ActionView::Helpers::NumberHelper

  EMAIL_TEMPLATE = 'team-order-attendee-invite'.freeze

  def initialize(team_order_attendee:)
    @team_order_attendee = team_order_attendee
    @notifying_account = team_admin
    @template_name = EMAIL_TEMPLATE
  end

  def call
    return if team_order.blank? || team_order.team_supplier_profiles.blank? || !preferred_notification?

    begin
      send_email
    rescue => exception
      error_message = "Failed to send team order ##{team_order.id} invite email to attendee - #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      log_errors(exception: exception, message: error_message, sentry: false)
    end
  end

private

  attr_reader :team_order_attendee

  def team_order
    @team_order ||= team_order_attendee.order.reload
  end

  def team_admin
    @_team_admin ||= team_order.present? ? team_order.customer_profile : nil
  end

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: E<PERSON>IL_TEMPLATE,
      recipient: team_order_attendee.email,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Sent team order ##{team_order.id} invite email to attendee #{team_order_attendee.uniq_code}, #{team_order_attendee.name}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_subject
    'Yordar: You\'ve been invited to a team meeting order'
  end

  def email_options
    {
      fk_id: team_order_attendee.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      attendee: deep_struct(attendee_data),
      team_order: deep_struct(team_order_data),
      header_color: :purple
    }
  end

  def team_order_data
    {
      id: team_order.id,
      name: team_order.name,
      delivery_at: team_order.delivery_at.in_time_zone.to_s(:full_verbose),
      delivery_address: team_order.delivery_address_arr.join(',<br/>'),
      budget: number_to_currency(team_order.team_order_budget, precision: 2),
      hide_budget: team_order.hide_budget,
      supplier: supplier_info_for(team_order),
    }
  end

  def supplier_info_for(team_order)
    supplier = team_order.team_supplier_profiles.first
    {
      name: supplier.name,
      image: cloudinary_image(supplier.profile.avatar)
    }
  end

  def attendee_data
    {
      name: team_order_attendee.first_name,
      order_url: url_helper.next_app_team_order_attendee_order_url(code: team_order_attendee.uniq_code, host: next_app_host, tld_length: 2),
      unsubscribe_url: url_helper.team_order_attendee_unsubscribe_url(code: team_order_attendee.uniq_code, confirm: 'true', host: app_host),
    }
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{team_order.id}-#{team_order_attendee.id}"
  end

end
