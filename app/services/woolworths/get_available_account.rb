class Woolworths::GetAvailableAccount

  def call
    available_account = disconnected_accounts.first
    available_account = not_in_use_accounts.first if available_account.blank?
    available_account
  end

private

  def disconnected_accounts
    Woolworths::Account.where(active: true).includes(:woolworths_orders).where(woolworths_orders: { id: nil }).order('woolworths_accounts.id ASC')
  end

  def not_in_use_accounts
    Woolworths::Account.where(active: true).where.not(id: Woolworths::Order.where(account_in_use: true).pluck(:account_id)).order('woolworths_accounts.id ASC')
  end

end
