class Orders::Adjust

  def initialize(order:, order_params:, customer: nil, is_admin: false)
    @order = order
    @order_params = order_params
    @customer = order&.customer_profile || customer
    @is_admin = is_admin
    @result = Result.new(order: order)
  end

  def call
    if can_adjust? && has_changes?
      result.has_changes = true
      order_updater = Orders::Update.new(order: order, order_params: sanitized_params, profile: customer).call
      if order_updater.success?
        regenerate_order_documents
        notify_suppliers
      else
        result.errors += order_updater.errors
      end
    end
    result
  end

private

  attr_reader :order, :order_params, :mode, :customer, :is_admin, :result

  def can_adjust?
    case
    when order.status != 'delivered'
      result.errors << 'Can only adjust a delivered order'
    end
    result.errors.blank?
  end

  def regenerate_order_documents
    Documents::Generate::CustomerOrderDetails.new(order: order).delay(queue: :data_integrity).call
  end

  def notify_suppliers
    Orders::Notifications::SendOrderAdjustedSupplierNotifications.new(order: order).delay(queue: :notifications).call
  end

  def sanitized_params
    @_sanitized_params ||= [
      default_adjustment_params,
      purchase_order_params,
      order_params.to_h.symbolize_keys.except(:cpo_id, :po_number)
    ].inject(&:merge)
  end

  def default_adjustment_params
    {
      status: 'delivered',
      update_with_invoice: order.invoice_id.present? && is_admin
    }
  end

  def purchase_order_params
    {
      customer_purchase_order: Customers::FetchPurchaseOrder.new(customer: customer, cpo_id: order_params[:cpo_id]).call,
    }
  end

  def has_changes?
    order.assign_attributes(sanitized_params.except(:status))
    order_lines_amended = order.order_lines.where(status: 'amended').present?
    order.changed? || woolworths_order_changed? || order_lines_amended
  end

  def woolworths_order_changed?
    return false if sanitized_params[:associated_woolworths_order_attributes].blank?
    return false if order.woolworths_order.blank?

    woolworths_order = order.woolworths_order
    woolworths_order.assign_attributes(sanitized_params[:associated_woolworths_order_attributes])
    woolworths_order.changed?
  end

  class Result
    attr_accessor :order, :has_changes, :errors

    def initialize(order:)
      @order = order
      @has_changes = false
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
