class Orders::CheckEditAccess

  def initialize(order:, current_user:, profile: nil)
    @order = order
    @current_user = current_user
    @profile = profile || current_user&.profile&.profileable
    @result = Result.new(order: order)
  end

  def call
    if can_edit_order? && is_within_lead_time?
      result.can_edit = true
    end

    result
  end

private

  attr_reader :order, :current_user, :profile, :result

  def can_edit_order?
    case
    when order.blank? || order_customer.blank? # cannot edit missing order
      result.errors << 'Cannot edit a missing order'
    when current_user.blank? # cannot edit missing customers
      result.errors << 'Cannot edit order without being logged in as a valid Customer'
    when %w[quoted new amended confirmed pending delivered cancelled skipped paused].exclude?(order.status) # cannot edit a non submitted order
      result.errors << 'The order is not saved yet!'
    when !has_customer_access? # cannot edit customers you do not have access to
      result.errors << 'You do not have access to the orders of this customer!'
    when is_order_admin_user? && order.status == 'delivered'
      # do nothing - allow admins to edit non-invoiced orders
    when order.status == 'delivered' # do not allow delivered orders to be edited
      result.errors << 'Order is already marked as delivered'
    when order.is_event_order? # do not allow custom orders to be edited
      result.errors << 'Custom Orders cannot be edited'
    when order.woolworths_order.present? && !is_order_admin_user? # non-admins cannot edit Woolworths order
      result.errors = 'Woolworths Orders cannot be edited! Please contact Yordar Admin.'
    end

    result.errors.blank?
  end

  def is_within_lead_time?
    return true if is_order_admin_user?

    lead_time_fetcher = Orders::GetMaximumLeadTime.new(order: order).call

    if !lead_time_fetcher.can_process?
      result.errors << 'Unfortunately this order has passed it\'s lead time. Please contact support if this is an issue.'
    end
    lead_time_fetcher.can_process?
  end

  def has_customer_access?
    return true if current_customer == order_customer

    lister_options = {
      customer: order_customer,
      for_user: current_user,
      limit: nil
    }
    admin_customers = Admin::ListCustomers.new(options: lister_options).call
    admin_customers.present?
  end

  def order_customer
    @_order_customer ||= order.customer_profile
  end

  def current_customer
    return nil if profile.blank? || !profile.is_a?(CustomerProfile)

    profile
  end

  def is_order_admin_user?
    return false if current_user.blank?

    current_user.admin? || current_user.super_admin? || current_user.allow_all_customer_access?
  end

  class Result
    attr_reader :order
    attr_accessor :can_edit, :errors

    def initialize(order:)
      @order = order
      @can_edit = false
      @errors = []
    end

    def success?
      errors.blank? && can_edit
    end
  end
  
end