class Suppliers::List

  MAXIMUM_LEAD_TIME = 24

  def initialize(options: {}, includes: [])
    @includes = includes
    @options = [default_options, options.symbolize_keys].inject(&:merge)
    @suppliers = []
  end

  def call
    @suppliers = base
    filter_searchable if options[:searchable].present?
    refresh_suburb_cache if options[:refresh].present?

    case
    when options[:suburb].present?
      filter_by_suburb
    when options[:for_react] # do not list any supplier without a suburb for react app
      @suppliers = SupplierProfile.none
    end

    filter_by_category_group_name if options[:category_group].present?
    filter_by_category_slugs if options[:category].present?
    filter_by_dietary_options if options[:dietary].present?
    filter_by_delivery_options if options[:delivery].present?
    filter_by_minimum_spend if options[:other].present? && options[:other].include?('min_order')
    filter_by_maximum_lead_time if options[:other].present? && options[:other].include?('lead_mode')
    filter_by_team_suppliers if options[:team_suppliers].present?
    filter_by_meal_plan_suppliers if options[:mealUUID].present?
    filter_by_supplier_flags if options[:other].present?

    filter_visibility
    filter_admin_only if options[:is_admin].blank?
    filter_by_keywords if options[:search_keywords].present?
    filter_by_closure_dates if options[:order_date].present?
    offset_suppliers if options[:offset].present?
    limit_suppliers if options[:limit].present?

    @suppliers
  end

private

  attr_reader :options, :includes
  attr_accessor :suppliers

  def base
    SupplierProfile.all.includes(includes)
  end

  def filter_searchable
    @suppliers = suppliers.where(is_searchable: true)
  end

  def filter_by_suburb
    @suppliers = Suppliers::ListForDeliverableSuburb.new(suburb: options[:suburb], order_date: options[:order_date], scoped_suppliers: suppliers).call
  end

  def filter_by_category_group_name
    category_group = case options[:category_group]
    when 'office-catering'
      'catering-services'
    when 'office-snacks'
      'kitchen-supplies'
    else
      options[:category_group]
    end
    if Suppliers::Cache::CategoryGroups::CACHEABLE_CATEGORY_GROUPS.include?(category_group)
      possible_category_option = { "has_#{category_group.underscore}" => true }
      @suppliers = suppliers.joins(:supplier_flags).where(supplier_flags: possible_category_option).distinct
    else
      @suppliers = suppliers.joins(:menu_sections).merge(MenuSection.where(archived_at: nil).joins(:categories).where('categories.group = ?', category_group)).distinct
    end
  end

  def filter_by_category_slugs
    case
    when options[:cumulative] || options[:team_suppliers]
      category_grouped_menu_section_supplier_ids = options[:category].map do |filter_category|
        filter_suppliers = suppliers.joins(menu_items: { menu_section: :categories })
        filter_suppliers = filter_suppliers.where(menu_items: { archived_at: nil })
        if options[:team_suppliers] # check if internal items are available for team orders
          filter_suppliers = filter_suppliers.where('menu_items.team_order = :truthy OR menu_items.team_order_only = :truthy', truthy: true)
        end
        filter_suppliers = filter_suppliers.where(menu_sections: { archived_at: nil })
        filter_suppliers = filter_suppliers.where(categories: { slug: filter_category })
        filter_suppliers.distinct.map(&:id)
      end
      suppliers_ids_within_all_categories = category_grouped_menu_section_supplier_ids.inject{|cumulative, category_supplier_ids| cumulative & category_supplier_ids }
      @suppliers = suppliers.where(id: suppliers_ids_within_all_categories)
    else
      @suppliers = suppliers.joins(:menu_sections).merge(MenuSection.where(archived_at: nil).joins(:categories).where('categories.slug IN (?)', options[:category])).distinct
    end
  end

  def filter_by_dietary_options
    possible_dietary_options = options[:dietary].reject{|option| SupplierProfile::SUPPLIER_DIETARY_FLAGS.exclude?(option.to_sym) }.map{|option| [option, true] }.to_h
    @suppliers = suppliers.joins(:supplier_flags).where(supplier_flags: possible_dietary_options)
  end

  def filter_by_delivery_options
    possible_delivery_options = options[:delivery].reject{|option| %w[needs_swipe_card_access supplies_in_working_hours provides_multi_service_point].exclude?(option) }.map{|option| [option, true] }.to_h

    @suppliers = suppliers.joins(:supplier_flags).where(supplier_flags: possible_delivery_options) if possible_delivery_options.present?
    filter_for_free_delivery if options[:delivery].include?('free_delivery')
  end

  def filter_for_free_delivery
    return if options[:suburb].blank?

    @suppliers = suppliers.joins(:delivery_zones).where(delivery_zones: { suburb: options[:suburb], delivery_fee: 0 }).distinct
  end

  def filter_by_minimum_spend
    min_spend = options[:category_group].present? && options[:category_group] == 'kitchen-supplies' ? 50 : 100
    minimums = Minimum.where(supplier_profile_id: suppliers.map(&:id)).group(:supplier_profile_id).maximum(:spend_price)
    minimum_supplier_ids = minimums.select{|_, spend_price| spend_price.to_i <= min_spend }.map{|supplier_id, _| supplier_id }
    @suppliers = suppliers.where(id: minimum_supplier_ids.uniq)
  end

  def filter_by_maximum_lead_time
    minimums = Minimum.where(supplier_profile_id: suppliers.map(&:id)).group(:supplier_profile_id).maximum(:lead_time)
    minimum_supplier_ids = minimums.select{|_, lead_time| lead_time.to_i <= MAXIMUM_LEAD_TIME }.map{|supplier_id, _| supplier_id }
    @suppliers = suppliers.where(id: minimum_supplier_ids.uniq)
  end

  def filter_by_supplier_flags
    possible_flag_options = options[:other].reject{|option| %w[provides_contactless_delivery is_socially_responsible is_eco_friendly is_indigenous_owned].exclude?(option) }.map{|option| [option, true] }.to_h
    @suppliers = suppliers.joins(:supplier_flags).where(supplier_flags: possible_flag_options) if possible_flag_options.present?
  end

  def filter_by_team_suppliers
    @suppliers = suppliers.where(team_supplier: true)
  end

  def filter_by_meal_plan_suppliers
    @suppliers = suppliers.joins(:menu_sections).merge(MenuSection.where(archived_at: nil).joins(:categories).where(categories: { slug: Category::MEAL_PLAN_CATEGORY_SLUGS })).distinct
  end

  def filter_visibility
    case
    when options[:for_cache]
      # no filtering
    when options[:visible_to].present? && options[:visible_to].is_a?(CustomerProfile)
      @suppliers = suppliers.visible_to(options[:visible_to].id)
    else
      @suppliers = suppliers.includes(:customer_profiles).where(customer_profiles_supplier_profiles: { supplier_profile_id: nil }).distinct
    end
  end

  def filter_admin_only
    @suppliers = suppliers.includes(:supplier_flags).where(supplier_flags: { admin_only: false })
  end

  def filter_by_keywords
    if options[:team_suppliers].present?
      @suppliers = suppliers.merge(SupplierProfile.search_by_team_order_keywords(options[:search_keywords]))
    else
      @suppliers = suppliers.merge(SupplierProfile.search_by_keywords(options[:search_keywords]))
    end
  end

  def filter_by_closure_dates
    @suppliers = suppliers.where.not('close_from <= :order_date AND close_to >= :order_date', order_date: Time.zone.parse(options[:order_date].to_s))
  end

  def offset_suppliers
    @suppliers = suppliers.offset(options[:offset])
  end

  def limit_suppliers
    @suppliers = suppliers.limit(options[:limit])
  end

  def refresh_suburb_cache
    Rails.cache.clear
  end

  def default_options
    {
      searchable: false,
      team_suppliers: false,
      mealUUID: nil,
      visible_to: nil,
      is_admin: false,
      suburb: nil,
      category_group: nil,
      category: [],
      dietary: [],
      delivery: [],
      other: [],
      cumulative: false,
      offset: nil,
      limit: nil,
      search_keywords: nil,
      order_date: nil,
      refresh: false,
      for_cache: false,
      for_react: false,
    }
  end

end
