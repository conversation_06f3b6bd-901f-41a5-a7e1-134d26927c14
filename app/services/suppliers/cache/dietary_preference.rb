class Suppliers::Cache::DietaryPreference

  POSSIBLE_PREFERENCES = %w[vegetarian vegan gluten_free dairy_free egg_free halal kosher nut_free].freeze
  PREFERENCE_THRESHOLD = 5

  def initialize(preference:)
    @preference = preference
    @result = Result.new
    @suppliers = []
  end

  def call
    if can_save_dietary_preferences?
      remove_preferences
      set_supplier_preferences
    end
    result
  end

private

  attr_reader :preference, :result

  def can_save_dietary_preferences?
    case
    when POSSIBLE_PREFERENCES.exclude?(preference)
      result.errors << "Passed in preference is invalid => #{preference}"
    when searchable_suppliers.blank?
      result.errors << "Could not find any searchable suppliers to set preference => #{preference}"
    end
    result.errors.blank?
  end

  def searchable_suppliers
    @_searchable_suppliers ||= Suppliers::List.new(options: { searchable: true, for_cache: true }, includes: [:supplier_flags]).call
  end

  def preference_suppliers
    grouped_items = menu_items.group_by(&:supplier_profile)
    grouped_items.select do |_, menu_items|
      menu_items.size >= PREFERENCE_THRESHOLD
    end.keys
  end

  def menu_items
    lister_options = {
      show_visible: true,
      show_active: true,
      suppliers: searchable_suppliers
    }
    items = MenuItems::List.new(includes: [:supplier_profile], options: lister_options).call
    items.where("#{menu_item_preference} = ?", true)
  end

  def remove_preferences
    searchable_suppliers.each do |supplier|
      supplier.supplier_flags.update(supplier_preference_params(false))
    end
  end

  def set_supplier_preferences
    preference_suppliers.each do |supplier|
      if supplier.supplier_flags.update(supplier_preference_params(true))
        result.preference_suppliers << supplier
      end
    end
  end

  def supplier_preference_params(value)
    Hash[supplier_preference.to_sym, value]
  end

  def supplier_preference
    "has_#{preference}_items"
  end

  def menu_item_preference
    "is_#{preference}"
  end

  class Result
    attr_accessor :preference_suppliers, :errors

    def initialize
      @preference_suppliers = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
