class Suppliers::FetchMajorSupplierMenu

  def initialize(supplier:, menu_options: {}, suburb: {}, profile: nil, order: nil)
    @supplier = supplier
    @menu_options = [default_menu_options, menu_options.to_h.symbolize_keys].inject(&:merge)
    @suburb = suburb
    @profile = profile
    @order = order
    @result = Result.new(supplier: supplier)
  end

  def call
    if can_view_supplier?
      if is_fetching_full_menu?
        fetch_menu_sections
      else
        retrieve_menu_section_grouped_items
      end
      fetch_favourites
      retrieve_recent_orders
      add_favourites_section_to_menu
      retrieve_markup_override
    end
    result
  end

private

  attr_accessor :result, :menu_items
  attr_reader :supplier, :menu_options, :suburb, :profile, :order

  def can_view_supplier?
    case
    when supplier.blank?
      result.errors << 'Please select a valid Supplier'
    when !supplier.is_searchable? && (profile.blank? || profile != supplier)
      result.errors << 'Please select an active Supplier'
    when !supplier.is_major_supplier?
      result.errors << 'Please select a Major Supplier'
    when is_invisible_to_profile?
      result.errors << 'You do not have access to this Supplier'
    end
    result.errors.blank?
  end

  def section_lister_options
    {
      supplier: supplier,
      show_visible: true,
      show_active: true,
      profile: profile,
      ignore_custom: true,
      order_by: :weight,
      within_budget: nil,
    }
  end

  def fetch_menu_sections
    section_grouped_items = {}
    menu_sections = MenuSections::List.new(options: section_lister_options).call
    menu_sections.each do |menu_section|
      section_grouped_items[menu_section] = []
    end
    result.section_grouped_menu_items = @section_grouped_menu_items = section_grouped_items.sort_by{|section, _| section.weight || 9999 }
  end

  def item_lister_options
    order_by = menu_options[:favourites_only].present? ? 'customer_profile_menu_items.created_at DESC' : { weight: :asc}
    {
      suburb: suburb,
      show_visible: true,
      show_active: true,
      order_type: 'normal',
      supplier: supplier,
      profile: profile,
      ignore_custom_menu_sections: true,
      order_by: order_by,
      query: menu_options[:query].presence,
      menu_section: menu_options[:menu_section].presence,
      show_favourites: menu_options[:favourites_only].presence,
    }
  end

  def fetch_menu_items
    @_menu_items ||= MenuItems::List.new(includes: [:menu_section, :serving_sizes, { menu_extra_sections: :menu_extras }], options: item_lister_options).call
  end

  def retrieve_menu_section_grouped_items
    section_grouped_items = {}
    case
    when menu_options[:menu_section].present?
     section_grouped_items[menu_options[:menu_section]] = fetch_menu_items
    when menu_options[:favourites_only].present?
      result.favourite_menu_items = section_grouped_items[favourite_menu_section] = fetch_menu_items
    when menu_options[:query].present?
      search_menu_section = MenuSection.search_menu_section(supplier: supplier, search_keywords: menu_options[:query])
      section_grouped_items[search_menu_section] = fetch_menu_items
    when menu_options[:recent_orders_only]
      section_grouped_items[recent_order_menu_section] = []
    end
    result.section_grouped_menu_items = @section_grouped_menu_items = section_grouped_items.sort_by{|section, _| section.weight || 9999 }
  end

  def fetch_favourites
    return if menu_options[:favourites_only] # already got favourites

    favourites = case
    when profile.blank? || !profile.is_a?(CustomerProfile)
      []
    when menu_items.present?
      CustomerProfileMenuItem.includes(:menu_item).where(customer_profile_id: profile, menu_item_id: menu_items.map(&:id)).order('customer_profile_menu_items.created_at DESC').map(&:menu_item)
    when menu_options[:query].blank?
      MenuItems::List.new(options: item_lister_options.merge({ show_favourites: true })).call
    end
    result.favourite_menu_items = favourites
  end

  def add_favourites_section_to_menu
    return if !is_fetching_full_menu?
    return if result.favourite_menu_items.blank?

    result.section_grouped_menu_items = @section_grouped_menu_items.unshift([favourite_menu_section, []])
  end

  def favourite_menu_section
    MenuSection.favourite_menu_section(supplier: supplier)
  end

  def retrieve_markup_override
    return if profile.blank? || !profile.is_a?(CustomerProfile)

    result.markup_override = Suppliers::FetchMarkupOverride.new(supplier: supplier, customer: profile, company: profile.company, required_override: :markup).call
  end

  def recent_order_menu_section
    MenuSection.recent_orders_menu_section(supplier: supplier)
  end

  def retrieve_recent_orders
    return if !is_fetching_full_menu? || (order.present? && order.status != 'draft')

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: profile).call
    return if recent_orders.blank?

    if result.favourite_menu_items.blank? # only add recent orders menu section if no favourites found
      result.section_grouped_menu_items = @section_grouped_menu_items.unshift([recent_order_menu_section, []])
    end
    # recent orders are passed when fetching full menu
    result.recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: profile).call
  end

  def is_invisible_to_profile?
    (profile.present? && !supplier.visible_to(profile.id)) || (profile.blank? && supplier.customer_profiles.present?) || (supplier.admin_only && !menu_options[:is_admin])
  end

  def is_fetching_full_menu?
    %i[query menu_section favourites_only recent_orders_only].none?{|option| menu_options[option].present? }
  end

  def default_menu_options
    {
      menu_section: nil,
      favourites_only: false,
      recent_orders_only: false,
      query: nil,
    }
  end

  class Result
    attr_accessor :supplier, :section_grouped_menu_items, :favourite_menu_items, :grouped_rate_cards, :markup_override, :recent_orders, :errors

    def initialize(supplier:)
      @supplier = supplier
      @section_grouped_menu_items = {}
      @favourite_menu_items = []
      @grouped_rate_cards = {}
      @markup_override = nil
      @recent_orders = []

      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end


