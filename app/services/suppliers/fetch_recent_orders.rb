class Suppliers::FetchRecentOrders

  ORDER_LIMIT = 2

  def initialize(supplier:, customer:)
    @supplier = supplier
    @customer = customer
  end

  def call
    if can_fetch?
      list_orders.where('created_at > ?', Time.zone.now - 1.year)
    else
      []
    end
  end

private

  attr_reader :supplier, :customer

  def can_fetch?
    supplier.present? && supplier.is_a?(SupplierProfile) && customer.present? && customer.is_a?(CustomerProfile)
  end

  def list_orders
    Orders::List.new(options: lister_options, includes: [order_lines: [:menu_item, :serving_size]]).call
  end

  def lister_options
    {
      for_customer: customer,
      supplier_ids: [supplier.id],
      statuses: %w[new amended confirmed delivered],
      order_type: 'one-off',
      order_variant: 'general',
      order_by: 'orders.created_at DESC',
      with_pagination: { page: 1, limit: ORDER_LIMIT }
    }
  end

end
