class Suppliers::Register

  USER_FIELDS = %i[firstname lastname email password suburb_id].freeze
  SUPPLIER_FIELDS = %i[company_name email company_address phone mobile abn_acn bsb_number bank_account_number].freeze

  def initialize(registration:)
    @registration = registration
    @result = Result.new
  end

  def call
    if can_register?
      SupplierProfile.transaction do
        result.user = build_user
        user.just_registered = true
        if user.save!
          associate_leads_to_user
          result.supplier = create_supplier
        else
          result.errors << 'We had some issues registering you. Please try again!'
        end
      rescue ActiveRecord::RecordInvalid
        result.errors += user.errors.full_messages if user.present?
        result.errors += supplier.errors.full_messages if supplier.present?
        raise ActiveRecord::Rollback
      end
    end
    result
  end

private

  attr_reader :registration, :user, :supplier, :result

  def can_register?
    case
    when registration.blank?
      result.errors << 'Cannot register with missing registration'
    when !registration.valid?
      result.errors += registration.errors.full_messages
    when User.where(email: registration.email).present?
      result.errors << 'An account already exists with these details'
    end
    result.errors.blank?
  end

  def build_user
    @user = User.new(sanitzed_user_params)
  end

  def sanitzed_user_params
    params = (USER_FIELDS - [:password]).map do |field|
      value = registration.send(field)
      [
        field,
        value.present? && value.is_a?(String) ? value.strip : value
      ]
    end.to_h
    params[:password] = registration.password
    params[:business] = 'yr'
    params
  end

  def create_supplier
    @supplier = SupplierProfile.new(sanitized_supplier_params)
    if supplier.save!
      profile = user.build_profile(profileable: supplier)
      if profile.save!
        sync_with_hubspot
        send_welcome_email
        notify_admin
        log_event
      end
      create_supplier_flags
    end
    supplier
  end

  def create_supplier_flags
    supplier.create_supplier_flags(certification_flags_params)
  end

  def certification_flags_params
    certification_fields = SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS
    cf = certification_fields.map do |field|
      next if !registration.respond_to?(field)

      value = registration.send(field)
      [field, value.present? && (value == '1' || value == 'true' || value == true)]
    end.reject(&:blank?).to_h
  end

  def sanitized_supplier_params
    params = [default_supplier_params, supplier_registration_params].inject(&:merge)
    params[:company_address_suburb_id] = registration.suburb_id
    params
  end

  def default_supplier_params
    {
      uuid: SecureRandom.uuid,
      is_searchable: false,
    }
  end

  def supplier_registration_params
    SUPPLIER_FIELDS.map do |field|
      value = registration.send(field)
      [
        field,
        (value.present? && value.is_a?(String) ? value.strip : value)
      ]
    end.to_h
  end

  def associate_leads_to_user
    Lead.where(email: user.email, user_id: nil).update_all(user_id: user.id)
  end

  def sync_with_hubspot
    Hubspot::SyncContact.new(contact: user.reload, refresh: true).delay.call
  end

  def send_welcome_email
    Suppliers::Emails::SendWelcomeEmail.new(supplier: supplier).delay(queue: :notifications).call
  end

  def notify_admin
    Admin::Emails::SendSupplierRegistrationEmail.new(supplier: supplier, registration: registration).delay(queue: :notifications).call
  end

  def log_event
    event_info = { category_group: registration.category_group }
    event_info[:is_team_supplier] = true if registration.category_group == 'catering-services' && registration.is_team_supplier.present?
    EventLogs::Create.new(event_object: supplier, event: 'new-supplier-registration', **event_info).delay(queue: :notifications).call
  end

  class Result
    attr_accessor :user, :supplier, :errors

    def initialize
      @user = nil
      @supplier = nil
      @errors = []
    end

    def success?
      errors.blank? && user.present? && supplier.present? && supplier.user == user
    end
  end
end

