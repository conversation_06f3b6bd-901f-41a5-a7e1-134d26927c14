class Reports::CaptureSupplierSpends

  def initialize(report_data:, categorised_orders:, verbose: false)
    @report_data = report_data
    @categorised_orders = categorised_orders
    @verbose = verbose
    @result = Result.new
  end

  def call
    capture_supplier_data
    save_supplier_spend_data
    save_ethical_spend_data
    result
  end

private

  attr_reader :report_data, :categorised_orders, :verbose, :result

  def capture_supplier_data
    categorised_orders.each do |digitized_order|
      capture_order_line_data_for(digitized_order)
    end
  end

  def save_supplier_spend_data
    result.supplier_spends.group_by(&:supplier).each do |supplier, supplier_spend|
      next if supplier.blank?

      order_datum = report_data.order_data.where(data_kind: 'Supplier', kind: supplier.name).first_or_create
      if order_datum.update(total_spend: supplier_spend.sum(&:spend))
        print 'sod-' if verbose
        result.order_data << order_datum
      end
    end
  end

  def save_ethical_spend_data
    SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.each do |flag|
      ethical_spends = result.supplier_spends.select{|spend| spend.ethical_flags.include?(flag) }
      next if ethical_spends.blank?

      ethical_label = I18n.t("supplier_profiles.tags.#{flag}.label")

      order_datum = report_data.order_data.where(data_kind: 'Ethical', kind: ethical_label).first_or_create
      if order_datum.update(total_spend: ethical_spends.sum(&:spend))
        print 'eod-' if verbose
        result.order_data << order_datum
      end
    end
  end

  def capture_order_line_data_for(digitized_order)
    order = digitized_order.order
    country_code = order.symbolized_country_code || :au
    delivery_with_gst = order.customer_delivery.present? ? order.customer_delivery * (1.0 + yordar_credentials(:yordar, :gst_percent, country_code).to_f) : nil

    order_lines = digitized_order.categorised_order_lines.map(&:order_line)
    supplier_grouped_order_lines = order_lines.group_by(&:supplier_profile)
    number_of_suppliers = supplier_grouped_order_lines.size

    if number_of_suppliers > 1
      supplier_grouped_order_lines.each do |supplier, supplier_order_lines|
        supplier_spend = supplier_order_lines.map do |supplier_order_line|
          order_line = supplier_order_line
          order_line.price_inc_gst(gst_country: country_code) * order_line.quantity
        end.sum
        supplier_spend += (delivery_with_gst / number_of_suppliers) if delivery_with_gst.present?
        print 'ols-' if verbose
        result.supplier_spends << SupplierSpend.new(supplier: supplier, ethical_flags: ethical_flags_for(supplier), spend: supplier_spend)
      end
    else
      supplier = supplier_grouped_order_lines.keys.first
      print 'ols-' if verbose
      result.supplier_spends << SupplierSpend.new(supplier: supplier, ethical_flags: ethical_flags_for(supplier), spend: order.customer_total)
    end
  end

  def ethical_flags_for(supplier)
    @_ethical_flags_for ||= {}
    return nil if supplier.blank?

    @_ethical_flags_for[supplier] ||= begin
      SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.map do |flag|
        supplier.send(flag) ? flag : nil
      end.reject(&:blank?)
    end
  end

  class SupplierSpend
    attr_reader :supplier, :ethical_flags, :spend

    def initialize(supplier:, ethical_flags:, spend:)
      @supplier = supplier
      @ethical_flags = ethical_flags
      @spend = spend
    end
  end

  class Result
    attr_accessor :supplier_spends, :order_data

    def initialize
      @supplier_spends = []
      @order_data = []
    end
  end

end