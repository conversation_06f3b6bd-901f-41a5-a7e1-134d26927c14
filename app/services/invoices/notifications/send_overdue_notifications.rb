class Invoices::Notifications::SendOverdueNotifications

  DAY_THRESHOLDS = {
    heads_up: 3,
    second: 7,
    third: 14,
    final: 21,
  }.freeze

  def initialize(kind:, time: Time.zone.now)
    @kind = kind
    @time = time
    @result = Result.new
  end

  def call(dry_run: false)
    if can_notify?
      customer_grouped_invoices.each do |customer, customer_invoices|
        next if dry_run

        send_email_for(customer: customer, invoices: customer_invoices)
        log_event_for(invoices: customer_invoices)
      end
      notify_via_slack
    end
    result
  end

private

  attr_reader :kind, :time, :result

  def can_notify?
    case
    when [0, 6].include?(time.wday)
      result.errors << 'Cannot send overdue notifications on a Weekend'
    when kind.blank? || DAY_THRESHOLDS[kind.to_sym].blank?
      result.errors << "Cannot send an invalid kind of overdue notification = #{kind}"
    when notifiable_invoices.blank?
      # result.errors << 'No invoices are overdue' # should not really error
    end
    result.errors.blank?
  end

  def customer_grouped_invoices
    @_customer_grouped_invoices ||= notifiable_invoices.group_by{|invoice| invoice.invoice_orders.first.customer_profile }
  end

  def notifiable_invoices
    @_notifiable_invoices ||= begin
      invoices = Invoice.where(status: 'confirmed', payment_status: 'unpaid')
      invoices = invoices.where(due_at: [due_starts_on..due_ends_on])
      invoices = invoices.left_outer_joins(:orders).where.not(orders: { customer_profile: CustomerProfile.where(id: yordar_credentials(:yordar, :non_invoicing_customer_id)) })
      invoices = invoices.where(do_not_notify: false)
      invoices.order(:due_at, :id)
    end
  end

  def due_starts_on
    if time.wday == 1
      notifiable_time - 2.days
    else
      notifiable_time
    end.beginning_of_day
  end

  def due_ends_on
    notifiable_time.end_of_day
  end

  def notifiable_time
    @_notifiable_time ||= time - DAY_THRESHOLDS[kind.to_sym].days
  end

  def send_email_for(customer:, invoices:)
    email_sender = Customers::Emails::SendOverdueInvoiceEmail.new(customer: customer, invoices: invoices, kind: kind).call
    if email_sender.success?
      result.notified_customers << customer
      result.notified_invoices += invoices
    else
      result.errors += email_sender.errors
    end
  end

  def log_event_for(invoices:)
    invoices.each do |invoice|
      EventLogs::Create.new(event_object: invoice, event: 'invoice-overdue', overdue_by: "#{DAY_THRESHOLDS[kind.to_sym]} days")
    end
  end

  def notify_via_slack
    message = ":warning: The following #{customer_grouped_invoices.size} customers have invoices which are overdue by *#{DAY_THRESHOLDS[kind.to_sym]} days*"
    slack_attachments = customer_grouped_invoices.map do |customer, customer_invoices|
      {
        type: 'mrkdwn',
        text: "#{customer.name} (#{customer.id}) => #{customer_invoices.map(&:number).join(', ')}",
        color: 'warning'
      }
    end
    SlackNotifier.send(message, attachments: slack_attachments)
  end

  class Result
    attr_accessor :notified_customers, :notified_invoices, :errors

    def initialize
      @notified_customers = []
      @notified_invoices = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
