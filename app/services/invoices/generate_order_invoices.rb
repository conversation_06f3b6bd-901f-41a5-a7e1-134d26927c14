class Invoices::GenerateOrderInvoices

  def initialize(frequency:, time: Time.zone.now, notify_customers: false)
    @frequency = frequency
    @time = time
    @notify_customers = notify_customers
    @result = Result.new
  end

  def call(dry_run: false)
    @dry_run = dry_run
    case frequency
    when 'cards-only'
      orders_with_card_payments.each do |card_order|
        generate_invoice_for(invoicable_orders: [card_order])
      end
    when 'instantly'
      (orders_within_frequency + individually_invoiced_orders).each do |instant_order|
        generate_invoice_for(invoicable_orders: [instant_order])
      end
    when 'weekly', 'monthly'
      orders_within_frequency.group_by(&:customer_profile).each do |customer, customer_orders|
        generate_invoices_by_po(customer, customer_orders)
      end
    end
    result
  end

private

  attr_reader :frequency, :time, :notify_customers, :dry_run, :result

  def orders_needing_invoices
    @_orders_needing_invoices ||= Order.where(status: 'delivered', invoice_id: nil, payment_status: 'unpaid').where.not(customer_profile_id: yordar_credentials(:yordar, :non_invoicing_customer_id))
  end

  def orders_with_card_payments
    arel = CreditCard.arel_table
    valid_token_condition = arel[:gateway_token].not_eq_any([nil, '']).or(arel[:stripe_token].not_eq_any([nil, '']))

    card_payment_orders = orders_needing_invoices.joins(:credit_card)
    card_payment_orders = card_payment_orders.where(credit_cards: { pay_on_account: false, auto_pay_invoice: false }) # is not a pay on account card
    card_payment_orders = card_payment_orders.where(valid_token_condition)
    card_payment_orders.distinct
  end

  def frequency_delivery_times
    @_frequency_delivery_times ||= case frequency
    when 'weekly'
      previous_week = time - 1.week
      {
        from: previous_week.beginning_of_week,
        to: previous_week.end_of_week
      }
    when 'monthly'
      previous_month = time - 1.month
      {
        from: previous_month.beginning_of_month,
        to: previous_month.end_of_month
      }
    else
      {}
    end
  end

  def frequency_customer_ids
    if frequency == 'instantly'
      instant_customers = CustomerProfile.joins(:billing_details).where(billing_details: { frequency: 'instantly' })
      non_billing_details_customers = CustomerProfile.includes(:billing_details).where(billing_details: { id: nil }) # non billing details customers are considered with billing preference of instantly
      CustomerProfile.where(id: (instant_customers.select(&:id) + non_billing_details_customers.select(&:id))).select(&:id)
    else
      CustomerProfile.joins(:billing_details).where(billing_details: { frequency: frequency }).select(&:id)
    end
  end

  def orders_within_frequency
    frequency_orders = orders_needing_invoices.joins(:customer_profile).where(customer_profiles: { id: frequency_customer_ids })
    frequency_orders = frequency_orders.where(delivery_at: [frequency_delivery_times[:from]...frequency_delivery_times[:to]]) if frequency_delivery_times.present?
    frequency_orders = frequency_orders.where.not(id: orders_with_card_payments.select(:id)) # exclude any on-card orders
    frequency_orders = frequency_orders.where(invoice_individually: false) # exclude any individually invoiced orders
    frequency_orders.distinct
  end

  def individually_invoiced_orders
    @_individually_invoiced_orders ||= orders_needing_invoices.where(invoice_individually: true)
  end

  def generate_invoices_by_po(customer, customer_orders)
    company = customer.company
    if company.present? && company.invoice_by_po?
      # Group orders by customer purchase order
      cpo_grouped_orders = customer_orders.group_by(&:customer_purchase_order)

      if customer.has_gst_split_invoicing
        # Group orders by GST-free purchase order or customer purchase order
        gst_free_cpo_grouped_orders = customer_orders.group_by{ |order| order.gst_free_customer_purchase_order || order.customer_purchase_order }
        # Merge both grouped orders
        po_grouped_orders = gst_free_cpo_grouped_orders.merge(cpo_grouped_orders) do |_purchase_order, gst_free_orders, cpo_orders|
          (gst_free_orders + cpo_orders).uniq
        end
      else
        po_grouped_orders = cpo_grouped_orders
      end

      po_grouped_orders.each do |purchase_order, po_orders|
        generate_invoice_for(invoicable_orders: po_orders.uniq, purchase_order: purchase_order)
      end
    else
      generate_invoice_for(invoicable_orders: customer_orders)
    end
  end

  def generate_invoice_for(invoicable_orders:, purchase_order: nil)
    if dry_run
      result.generated_invoices << invoicable_orders.map(&:id)
      return
    end
    invoice_generator = Invoices::GenerateInvoice.new(invoicable_orders: invoicable_orders, invoice_dates: frequency_delivery_times, purchase_order: purchase_order, notify_customer: notify_customers).call
    if invoice_generator.success?
      result.generated_invoices << invoice_generator.generated_invoice
    else
      result.errors += invoice_generator.errors
    end
  end

  class Result
    attr_accessor :generated_invoices, :errors

    def initialize
      @generated_invoices = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end
