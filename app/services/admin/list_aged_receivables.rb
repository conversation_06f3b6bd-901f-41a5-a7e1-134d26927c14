class Admin::ListAgedReceivables

  TIME_FRAMES_MAP = {
    current: 'Current',
    within_1_month: '<1 month',
    more_than_1_month: '1 month',
    more_than_2_months: '2 months',
    more_than_3_months: '3 months',
    older: 'Older'
  }

  def initialize(options: {})
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
    @aged_receivables = []
  end

  def call
    unpaid_invoices.group_by(&:customer_profile).each do |customer, customer_invoices|
       possible_time_frame_keys.each do |time_frame_key|
        capture_data_for(customer: customer, invoices: customer_invoices, time_frame_key: time_frame_key)
      end
    end

    customer_aged_receivables
  end

private

  attr_reader :filter_options, :aged_receivables

  def possible_time_frame_keys
    if filter_options[:time_frame].present? && TIME_FRAMES_MAP.keys.include?(filter_options[:time_frame].to_sym)
      TIME_FRAMES_MAP.keys & [filter_options[:time_frame].to_sym]
    else
      TIME_FRAMES_MAP.keys
    end
  end

  def capture_data_for(customer: , invoices:, time_frame_key:)
    time_frame = time_frames[time_frame_key]
    return if time_frame.blank?

    timed_invoices = invoices.select do |invoice|
      case
      when time_frame[:from].present? && time_frame[:to].present?
        invoice.due_at > time_frame[:from] && invoice.due_at <= time_frame[:to]
      when time_frame[:from].present?
        invoice.due_at > time_frame[:from]
      when time_frame[:to].present?
        invoice.due_at <= time_frame[:to]
      end
    end
    aged_receivables << CustomerTimedInvoices.new(customer: customer, time_frame: time_frame_key, invoices: timed_invoices)
  end

  def customer_aged_receivables
    aged_receivables.group_by(&:customer).sort_by do |customer, _|
      case filter_options[:sort_by]
      when 'customer_name'
        customer.name
      when 'company_name'
        [(customer.company&.name || customer.company_name), customer.name]
      end
    end.to_h
  end

  def unpaid_invoices
    lister_options = {
      page: 1,
      limit: 2000,
      payment_status: 'unpaid',
      query: filter_options[:query],
      for_user: filter_options[:user],
    }
    invoices = ::Admin::ListInvoices.new(options: lister_options, includes: [orders: { customer_profile: [:user, :company, :billing_details] }]).call
    invoices.where('due_at >= ?', Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE))
  end

  def time_frames
    {
      current: {
        from: filter_options[:time],
        to: nil,
      },
      within_1_month: {
        from: (filter_options[:time] - 1.month),
        to: filter_options[:time],
      },
      more_than_1_month: {
        from: (filter_options[:time] - 2.months),
        to: (filter_options[:time] - 1.month),
      },
      more_than_2_months: {
        from: (filter_options[:time] - 3.months),
        to: (filter_options[:time] - 2.months),
      },
      more_than_3_months: {
        from: (filter_options[:time] - 4.months),
        to: (filter_options[:time] - 3.months),
      },
      older: { 
        from: nil,
        to: (filter_options[:time] - 4.months),
      }
    }
  end

  def default_options
    {
      user: nil,
      time: Time.zone.now.beginning_of_day,
      time_frame: nil,
      query: nil,
      sort_by: 'company_name',
    }
  end

  class CustomerTimedInvoices
    attr_reader :customer, :time_frame, :invoices

    def initialize(customer:, time_frame:, invoices:)
      @customer = customer
      @time_frame = time_frame
      @invoices = invoices
    end

    def total
      amount = invoices.sum(&:amount_price)
      amount != 0 ? amount : nil
    end
  end

end