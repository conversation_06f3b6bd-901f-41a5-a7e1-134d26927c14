class Admin::ListInvoices

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @invoices = base_invoices
    filter_by_query  if filter_options[:query].present?
    filter_by_access
    filter_by_payment_status if filter_options[:payment_status].present?
    filter_by_invoice if filter_options[:invoice].present?
    sort_invoices if filter_options[:order_by].present?
    paginate_invoices

    invoices.includes(includes).distinct
  end

private
  
  attr_reader :includes, :filter_options, :invoices

  def base_invoices
    Invoice.all
  end

  def filter_by_query
    invoice_arel = Invoice.arel_table
    number_condition = invoice_arel[:number].matches("#{filter_options[:query]}%")

    order_arel = Order.arel_table
    order_id_condition = order_arel[:id].in(filter_options[:query])

    customer_arel = CustomerProfile.arel_table
    customer_name_condition = customer_arel[:customer_name].matches("%#{filter_options[:query]}%")
    company_name_condition = customer_arel[:company_name].matches("%#{filter_options[:query]}%")

    company_arel = Company.arel_table
    company_condition = company_arel[:name].matches("%#{filter_options[:query]}%")

    query_condition = number_condition.or(order_id_condition).or(customer_name_condition).or(company_name_condition).or(company_condition)
    @invoices = invoices.left_outer_joins(:orders, { customer_profile: :company }).where(query_condition)
  end

  def filter_by_access
    case
    when filter_user.blank? # if no user is passed return none
      @invoices = Invoice.none
    when (filter_user.admin? || filter_user.super_admin?) && !filter_options[:favourites_only]
      # do not fitler - admins get access to all invoices
    when filter_options[:customer_id].present? && customer_ids.include?(filter_options[:customer_id])
      @invoices = invoices.where(customer_profile_id: filter_options[:customer_id])
    when customer_ids.present?
      @invoices = invoices.where(customer_profile_id: customer_ids)
    else # defaults to none
      @invoices = Invoice.none
    end
  end

  def filter_by_payment_status
    case filter_options[:payment_status]
    when 'paid', 'unpaid'
      @invoices = invoices.where(payment_status: filter_options[:payment_status], status: 'confirmed', do_not_notify: false)
    when 'overdue'
      @invoices = invoices.where(payment_status: 'unpaid', status: 'confirmed', do_not_notify: false)
      @invoices = invoices.where('due_at <= ?', Time.zone.now.beginning_of_day)
      @invoices = invoices.where('due_at >= ?', Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE))
    end
  end

  def filter_by_invoice
    if filter_options[:invoice].is_a?(Invoice)
      @invoices = invoices.where(id: filter_options[:invoice].id)
    else
      @invoices = Invoice.none
    end
  end

  def sort_invoices
    @invoices = invoices.order(filter_options[:order_by])
  end

  def paginate_invoices
    @invoices = invoices.page(filter_options[:page]).per(filter_options[:limit])
  end

  def filter_user
    @_filter_user ||= filter_options[:for_user]
  end

  def customer_ids
    return [] if filter_user.blank?

    lister_options = { for_user: filter_user, favourites_only: filter_options[:favourites_only], limit: nil }
    customers = Admin::ListCustomers.new(options: lister_options).call.map(&:id)
  end

  def default_options
    {
      for_user: nil,
      customer_id: nil,
      query: nil,
      payment_status: nil,
      invoice: nil,
      favourites_only: false,
      page: 1,
      limit: 20,      
      order_by: { created_at: :desc, id: :desc }
    }
  end

end