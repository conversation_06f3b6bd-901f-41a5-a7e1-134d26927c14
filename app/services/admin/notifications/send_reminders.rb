class Admin::Notifications::SendReminders

  def initialize(time:, frequency:)
    @time = time
    @frequency = frequency
    @result = Result.new
  end

  def call
    frequency_reminders.each do |reminder|
      send_email_for(reminder)
    end
    result
  end

private

  attr_reader :time, :frequency, :result

  def send_email_for(reminder)
    return if !can_send_reminder?(reminder)

    email_sender = Admin::Emails::SendReminderEmail.new(reminder: reminder).call
    if email_sender.success?
      result.sent_notifications << reminder
    else
      result.errors += email_sender.errors
    end
  end

  def can_send_reminder?(reminder)
    starting_at = reminder.starting_at
    case frequency
    when 'weekly'
      starting_at.wday == time.wday
    when 'fortnightly'
      starting_at.wday == time.wday && (starting_at.strftime('%V').to_i % 2) == (time.strftime('%V').to_i % 2)
    when 'monthly_by_date'
      starting_at.strftime('%d').to_i == time.strftime('%d').to_i
    when 'monthly_by_weekday'
      # first weekday of month
      reminder_at = time.beginning_of_month
      while reminder_at.wday != starting_at.wday
        reminder_at += 1.day
      end
      reminder_at.to_date == time.to_date
    else
      false
    end
  end

  def frequency_reminders
    reminders = Reminder.where(frequency: frequency)
    reminders = reminders.where('starting_at <= ?', time)
    reminders.order(starting_at: :asc)
  end

  class Result
    attr_accessor :sent_notifications, :errors

    def initialize
      @sent_notifications = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end

end