class Admin::Notifications::NotifyPendingOrders

  def initialize(time: Time.zone.now)
    @time = time
    @result = Result.new
  end

  def call
    if can_notify?
      send_admin_email
      log_event
      result.pending_orders = pending_orders
    end
    result
  end

private

  attr_reader :time, :result

  def can_notify?
    case
    when time.on_weekend?
      result.errors << 'Cannot notify on a weekend'
    when pending_orders.blank?
      result.errors << 'No Pending Orders'
    end
    result.errors.blank?
  end

  def send_admin_email
    Admin::Emails::SendPendingOrdersEmail.new(pending_orders: pending_orders, delivery_on: delivery_on).call
  end

  def log_event
    custom_orders, normal_orders = pending_orders.partition(&:is_event_order?)
    EventLogs::Create.new(event: 'pending-orders', delivery_on: delivery_on.to_s(:full_date), custom_orders: custom_orders.map(&:id).sort, normal_orders: normal_orders.map(&:id).sort)
  end

  def pending_orders
    return @pending_orders if !@pending_orders.nil?

    orders = Order.where(pending_orders_condition)
    orders = orders.where(delivery_at: delivery_on..delivery_end)
    orders = orders.where.not(customer_profile_id: nil)
    orders = orders.where.not(delivery_at: nil)

    @pending_orders = orders.order(delivery_at: :asc, id: :asc)
  end

  def pending_orders_condition
    arel = Order.arel_table
    custom_order_condition = arel[:order_variant].eq('event_order').and(arel[:status].eq('draft'))
    quote_order_condition = arel[:status].eq('quoted')
    custom_order_condition.or(quote_order_condition)
  end

  def delivery_on
    (time + 1.day).beginning_of_day
  end

  def delivery_end
    days_in_future = time.wday == 5 ? 3 : 1
    (time + days_in_future.days).end_of_day
  end

  class Result
    attr_accessor :pending_orders, :errors

    def initialize
      @pending_orders = []
      @errors = []
    end

    def success?
      errors.blank?
    end
  end
end
