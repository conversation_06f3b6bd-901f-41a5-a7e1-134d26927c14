class Admin::Emails::SendReminderEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-reminder'.freeze

  def initialize(reminder:)
    @reminder = reminder
    @result = Result.new
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send Reminder email for remindable #{reminder&.remindable_type} ID: #{reminder&.remindable_id} - #{reminder&.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { reminder_id: reminder.id, remindable_type: reminder.remindable_type, remindable_id: reminder.remindable_id })
      result.errors << error_message
    end
    result
  end

private

  attr_reader :reminder, :result

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      cc: email_cc,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "Customer Reminder for #{reminder.remindable_type} ID: #{reminder.remindable_id} sent - #{reminder.id}"
      result.sent_notification = email_sender.email
    else
      result.errors += email_sender.errors
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    @_email_recipients ||= begin
      recipients = case reminder.recipients
      when 'account_manager', 'pantry_manager'
        managers.map(&:email_recipient).join(';')
      when 'accounts_team'
        yordar_credentials(:yordar, :accounts_email)
      end
      recipients.presence || yordar_credentials(:yordar, :orders_email)
    end
  end

  def email_cc
    return nil if reminder.recipients == 'accounts_team' || email_recipients == yordar_credentials(:yordar, :orders_email)

    yordar_credentials(:yordar, :orders_email)
  end

  def email_subject
    if remindable.present?
      "Yordar - TASK REMINDER for #{remindable.name} - #{reminder.title}"
    else
      "Yordar - TASK REMINDER: #{reminder.title}"
    end
  end

  def email_options
    {
      fk_id: reminder.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      reminder: deep_struct(reminder_data),
      remindable: deep_struct(remindable_data),

      header_color: :black
    }
  end

  def reminder_data
    {
      frequency: reminder.frequency,
      title: reminder.title,
      message: reminder.message
    }
  end

  def remindable_data
    return {} if remindable.blank?

    {
      name: remindable.name,
      company_name: (remindable.respond_to?(:company) && remindable.company&.name.presence) || remindable.company_name,
      admin_url: admin_url
    }
  end

  def remindable
    @_remindable ||= reminder.remindable
  end

  def admin_url
    case reminder.remindable_type
    when 'CustomerProfile'
      url_helper.customers_admin_url(customer_name: remindable.email, host: app_host)
    else
      ''
    end
  end

  def managers
    return [] if reminder.recipients == 'accounts_team' || reminder.remindable_type != 'CustomerProfile'

    @_account_managers = Customers::FetchManagers.new(customer: remindable, type: reminder.recipients).call
  end

  def confirmation_url
    Rails.application.routes.url_helpers.user_confirmation_url(confirmation_token: customer.user.confirmation_token, host: app_host)
  end

  def email_ref
    "#{EMAIL_TEMPLATE}-#{reminder.id}-#{Time.zone.now.to_s(:date_spreadsheet)}"
  end

end

