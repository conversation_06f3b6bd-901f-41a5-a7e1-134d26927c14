class Admin::Emails::SendSupplierRegistrationEmail < Notifications::Base

  EMAIL_TEMPLATE = 'yordar-new_supplier_registration'.freeze

  def initialize(supplier:, registration:)
    @supplier = supplier
    @registration = registration
  end

  def call
    begin
      send_email
    rescue => exception
      error_message = "Failed to send supplier registration email to admin for #{supplier.id}"
      log_errors(exception: exception, message: error_message, sentry: true, error_objects: { supplier_id: supplier.id })
    end
  end

private

  attr_reader :supplier, :registration

  def send_email
    email_sender = ::Emails::Send.new(
      template_name: EMAIL_TEMPLATE,
      recipient: email_recipients,
      subject: email_subject,
      email_options: email_options,
      email_variables: email_variables
    ).call
    if email_sender.success?
      Rails.logger.info "New supplier registration email sent to admin #{supplier.id}"
      email_sender.email
    else
      raise NotificationError.new(email_sender.errors.join('. '))
    end
  end

  def email_recipients
    supplier_admin_email = yordar_credentials(:yordar, :supplier_email)
    admin_email = yordar_credentials(:yordar, :admin_email)
    [supplier_admin_email, admin_email].compact.join('; ')
  end

  def email_subject
    "YORDAR: New #{supplier_category} supplier registration"
  end

  def email_options
    {
      fk_id: supplier.id,
      ref: email_ref,
    }
  end

  def email_variables
    {
      supplier: deep_struct(supplier_data),
    }
  end

  def supplier_data
    {
      name: "#{supplier.user.firstname} #{supplier.user.lastname}",
      company_name: supplier.company_name,
      admin_url: url_helper.suppliers_admin_url(company_name: supplier.email, host: app_host),
      category: supplier_category,
      is_team_supplier: registration.category_group == 'catering-services' && registration.is_team_supplier.present?
    }
  end

  def supplier_category
    @_supplier_category ||= registration.category_group == 'catering-services' ? 'Catering' : 'Snacks and Pantry'
  end

  def email_ref
    EMAIL_TEMPLATE
  end

end

