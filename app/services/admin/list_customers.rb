class Admin::ListCustomers

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @customers = base_customers
    filter_by_query  if filter_options[:query].present?
    filter_by_access
    filter_by_customer if filter_options[:customer].present?
    filter_by_favourites if filter_options[:favourites_only].present?
    order_customers if filter_options[:order_by].present?
    paginate_customers if filter_options[:page].present? && filter_options[:limit].present?

    customers.includes(includes)
  end

private
  
  attr_reader :includes, :filter_options, :customers

  def base_customers
    CustomerProfile.all
  end

  def filter_by_query
    customer_arel = CustomerProfile.arel_table
    name_condition = customer_arel[:customer_name].matches("%#{filter_options[:query]}%")
    company_name_condition = customer_arel[:company_name].matches("%#{filter_options[:query]}%")

    user_arel = User.arel_table
    email_condition = user_arel[:email].matches("%#{filter_options[:query]}%")

    company_arel = Company.arel_table
    company_condition = company_arel[:name].matches("%#{filter_options[:query]}%")

    query_condition = name_condition.or(company_name_condition).or(email_condition).or(company_condition)
    @customers = customers.joins(:user).left_outer_joins(:company).where(query_condition)
  end

  def order_customers
    @customers = customers.order(filter_options[:order_by])
  end

  def paginate_customers
    @customers = customers.page(filter_options[:page]).per(filter_options[:limit])
  end

  def filter_by_favourites
    return if filter_options[:for_user].blank?

    @customers = customers.where(id: filter_options[:for_user].favourite_customer_profile_ids)
  end

  def filter_by_access
    user = filter_options[:for_user]

    case
    when user.blank? # if no user is passed return none
      @customers = CustomerProfile.none
    when (user.admin? || user.super_admin?) && filter_options[:my_customers].blank?
      # do not fitler - admins get access to all customers
    when user.allow_all_customer_access && filter_options[:my_customers].blank?
      @customers = customers.joins(:user).where(users: { is_active: true })
    when is_customer_team_admin?
      admin_customer = user.profile.profileable
      customer_arel = CustomerProfile.arel_table
      self_customer_condition = filter_options[:my_customers].present? ? customer_arel[:id].eq(-1) : customer_arel[:id].eq(admin_customer.id)
      permission_arel = AccessPermission.arel_table
      active_condition = permission_arel[:active].eq(true)
      admin_condition = permission_arel[:admin_id].eq(admin_customer.id)
      team_admin_condition = self_customer_condition.or(permission_arel.grouping(admin_condition.and(active_condition)))

      @customers = customers.left_outer_joins(:access_permissions_as_customer).where(team_admin_condition)
    else # defaults to none
      @customers = CustomerProfile.none
    end
  end

  def filter_by_customer
    if filter_options[:customer].is_a?(CustomerProfile)
      @customers = customers.where(id: filter_options[:customer].id)
    else
      @customers = CustomerProfile.none
    end
  end

  def is_customer_team_admin?
    user = filter_options[:for_user]
    return false if user.blank?

    user_profile = user.profile&.profileable
    user_profile.present? && user_profile.is_a?(CustomerProfile) && user_profile.company_team_admin?
  end

  def default_options
    {
      for_user: nil,
      query: nil,
      customer: nil,
      favourites_only: false,
      my_customers: false,
      page: 1,
      limit: 20,      
      order_by: { updated_at: :desc, id: :desc }
    }
  end

end