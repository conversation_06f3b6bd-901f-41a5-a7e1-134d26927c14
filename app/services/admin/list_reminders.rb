class Admin::ListReminders

  def initialize(includes: [], options: {})
    @includes = includes
    @filter_options = [default_options, options.to_h.symbolize_keys].inject(&:merge)
  end

  def call
    @reminders = base_reminders
    filter_active if filter_options[:active_only].present?
    filter_by_query  if filter_options[:query].present?
    filter_by_reminder if filter_options[:reminder].present?
    order_reminders if filter_options[:order_by].present?
    paginate_reminders

    reminders.includes(includes).distinct
  end

private

  attr_reader :includes, :filter_options, :reminders

  def base_reminders
    Reminder.all
  end

  def filter_active
    @reminders = reminders.where(active: true)
  end

  def filter_by_query
    reminder_arel = Reminder.arel_table
    title_condition = reminder_arel[:title].matches("#{filter_options[:query]}%")
    message_condition = reminder_arel[:message].matches("%#{filter_options[:query]}%")
    frequency_condition = reminder_arel[:frequency].matches("%#{filter_options[:query].gsub(/\s/, '_')}%")

    customer_arel = CustomerProfile.arel_table
    customer_name_condition = customer_arel[:customer_name].matches("%#{filter_options[:query]}%")
    company_name_condition = customer_arel[:company_name].matches("%#{filter_options[:query]}%")

    company_arel = Company.arel_table
    company_condition = company_arel[:name].matches("%#{filter_options[:query]}%")

    supplier_arel = SupplierProfile.arel_table
    supplier_name_condition = supplier_arel[:company_name].matches("%#{filter_options[:query]}%")

    remindable_customer_join = <<-SQL
      LEFT OUTER JOIN customer_profiles ON reminders.remindable_type = 'CustomerProfile' AND reminders.remindable_id = customer_profiles.id
      LEFT OUTER JOIN companies ON customer_profiles.company_id = companies.id
    SQL

    remindable_supplier_join = <<-SQL
      LEFT OUTER JOIN supplier_profiles ON reminders.remindable_type = 'SupplierProfile' AND reminders.remindable_id = supplier_profiles.id
    SQL

    joins_sql = [remindable_customer_join, remindable_supplier_join].join(' ')

    query_condition = title_condition.or(message_condition)
      .or(frequency_condition)
      .or(customer_name_condition)
      .or(company_name_condition)
      .or(company_condition)
      .or(supplier_name_condition)
    @reminders = reminders.joins(joins_sql).where(query_condition)
  end

  def filter_by_reminder
    if filter_options[:reminder].is_a?(Reminder)
      @reminders = reminders.where(id: filter_options[:reminder].id)
    else
      @reminders = Reminder.none
    end
  end

  def order_reminders
    @reminders = reminders.order(filter_options[:order_by])
  end

  def paginate_reminders
    @reminders = reminders.page(filter_options[:page]).per(filter_options[:limit])
  end

  def default_options
    {
      query: nil,
      reminder: nil,
      page: 1,
      limit: 20,
      active_only: false,
      order_by: { created_at: :desc, id: :desc }
    }
  end

end