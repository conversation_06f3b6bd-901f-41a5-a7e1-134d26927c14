.auth-container{
	max-width: 1300px;
	padding: 0 150px;
	margin: auto;
	@include media-down(hamburger) {
		padding: 0 40px;
	}
	@include media-down(mobile) {
		padding: 0;
	}
	&__success {
		display:flex;
		align-items: center;
		background: white;
		width:100%;
		max-width: 700px;
		margin: auto;
		padding: 3rem;
		border-radius: 4px;
		img {
			margin-right: 3rem;
		}
	}
}
.auth-card {
	display: flex;
	background: #FFFFFF;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.10);
	border-radius: 4px;
	@include media-down(hamburger) {
		flex-direction: column;
	}
	&__illustration {
		margin-right: 20px;
		padding: 35px;
		border-right: 1px solid #e0e0e0;
		width: 60%;
		// Prevent layout shift by reserving space for images
		min-height: 200px;
		img {
			display: block;
			max-width: 100%;
			height: auto;
		}
		&.quote {
			padding: 0;
			margin: 0;
			.quote-image {
				width: 450px;
				height: 300px;
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}
		@include media-down(hamburger) {
			width: 100%;
			border: none;
			padding-bottom: 0;
		}
	}
	&__title {
		font-weight: bold;
		margin-top: 16px;
		font-size: 22px;
	}
	&__step {
		margin-top: 8px;
		color: #b3b3b3;
	}
}

.authorization-module {
	background: $white;
	border-radius: $global-radius;
	overflow: hidden;
	padding: 2.3rem 1rem;
	position: relative;
	width: 100%;
	&.plan {
		padding: 1rem 1.2rem 2rem;
		label {
			font-family: $header-font-family;
			display: flex;
			align-items: center;
			&::before {
				content: '';
				display: inline-block;
				width: 16px;
				height: 16px;
				margin-right: 8px;
				background-repeat: no-repeat;
				background-size: contain;
				background-position: center;
			}
			&.name::before {
				background-image: asset-data-url('icons/note.svg')
			}
			&.time::before {
				background-image: asset-data-url('icons/alarm.svg')
			}
			&.staff::before {
				background-image: asset-data-url('icons/team.svg')
			}
			&.address::before {
				background-image: asset-data-url('icons/marker.svg')
			}
			&.level::before {
				background-image: asset-data-url('icons/company.svg')
			}
			&.instructions::before {
				background-image: asset-data-url('icons/suppliers.svg')
			}

		}
	}
}
.authorization-module--splitted {

	&:after {
		content: '';
		width: 1px;
		background: #e5e5e5;
		position: absolute;
		left: 50%;
		margin-left: -0.5px;
		top: 3rem;
		bottom: 3rem;
	}

	@media screen and (max-width: 639px) {
		&:after {
			content: normal;
		}
	}
}
.login-heading {
	font-family: $header-font-family;
	font-weight: 700;
}
.authorization-form {
	width: calc(100% - #{$global-padding});
	margin: 0 auto;

	.error {
		color: $error;
	}

	.row {
		margin-left: -.625rem;
    margin-right: -.625rem;
	}
	.form-header {
		min-height: 7.5rem;
	}
	.form-title {
		font-size: rem-calc(24);
	}
	p {
		font-size: rem-calc(14);
		color: $medium-gray;
	}
	a {
		/*color: $medium-gray;*/
		color: $black;
		text-decoration: underline;
		transition: color 0.25s ease-out;
		&:hover {
			color: $black
		}
	}
	.forgot-password {
		font-size: rem-calc(15);
		margin-top: 1rem;
	}
	.button {
		text-transform: uppercase;
		min-width: 180px;
	}
	.no-min-width {
		min-width: auto;
		margin-top: 1rem;
	}
	.button-large {
		min-width: 100%;
		padding: 1.3rem;
		margin-top: 1rem;
	}
	.input {
		height: 3.5rem;
		background-color: white;
		border: 3px;
		-webkit-box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
		-moz-box-shadow:    0px 0px 5px rgba(0, 0, 0, 0.25);
		box-shadow:         0px 0px 5px rgba(0, 0, 0, 0.25);
	}
	.input-label {
		margin-bottom: 6px;
	}
	select {
		height: 3.5rem;
    background-color: white;
    border: 3px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
	}
	select {
		height: 3.5rem;
    background-color: white;
    border: 3px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
	}

	#register-step-2 .input-label {
		left: 4%;
	}
	.register-testimonials {
		padding-left: 5%;
		width: 70%;
		float: left;

		.testimonial-img {
			width: 30%;
			float: left;
		}
		.testimonial-text {
			padding-left: 5%;
			text-align: left;
			width: 70%;
			float: left;

			.text {
				color: $black;
			}
			.bolded {
				font-weight: bold;
			}
		}
	}
}
.authorization-form--compact {
	max-width: 240px;
}

@media screen and (max-width: 370px) {

	.authorization-form {
		.left-float {
			float: none;
		}
		.right-float {
			float: none;
		}
		.forgot-password {
			text-align: center;
		}
	}

}
