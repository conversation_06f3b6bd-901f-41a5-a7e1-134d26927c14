.delete-icon,
.cancel-icon {
  display: block;
  width: 20px;
  height: 20px;
  text-indent: -9999px;
  margin: 0 auto;
  filter: brightness(0%);
  &:hover {
    filter: none;
  }
}

.delete-icon {
  background: asset-data-url("icons/bin.svg") no-repeat center center;
}

.cancel-icon {
  background: asset-data-url("icons/cancel.svg") no-repeat center center;
}

.checked-icon-green,
.edit-icon {
  display: block;
  width: 16px;
  height: 16px;
  text-indent: -9999px;
  margin: 0 auto;
}

.checked-icon-green {
  background: asset-data-url("checked-icon-green.png") no-repeat center center;
}

.edit-icon {
  background: asset-data-url("icons/edit-icon.svg") no-repeat 0 0;
}

.hamburger-menu-icon {
  background:  asset-data-url("icons/hamburger-menu.svg") no-repeat center center;
}
.supplier-menu-dash {
  counter-reset: my-counter;
}

.drag-numbered-icon {
  width: 40px !important;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  &::before {
    counter-increment: my-counter;
    content: counter(my-counter);
  }
}

.drag-reorder-icon {
  background:  asset-data-url("icons/drag-reorder.svg") no-repeat center center;
  min-width: 20px;
  min-height: 20px;
  max-width: 20px;
  max-height: 20px;
  margin-right: 4px;
  cursor: grab;
}

.one-off-icon::before,
.recurrent-icon::before,
.team-order-icon::before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: contain;
}

.one-off-icon::before {
  background-image: asset-data-url("icons/one-off-icon.svg");
}
.recurrent-icon::before {
  background-image: asset-data-url("icons/recurring-icon.svg");
}
.team-order-icon::before {
  background-image: asset-data-url("icons/team-order.svg");
}

.settings-icon {
  &:before {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url("settings-icon.png") no-repeat 0 0;
    position: relative;
    top: 1px;
    margin-right: 0.6rem;
  }
}
.print-icon {
  &:before {
    content: "";
    display: inline-block;
    width: 14px;
    height: 12px;
    background: url("print-icon.png") no-repeat 0 0;
    position: relative;
    top: 1px;
  }
}
.letter-icon {
  display: inline-block;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  line-height: 22px;
  text-align: center;
  font-size: 10px;
  font-weight: bold;
  color: black;
  margin-right: 5px;
  opacity: 0.4;
  &.is_vegetarian {
    background: $green;
  }
  &.is_vegan {
    background: lighten($green-darker, 10%);
  }
  &.is_gluten_free, &.is_dairy_free, &.is_individually_packed {
    background: $yellow;
  }
  &.is_egg_free, &.is_halal, &.is_kosher, &.is_nut_free {
    background: $yellow;
  }
  &.team_order, &.team_order_only {
   background: $medium-gray;
   color: $white;
  }
  &.is_hidden {
    background: $yellow;
    color: red;
    opacity: 0.7;
  }
  &__tooltip {
    transition: background-color 0.5s ease;
    position: relative;
    display: inline-block;
    background: $black;
    color: white;
    border-radius: 3px;
    border: none;
    z-index: 1100;
    padding: 0.2rem 0.4rem;
    max-width: 250px;
    font-size: 14px;
  }
}

.hr-icon,
.he-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-position: 0 0;
  text-indent: -9999px;
}

.hr-icon {
  background-image: asset-data-url("icon-heart.svg");
}

.he-icon {
  background-image: asset-data-url("icon-heart-empty.svg");
}

i.favourite {
  display: inline-flex;
  margin-left: 4px;
}

.icon-alarm::before,
.icon-cost::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

.icon-usage::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 4px;
  background: asset-data-url("icons/copy.svg");
}

.icon-alarm::before {
  background: asset-data-url("icons/alarm.svg");
}

.icon-cost::before {
  background: asset-data-url("icons/cost.svg");
}
