require 'open-uri'
class Customers::MealPlanDetails < YordarPdf
  include Rails.application.routes.url_helpers
  include MenuItemHelper

  DEFAULT_FONT_FAMILY = 'MuseoSlab'.freeze
  SANS_FONT_FAMILY = 'MuseoSans'.freeze
  DEFAULT_FILL_COLOR = '000000'.freeze
  DEFAULT_BORDER_COLOR = 'ACACAC'.freeze

  TABLE_CELL_STYLE = { inline_format: true }.freeze
  DIETARY_COLORS = {
    is_vegan: '006400',
    is_vegetarian: '4A7B26',
    is_gluten_free: '8B4513',
    is_dairy_free: '1E90FF',
    is_individually_packed: 'FF9934',
    is_egg_free: 'F7D5ED',
    is_halal: '24DCA5',
    is_kosher: '1F9E86',
    is_nut_free: '7F53CC'
  }.freeze

  DEFAULT_ACCOUNT_MANAGER = OpenStruct.new(
    name: 'Orders Team',
    email: yordar_credentials(:yordar, :orders_email),
    contact_phone: '+***********'
  ).freeze

  def initialize(meal_plan:, orders:, variation: nil)
    @meal_plan = meal_plan
    @orders = orders
    @variation = variation.presence || 'normal'

    # prawn/pdf setup in YordarPdf
    super(font_family: DEFAULT_FONT_FAMILY)
  end

  def generate
    grid([0, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      briefing_page
      start_new_page
    end
    grid([0, 0], [FOOTER_ROW - 1, LAST_COL]).bounding_box do
      orders.each_with_index do |order, oidx|
        info_for(order)

        start_new_page if oidx < (orders.size - 1)
      end
    end
  end

private

  attr_reader :meal_plan, :orders, :variation

  def report_folder
    'meal-plan-details'
  end

  def report_reference
    if meal_plan.present?
      "#{meal_plan.uuid}/#{Time.zone.now.to_s(:filename)}"
    else
      "#{customer.uuid}/#{Time.zone.now.to_s(:filename)}"
    end
  end

  def briefing_page
    # Add the team-sharing illustration at the top
    illustration_path = image_asset_path_for('illustrations/team-sharing.png')
    if File.exist?(illustration_path)
      image illustration_path, width: bounds.width, height: bounds.height * 0.55, position: :center
    end

    # Create a colored background box for the content
    bounding_box([0, cursor], width: bounds.width, height: bounds.height * 0.27) do
      fill_color 'F5D7F1'
      fill_rectangle [0, cursor], bounds.width, bounds.height
      fill_color DEFAULT_FILL_COLOR

      move_down 20

      logo_svg_path = image_asset_path_for('logo.svg')
      if File.exist?(logo_svg_path) && defined?(svg)
        svg IO.read(logo_svg_path), width: 100, position: :center
      end

      move_down 5

      text_box meal_plan.present? ? 'Curated Meal Plan' : 'Your Orders',
        size: 30,
        style: :extra_bold,
        align: :center,
        at: [0, cursor - 10],
        width: bounds.width

      move_down 45

      text_box "Presented to #{customer.customer_name}",
        size: 20,
        style: :normal,
        align: :center,
        at: [0, cursor - 10],
        width: bounds.width

      # Add secondary heading - check if meal_plan.name exists
      # meal_plan_name = meal_plan.name.to_s
      # text_box meal_plan_name,
      #   size: 20,
      #   style: :normal,
      #   align: :center,
      #   at: [0, cursor - 10],
      #   width: bounds.width

      move_down 40
      # order dates
      delivery_dates = orders.map(&:delivery_at).sort
      current_y = cursor

      calendar_icon_path = image_asset_path_for('icons/icon-calendar.svg')
      if File.exist?(calendar_icon_path) && defined?(svg)
        svg IO.read(calendar_icon_path), at: [(bounds.width / 2) - 100, current_y - 5], width: 20, height: 20
      end

      font SANS_FONT_FAMILY do
        text_box "#{delivery_dates.first.to_s(:date)} to #{delivery_dates.last.to_s(:date)}",
          size: 14,
          at: [(bounds.width / 2) - 70, current_y - 10],
          width: 200,
          align: :left
      end

      move_down 50
    end

    move_down 10
    account_manager_signature
  end

  def account_manager_signature
    bounding_box([0, cursor], width: bounds.width, height: bounds.height * 0.18) do

      text_box 'Managed By',
        size: 18,
        style: :normal,
        align: :center,
        at: [0, cursor - 10],
        width: bounds.width

      move_down 40

      center_x = bounds.width / 2
      column_width = 285
      image_size = 100
      gap = 5

      account_manager_avatar = nil
      if account_manager.id.present? && (manager_avatar = account_manager&.profile&.avatar.presence)
        image_options = {
          width: 200,
          height: 200,
          crop: :fill
        }
        account_manager_avatar = local_image_for(image: manager_avatar, name: "account-manager-#{account_manager.id}", options: image_options)
      end

      if account_manager_avatar.present?
        image account_manager_avatar, at: [center_x - image_size - gap, cursor], width: image_size, height: image_size
      else # fallback to yo-png
        account_manager_avatar = image_asset_path_for('yo-png.png')
        image account_manager_avatar, at: [center_x - image_size - gap, cursor - 20], width: 97, height: 62
      end

      contact_y = cursor
      font SANS_FONT_FAMILY do
        user_icon_path = image_asset_path_for('icons/user-outline.svg')
        if File.exist?(user_icon_path.to_s) && defined?(svg)
          svg IO.read(user_icon_path), at: [center_x + gap, contact_y - 10], width: 16, height: 16
        end

        text_box account_manager.name,
          size: 16,
          at: [center_x + gap + 25, contact_y - 10],
          width: column_width,
          align: :left

        # Email with icon
        email_icon_path = image_asset_path_for('icons/email-outline.svg')
        if File.exist?(email_icon_path.to_s) && defined?(svg)
          svg IO.read(email_icon_path), at: [center_x + gap, contact_y - 40], width: 16, height: 16
        end

        text_box account_manager.email,
          size: 16,
          at: [center_x + gap + 25, contact_y - 40],
          width: column_width,
          align: :left

        # Phone with icon
        phone_icon_path = image_asset_path_for('icons/phone.svg')
        if File.exist?(phone_icon_path.to_s) && defined?(svg)
          svg IO.read(phone_icon_path), at: [center_x + gap, contact_y - 70], width: 16, height: 16
        end

        text_box account_manager.contact_phone,
          size: 16,
          at: [center_x + gap + 25, contact_y - 70],
          width: column_width,
          align: :left
      end
    end
  end

  def info_for(order)
    supplier = order.supplier_profiles.sample
    supplier_banner_for(supplier: supplier)

    order_details_for(order: order, supplier: supplier)

    order_lines_table_for(order: order)
  end

  def supplier_banner_for(supplier:)
    supplier_image = supplier_image_for(supplier).presence || supplier_image_for(yordar_sourcing)
    grid([0, 0], [3, 6]).bounding_box do
      image supplier_image, width: 580, height: 110, position: :center, border: 2
    end
  end

  def order_details_for(order:, supplier:)
    is_quoted_order = order.status == 'quoted'

    # Create centered text blocks for date, supplier name, and order quote

    move_down 8
    # Format date as "Monday 28th April - 11:30am"
    formatted_date = "#{order.delivery_at.strftime('%A %d')}#{order.delivery_at.day.ordinal} #{order.delivery_at.strftime('%B - %l:%M%P')}"

    text formatted_date, size: 14, style: :normal, align: :center

    move_down 6    

    text supplier.name, size: 24, style: :extra_bold, align: :center

    move_down 6
    
    # Get the appropriate icon based on order status
    current_y = cursor

    # Calculate center position for icon and text
    center_x = bounds.width / 2
    text_width = 120 # Increased width to prevent wrapping
    icon_width = 16
    spacing = 5
    # Calculate total width of icon + spacing + text
    total_width = icon_width + spacing + text_width
    # Position icon to start at a point that will center the whole combination
    icon_x = center_x - (total_width / 2)

    # Display the icon
    icon_name = (is_quoted_order ? 'quote-purple.svg' : 'check-circle.svg')
    icon_path = image_asset_path_for("icons/#{icon_name}")
    if File.exist?(icon_path) && defined?(svg)
      svg IO.read(icon_path), at: [icon_x, current_y - 3], width: icon_width, height: 16
    end
    # Position text to start right after the icon
    text_x = icon_x + icon_width + spacing

    fill_color is_quoted_order ? 'AF5FFF' : '1F9E86'

    text_box is_quoted_order ? 'Order Quote' : 'Order Placed',
      size: 14,
      style: :bold,
      align: :left,
      at: [text_x, current_y - 5],
      width: text_width
    fill_color DEFAULT_FILL_COLOR # Reset fill color

    move_down 15

    # Two column layout
    po_number = order.po_number
    column_width = with_pricing? ? bounds.width / 2 : bounds.width
    column_height = po_number.present? ? 110 : 95

    # Left column with order details
    bounding_box([0, cursor], width: column_width, height: column_height) do
      fill_color 'F7F7F7'
      fill_rectangle [0, cursor], column_width, column_height
      fill_color DEFAULT_FILL_COLOR

      # Add padding inside the box
      indent(15, 15) do
        move_down 10

        font SANS_FONT_FAMILY do
          # Order ID
          text_box "Order:",
            size: 11,
            style: :normal,
            at: [0, cursor],
            align: :left
          
          text_box "<b>##{order.id}</b>",
            size: 11,
            style: :normal,
            inline_format: true,
            at: [with_pricing? ? 110 : column_width / 2, cursor]

          move_down 18

          # PO Number if present
          if po_number.present?
            text_box "PO Number:", 
              size: 11, 
              style: :normal,
              at: [0, cursor],
              align: :left
            
            text_box "<b>#{po_number}</b>", 
              size: 11, 
              style: :normal,
              inline_format: true,
              at: [with_pricing? ? 110 : column_width / 2, cursor]
            move_down 18
          end

          # Number of People
          if (number_of_people = order.number_of_people.presence)
            text_box "Number of People:", 
              size: 11, 
              style: :normal,
              at: [0, cursor],
              align: :left
            
            text_box "<b>#{number_of_people.to_s}</b>", 
              size: 11, 
              style: :normal,
              inline_format: true,
              at: [with_pricing? ? 110 : column_width / 2, cursor]
            move_down 18
          end

          # Delivery Address
          text_box "Delivery Address:",
            size: 11,
            style: :normal,
            at: [0, cursor],
            align: :left

          text_box "<b>#{order.delivery_address_arr.join("\n")}</b>",
            size: 11,
            style: :normal,
            inline_format: true,
            at: [with_pricing? ? 110 : column_width / 2, cursor]

          move_down 30
        end
      end
    end

    if with_pricing?
      action_buttons(order: order, column_width: column_width, column_height: column_height)
    end

    # Move cursor below the two columns - reduced space
    move_down 12
  end

  def action_buttons(order:, column_width:, column_height:)
    is_quoted_order = order.status == 'quoted'
    # Right column with actions - positioned directly next to the left column
    bounding_box([column_width, cursor + column_height], width: column_width, height: column_height) do
      stroke_color 'D0D0D0'
      stroke_bounds
      stroke_color DEFAULT_FILL_COLOR

      # Add padding inside the box
      indent(15, 15) do
        move_down 6

        # Actions header
        font SANS_FONT_FAMILY do
          text 'Actions', size: 12
        end

        button_width = column_width - 30
        button_height = 26
        button_spacing = 10

        move_down 14
        
        # Approve/Amend button
        amend_button_config = case
        when order.is_event_order? && is_quoted_order
          {
            label: 'APPROVE',
            link: "mailto:#{account_manager.email}?subject=Approve order ##{order.id}"
          }
        when order.is_event_order?
          nil
        when is_quoted_order
          {
            label: 'APPROVE',
            link: next_app_order_edit_url(order, finaliseQuote: true, host: app_host)
          }
        else
          {
            label: 'AMEND',
            link: next_app_order_edit_url(order, host: app_host)
          }
        end
        
        # Calculate vertical position to center the button group
        total_buttons_height = amend_button_config.present? ? (button_height * 2) + button_spacing : button_height
        start_y_position = cursor - ((column_height - 50 - total_buttons_height) / 2)
        
        if amend_button_config.present?
          button_at = [0, start_y_position]
          fill_color is_quoted_order ? '1F9E86' : 'FFBD3E'
          fill_rounded_rectangle button_at, button_width, button_height, 5
          fill_color 'FFFFFF'
          button_text = "<a href='#{amend_button_config[:link]}'>#{amend_button_config[:label]}</a>"

          font SANS_FONT_FAMILY do
            text_box button_text,
              at: button_at,
              inline_format: true,
              width: button_width,
              height: button_height,
              align: :center,
              valign: :center,
              size: 12,
              style: :bold,
              color: 'FFFFFF'
          end
        
          # Position for the second button
          second_button_y = start_y_position - button_height - button_spacing
        else
          second_button_y = start_y_position
        end

        # Request Changes button
        button_at = [0, second_button_y]
        fill_color 'A3A3A3'
        fill_rounded_rectangle button_at, button_width, button_height, 5
        fill_color 'FFFFFF'

        font SANS_FONT_FAMILY do
          text_box "<a href='mailto:#{account_manager.email}?subject=Changes to order ##{order.id}'>REQUEST CHANGES</a>",
            at: button_at,
            inline_format: true,
            width: button_width,
            height: button_height,
            align: :center,
            valign: :center,
            size: 12,
            style: :bold,
            color: 'FFFFFF'
        end

        # Reset fill color
        fill_color DEFAULT_FILL_COLOR
      end
    end
  end

  def order_lines_table_for(order:)
    order_lines_table = []

    if with_pricing?
      column_widths = [50, 360, 35, 60, 70]
      totals_colspan = 5
    else
      column_widths = [50, 480, 45]
      totals_colspan = 3
    end

    header_row = []
    font SANS_FONT_FAMILY do
      header_row << { content: '', borders: [:bottom], width: column_widths[0], border_color: DEFAULT_BORDER_COLOR }
      header_row << { content: 'Item details & description', borders: [:bottom], width: column_widths[1], border_color: DEFAULT_BORDER_COLOR }
      header_row << { content: 'Qty', borders: [:bottom], align: :center, width: column_widths[2], border_color: DEFAULT_BORDER_COLOR }
      if with_pricing?
        header_row << { content: 'Price', borders: [:bottom], align: :right, width: column_widths[3], border_color: DEFAULT_BORDER_COLOR }
        header_row << { content: 'Total', borders: [:bottom], align: :right, width: column_widths[4], border_color: DEFAULT_BORDER_COLOR }
      end
    end
    order_lines_table << header_row

    # Order lines grouped by location
    order_lines = order_lines_for(order: order)
    font SANS_FONT_FAMILY do
      order_lines.each do |order_line|
        menu_item = order_line.menu_item

        # Handle image or fallback circle with letter
        item_image = item_image_for(menu_item)
        if item_image.present?
          image_cell = {
            content: '',
            image: item_image,
            fit: [50, 50],
            borders: [:bottom],
            border_color: DEFAULT_BORDER_COLOR,
            width: column_widths[0],
            padding: [0, 0, 0, 0] # Remove padding around the image
          }
        else
          # Create a cell with styled text for fallback
          image_cell = {
            content: menu_item.name[0].upcase,
            borders: [:bottom],
            border_color: DEFAULT_BORDER_COLOR,
            width: column_widths[0],
            height: 50,
            align: :center,
            valign: :center,
            background_color: DEFAULT_FILL_COLOR,
            text_color: 'FFFFFF',
            padding: [0, 0, 0, 0] # Remove padding around the fallback
          }
        end

        item_value = order_line.is_gst_free ? '* ' : ''
        item_value += "<b>#{order_line.name}</b>"

        # Add dietary indicators
        dietaries = dietary_preferences_for(menu_item: menu_item)
        if dietaries.present?
          item_value += ' <font size=\'9\'>| '
          dietaries.each do |letter, field|
            item_value += "<color rgb='#{DIETARY_COLORS[field]}'><b>#{letter}</b></color>"
            item_value += ' | '
          end
          item_value += '</font>'
        end

        if order_line.selected_menu_extras.present?
          grouped_extra = OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call
          grouped_extra.each do |section, menu_extras|
            item_value += "\n"
            item_value += "<font size='9'>#{section.name}: #{menu_extras.map(&:name).map(&:strip).join(', ')}</font>"
          end
        end
        if order_line.note.present?
          item_value += "\n"
          item_value += "<font size='8'>Note: <i>#{order_line.note}</i></font>"
        end
        if (description = menu_item.description.presence)
          item_value += "\n\n"
          item_value += "<font size='8'><i>#{description}</i></font>"
        end

        order_line_value = order_line.price_exc_gst(gst_country: order.symbolized_country_code)
        order_line_total_value = order_line_value * order_line.quantity

        order_line_row = []
        order_line_row << image_cell
        order_line_row << { content: item_value, inline_format: true, borders: [:bottom], border_color: DEFAULT_BORDER_COLOR, width: column_widths[1], padding: [5, 5, 5, 5] }
        order_line_row << { content: order_line.quantity.to_s, borders: [:bottom], border_color: DEFAULT_BORDER_COLOR, align: :center, width: column_widths[2], padding: [5, 5, 5, 5] }

        if with_pricing?
          order_line_row << { content: number_to_currency(order_line_value), borders: [:bottom], border_color: DEFAULT_BORDER_COLOR, align: :right, width: column_widths[3], padding: [5, 5, 5, 5] }
          order_line_row << { content: number_to_currency(order_line_total_value), borders: [:bottom], border_color: DEFAULT_BORDER_COLOR, align: :right, width: column_widths[4], padding: [5, 5, 5, 5] }
        end

        order_lines_table << order_line_row
      end # order lines each

      if with_pricing?
        totals_row = []
        ################################################################ Total info
        if (promotion = order.promotion.presence)
          discount_label = "#{promotion.name} (#{promotion.discount_note})"
        else
          discount_label = 'Discount'
        end

        total_row_content = "<b>Subtotal:</b>  #{number_to_currency(order.customer_subtotal)}\n\n"
        total_row_content += "<b>Delivery:</b>  #{number_to_currency(order.customer_delivery)}\n\n" if order.customer_delivery.present? && order.customer_delivery > 0
        total_row_content += "<b>#{discount_label}:</b> -#{number_to_currency(order.discount)}\n\n" if order.discount.present? && order.discount > 0.00
        total_row_content += "<b>GST:</b>  #{number_to_currency(order.customer_gst)}\n\n"
        total_row_content += "<b>Topup:</b>  #{number_to_currency(order.customer_topup)}\n\n" if order.customer_topup.present? && order.customer_topup > 0
        total_row_content += "<b>Surcharge:</b>  #{number_to_currency(order.customer_surcharge)}\n\n" if order.customer_surcharge.present? && order.customer_surcharge > 0

        totals_row << {
          content: "<font name='#{DEFAULT_FONT_FAMILY}'>#{total_row_content}</font>",
          align: :right,
          colspan: totals_colspan,
          borders: [],
          inline_format: true
        }

        order_lines_table << totals_row

        order_lines_table << [
          {
            content: "<font name='#{DEFAULT_FONT_FAMILY}' size='14'><b>Total (inc. GST):</b>  #{number_to_currency(order.customer_total)}</font>",
            align: :right,
            colspan: totals_colspan,
            borders: [:top],
            border_color: DEFAULT_BORDER_COLOR,
            inline_format: true
          }
        ]
      end

      table(order_lines_table) do |table|
        table.cell_style = TABLE_CELL_STYLE
        table.header = true
        table.cell_style = { border_width: 0.5, size: 12 }
      end
    end
  end

  def order_lines_for(order:)
    lister_options = {
      order: order,
      confirmed_attendees_only: order.is_team_order?,
    }
    order_lines = OrderLines::List.new(options: lister_options).call
    order_lines.sort_by do |order_line|
      [
        order_line.location.id,
        order_line.supplier_profile.company_name.downcase,
        order_line.id
      ]
    end
  end

  def order_suppliers
    @_order_suppliers ||= order.supplier_profiles
  end

  def supplier_image_for(supplier)
    image_options = {
      width: 580,
      height: 180,
      crop: :fill
    }
    local_image_for(image: supplier.profile.avatar, name: "supplier-#{supplier.id}", options: image_options)
  end

  def item_image_for(menu_item)
    image_options = {
      width: 50,
      height: 50,
      crop: :fill,
      radius: :max,
      quality: 'auto',
      fetch_format: 'auto'
    }
    local_image_for(image: menu_item.ci_image, name: "menu_item-#{menu_item.id}", options: image_options)
  end

  def dietary_preferences_for(menu_item:)
    dietary_preferences(menu_item: menu_item).map do |field, preference|
      [preference[:letter], field]
    end
  end

  def customer
    @_customer ||= meal_plan&.customer_profile || orders.sample.customer_profile
  end

  def account_manager
    @_account_manager ||= Customers::FetchManagers.new(customer: customer, type: 'account_manager').call.first || DEFAULT_ACCOUNT_MANAGER
  end

  def yordar_sourcing
    @_yordar_sourcing ||= SupplierProfile.where(company_name: 'Yordar Sourcing').first
  end

  def with_pricing?
    variation.blank? || variation != 'no-pricing'
  end

  def pdf_margins
    4.times.map{|_| 10 }
  end

  def pdf_header; end

  def pdf_footer; end

  def supplementary_footer; end

  def with_footer_logo?
    true
  end

  # Helper method to create table cells
  def make_cell(options = {})
    Prawn::Table::Cell.new(options)
  end
end
