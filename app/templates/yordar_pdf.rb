require 'prawn'

class YordarPdf < Prawn::Document
	include ActionView::Helpers::Number<PERSON><PERSON>per

  DEFAULT_PAGE_SIZE = 'A4'.freeze
  DEFAULT_FONT_FAMILY = 'Arial'.freeze
  DEFAULT_PAGE_MARGINS = [50, 35, 40, 35].freeze

	LINE_HEIGHT = 20
	BOX_HEIGHT = 50
	META_FONT_SIZE = 10

	LAST_COL = 6
	LAST_ROW = 28
	FOOTER_ROW = 26

	PAGE_FULL_WIDTH = 555 # page_width 555
	PAGE_PRINT_WIDTH = 525

  BORDER_TB = %i[top bottom].freeze
  BORDER_LB = %i[left bottom].freeze
  BORDER_LRB = %i[left right bottom].freeze
  BORDER_LTB = %i[left top bottom].freeze
  BORDER_LTR = %i[left top right].freeze
  BORDER_TRB = %i[top right bottom].freeze
  BORDER_ALL = %i[left top right bottom].freeze

  LOCATION_COLOR = 'd7ebd5'.freeze
  SUPPLIER_COLOR = 'fefdfb'.freeze
  BACKGROUND_COLOR = 'e3e3e3'.freeze
  LINK_COLOR = '1f9e86'.freeze

  # sets up the PDF variables for PRAWN
	def initialize(*args)
		# initialize with default values

    page_size = args&.first&.dig(:page_size).presence || DEFAULT_PAGE_SIZE
		super page_size: page_size, margin: pdf_margins.presence || DEFAULT_PAGE_MARGINS

		# ===== font family defined =======
		font_families.update('Arial' => {
			normal: "#{Rails.root}/app/assets/fonts/arial.ttf",
			italic: "#{Rails.root}/app/assets/fonts/ariali.ttf",
			bold: "#{Rails.root}/app/assets/fonts/arialbd.ttf",
			bold_italic: "#{Rails.root}/app/assets/fonts/arialbi.ttf"
		})

    font_families.update('MuseoSans' => {
      normal: "#{Rails.root}/app/assets/fonts/museosans_500.ttf",
      italic: "#{Rails.root}/app/assets/fonts/museosans_500.ttf",
      bold: "#{Rails.root}/app/assets/fonts/museosans_700.ttf",
      bold_italic: "#{Rails.root}/app/assets/fonts/museosans_700.ttf",
      extra_bold: "#{Rails.root}/app/assets/fonts/museosans_900-webfont.ttf",
    })

    font_families.update('MuseoSlab' => {
      normal: "#{Rails.root}/app/assets/fonts/museoslab-500-webfont.ttf",
      bold: "#{Rails.root}/app/assets/fonts/museoslab-700-webfont.ttf",
      extra_bold: "#{Rails.root}/app/assets/fonts/museoslab-900-webfont.ttf",
    })

		# font 'Arial'
    font_family = args&.first&.dig(:font_family).presence || DEFAULT_FONT_FAMILY
    font font_family

		define_grid(columns: LAST_COL + 1, rows: LAST_ROW + 1, gutter: 3)

		# ===== do not remove (uncomment for report layout debugging) =======
		# grid.show_all	# show all the grid-boxes that help to find co-ordinate

		# # selective grid-box display
		# (5..25).each do |c|
			# (0..6).each do |r|
				# grid(c,r).show
			# end
		# end

		# Apply header and standard footer to all pages
		repeat :all do
			pdf_header

			# ==== footer =====
			grid([FOOTER_ROW, 0], [LAST_ROW, LAST_COL]).bounding_box do
				supplementary_footer
				pdf_footer
				# transparent(0.5) {stroke_bounds}
			end
		end
		
		# Apply footer logo only to pages after the first one
		repeat(lambda { |pg| pg > 1 }) do
			add_footer_logo if with_footer_logo?
		end
	end

	def generate
		text 'You must override generate() function'
	end

  # kept public for use in Cloudinary upload
  def report_path(ext: '.pdf')
    @_report_path ||= begin
      hashable_text = report_reference + yordar_credentials(:secret_token)
      md5_hash = Digest::MD5.hexdigest(hashable_text)
      "#{report_folder}/#{md5_hash}"
    end + "#{ext}"
  end

	# Adds pagination and delegates call to prawn render
	def render(*args)
    add_pagination if with_pagination?
    super # call prawn to render
	end

	# Adds pagination to pdf, creates folrder and delegates call to prawn render_file
  def render_file
    add_pagination if with_pagination?
    path = prepare_report_folder # creates folder structure for the pdf to save
    super path # call prawn to render_file
  end

  # print the file to local tmp directory
  def file_path
    Rails.root.join(yordar_credentials(:yordar, :report_folder_path), report_path)
  end

private

  # overridable
  def pdf_margins; end

  def pdf_header
    logo_image = "#{Rails.root}/app/assets/images/logo.png"

    grid([0, 0], [2, 2]).bounding_box do
      # place_logo
      move_up 10
      image logo_image, width: 105, height: 30, position: :left, border: 2
      # transparent(0.5) {stroke_bounds}
    end
  end

  def add_footer_logo
    logo_image = "#{Rails.root}/app/assets/images/logo.png"

    grid([LAST_ROW, 0], [LAST_ROW, 2]).bounding_box do
      # place_logo
      move_up 10
      image logo_image, width: 105, height: 30, position: :left, border: 2
      # transparent(0.5) {stroke_bounds}
    end
  end

  def pdf_footer
    text "<b>yordar.com.au</b>, #{yordar_credentials(:yordar, :office, :address_level)},", align: :right, size: META_FONT_SIZE, inline_format: true
    text yordar_credentials(:yordar, :office, :street_address), align: :right, size: META_FONT_SIZE
    text "Tel: #{yordar_credentials(:yordar, :office, :phone)}", align: :right, size: META_FONT_SIZE
    text "ABN: #{yordar_credentials(:yordar, :abn_acn)}", align: :right, size: META_FONT_SIZE
  end

  def supplementary_footer
    text 'You must override supplementary_footer() function'
  end

  # ==== page number =====
  def add_pagination
    grid(LAST_ROW, 3).bounding_box do
      number_pages 'Page <page>/<total>', { align: :center, size: META_FONT_SIZE }
      # transparent(0.5) {stroke_bounds}
    end
  end

  # creates tmp folder structure for PRAWN to write files to
  def prepare_report_folder
    path_to_file = file_path
    # small sanity check for the folder creation
    dir = File.dirname(path_to_file)
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end

    path_to_file
  end

  def local_image_for(image:, name:, options: {})
    return nil if image.blank? || name.blank? || image.match?(/\.avif|\.webp/)

    image = image.remove('image/upload/').split('#').first
    external_url = Cloudinary::Utils.cloudinary_url(image, options).sub('yordar-dev', 'yordar-p')
    filename = Rails.root.join('tmp/pdfs/images', "#{name}.jpg")

    dir = File.dirname(filename)
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end

    if !File.file?(filename)
      open(filename, 'wb') do |file|
        file << URI.open(external_url).read
      end
    end
    filename
  end

  def image_asset_path_for(image_url)
    Rails.root.join('app/assets/images', image_url)
  end

  def with_pagination?
    true
  end

  def with_footer_logo?
    false
  end

  def app_host
    host = Rails.env.development? ? 'http://' : 'https://'
    host += yordar_credentials(:default_host)
    host += ':3000' if Rails.env.development?
    host
  end

  def next_app_host
    host = Rails.env.development? ? 'http://' : 'https://'
    host += "#{yordar_credentials(:next_app_subdomain)}." if yordar_credentials(:next_app_subdomain).present?
    host += yordar_credentials(:default_host).remove('app.').remove('staging.')
    host += ':8080' if Rails.env.development?
    host
  end

end
