:ruby
  has_favourite_customers = current_user&.favourite_customers&.present?
  if is_company_team_admin? && !is_pantry_manager? && !is_account_manager? && !is_yordar_admin?
    customer = current_user.profile.profileable
    customer_invite_link = new_company_customer_registration_url(company_team_admin_code: customer.uuid)
    customer_uuid = customer.uuid
  else
    customer_invite_link = nil
    customer_uuid = nil
  end
  admin_invite_link = is_yordar_admin? ? new_customer_admin_registration_url(adminable_customer_code: '_customer_code') : nil

  can_add_customers = (is_yordar_admin? || session[:original_admin_id].present?) && customer_invite_link.present?

- content_for :header_title, is_company_team_admin? ? 'Users' : 'Customers'

%div{ data: { view_customer_admin: { userName: current_user.name, customerUUID: customer_uuid,  customerInviteLink: customer_invite_link, adminInviteLink: admin_invite_link, googleApiKey: yordar_credentials(:google, :api_key), canManageCustomers: is_yordar_admin?, canAddCustomers: can_add_customers, isSuperAdmin: current_user&.super_admin?, hasFavourites: has_favourite_customers, hasMyCustomers: is_account_manager?, countryCode: request_country_code, customerName: params[:customer_name] }.to_json } }