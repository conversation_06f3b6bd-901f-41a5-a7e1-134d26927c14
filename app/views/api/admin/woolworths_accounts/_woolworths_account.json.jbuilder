json.extract! woolworths_account, :id, :email, :short_name, :active
json.token_expires_at woolworths_account.token_expires_at&.to_s(:full_verbose)

woolworth_order = Woolworths::Order.where(account: woolworths_account, account_in_use: true).includes(:order).last
if woolworth_order.present? && (order = woolworth_order.order.presence)
  customer = order.customer_profile

  json.order do
    json.extract! order, :id, :status
    json.created_at order.created_at.to_s(:full_verbose)
    json.delivery_at order.delivery_at&.to_s(:full_verbose)
    json.delivery_address order.delivery_address_arr.join(', ')
    json.order_lines_count order.order_lines.count
    json.customer_name customer.name
    json.company_name customer.company&.name || customer.company_name
  end
end