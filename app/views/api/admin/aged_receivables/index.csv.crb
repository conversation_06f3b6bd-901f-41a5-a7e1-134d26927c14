header = [ 'first_name', 'last_name', 'email', 'teams' ]
header = ['Company', 'Customer', 'Email', 'Contact Number']
header += Admin::ListAgedReceivables::TIME_FRAMES_MAP.values
header << 'Total'
header << 'Invoices'
csv << header

@aged_receivables.each do |customer, customer_receivables|
  row = []
  if (billing_details = customer&.billing_details.presence)
    row << customer.company&.name || customer.company_name
    row << customer.name
    row << billing_details.email
    row << billing_details.phone
  else
    row += 4.times.map{|_| '-' }
  end

  overdue_total = 0
  customer_invoices = []
  Admin::ListAgedReceivables::TIME_FRAMES_MAP.each do |key, label|
    customer_receivable = customer_receivables.detect{|receivable| receivable.time_frame == key }
    customer_invoices += customer_receivable.invoices
    overdue_total += customer_receivable&.total || 0
    row << customer_receivable&.total
  end

  row << overdue_total
  row << customer_invoices.map(&:number).join(' | ')
  csv << row
end
