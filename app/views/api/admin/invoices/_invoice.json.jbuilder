invoice_customer ||= invoice.customer_profile

payment_status = case
when %w[deleted voided].include?(invoice.status)
  invoice.status
when invoice.payment_status == 'unpaid' && invoice.do_not_notify
  'amended'
else
  invoice.payment_status
end

due_date = payment_status == 'unpaid' && invoice.status == 'confirmed' && !invoice.do_not_notify ? invoice.due_at : nil
is_due = due_date.present? && invoice.due_at >= (Time.zone.now.beginning_of_day + 1.days)
is_overdue = due_date.present? && invoice.due_at <= Time.zone.now.beginning_of_day && invoice.due_at > Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE)

json.extract! invoice, :id, :number, :pushed_to_xero, :do_not_notify

json.payment_status payment_status.upcase
json.total number_to_currency(invoice.amount_price, precision: 2)
json.from_date invoice.from_at&.to_s(:date)
json.to_date invoice.to_at&.to_s(:date)
json.dates [invoice.from_at, invoice.to_at].reject(&:blank?).map{|date| date.to_s(:date) }.uniq
json.due_date due_date&.to_s(:date)

json.is_due is_due
json.is_overdue is_overdue
json.due_distance is_overdue && distance_of_time_in_words(invoice.due_at.beginning_of_day - Time.zone.now.beginning_of_day)

if invoice_customer.present?
  json.customer do
    json.name invoice_customer.customer_name
    json.company_name invoice_customer.company&.name || invoice_customer.company_name
  end
end

if (latest_document = invoice.latest_document.presence)
  json.document do
    json.extract! latest_document, :kind, :name, :url
  end
else
  json.document nil
end