invoice = @invoice
invoice_orders = invoice.invoice_orders.order(delivery_at: :asc, id: :asc)
customer_user = invoice_orders.sample.customer_profile&.user

failed_payment = !invoice.paid? && invoice_orders.any?{|order| order.payment_status == 'error' }
json.failed_payment failed_payment

json.orders invoice_orders.each do |order|
  json.extract! order, :id, :name, :status
  json.delivery_at order.delivery_at.to_s(:full_verbose)
  json.total order.customer_total.present? ? number_to_currency(order.customer_total, precision: 2) : nil

  if order.is_team_order?
    json.show_path sign_in_as_customer_path(customer_user, redirect_path: team_order_path(order))
  else
    json.show_path sign_in_as_customer_path(customer_user, redirect_path: order_show_path(order))
  end
end

if customer_user.present?
  json.customer_invoices_path sign_in_as_customer_path(customer_user, redirect_path: customer_invoices_path)
end

