with_emails ||= false

json.extract! reminder, :id, :title, :message, :frequency, :remindable_type, :remindable_id, :recipients

if (remindable = reminder.remindable.presence)
  json.remindable do
    json.extract! remindable, :name, :email
  end
end

recipients = reminder.recipients.titleize
recipients = recipients.pluralize if %w[account_manager pantry_manager].include?(reminder.recipients)
json.formatted_recipients recipients
json.formatted_frequency reminder.formatted_frequency
json.starting_at reminder.starting_at&.to_s(:full_date)
json.with_emails with_emails
if with_emails
  reminder_emails = reminder.sent_emails.order(sent_at: :desc)
  json.emails reminder_emails.each do |email|
    json.sent_at email.sent_at.to_s(:full_verbose)
  end
end