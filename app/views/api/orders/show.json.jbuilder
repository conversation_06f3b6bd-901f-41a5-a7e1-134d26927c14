order = supplier_order.order
json.isCateringOrder order.order_lines.joins(:category).where(categories: { group: 'catering-services' }).present?
json.isRecurrent order.is_recurrent?
json.extract! order, :id, :status, :delivery_address, :delivery_address_level, :delivery_instruction

woolworths_order = order.woolworths_order.presence
json.isWoolworthsOrder woolworths_order.present?
json.delivery_suburb do
  suburb = order.delivery_suburb
  json.name suburb&.name
  json.state suburb&.state
  json.postcode suburb&.postcode
end

recurrent_orders = supplier_order.recurrent_orders
if recurrent_orders
  json.recurrentOrderDays recurrent_orders.each do |recurrent_order|
    json.extract! recurrent_order, :id, :name
  end
end

if woolworths_order.present?
  json.woolworthsOrderId woolworths_order.id
  json.deliveryWindowId woolworths_order.delivery_window_id
  json.deliveryWindowText woolworths_order.delivery_window_text
end

orders_to_render = order.is_recurrent? && order.status == 'draft' ? supplier_order.recurrent_orders : [order]
order_suppliers = []

if orders_to_render.any?(&:persisted?)
  json.set! :orders do
    orders_to_render.map do |order_to_render|
      json.set! order_to_render.id do
        json.partial! 'api/orders/checkout_order', order: order_to_render, include_empty_locations: order_to_render.status == 'draft'
      end
      order_suppliers += order_to_render.supplier_profiles
    end
  end
end

json.order_suppliers do
  order_suppliers.uniq.map do |supplier|
    json.set! supplier.id do
      json.extract! supplier, :id, :name, :provides_contactless_delivery
      json.menu_link next_app_supplier_show_url(supplier&.slug)
      json.image_id  supplier.profile.avatar
    end
  end
end

if @meal_plan.present? && @meal_plan.admin_notes.present?
  json.mealPlanAdminNotes @meal_plan.admin_notes
end