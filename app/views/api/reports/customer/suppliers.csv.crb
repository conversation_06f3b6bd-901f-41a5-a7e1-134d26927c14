report_label_heading = @report_retriever.options.report_type == 'monthly' ? 'Month' : 'Week Starting'
has_supplier_costs = @report_retriever.options.with_supplier_costs
ethical_flags = SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS

header = [report_label_heading]
header += ['Supplier Name', 'Supplier Spends']
header += ethical_flags.map{|flag| flag.to_s.remove('is_').titleize }
csv << header

@report_retriever.data.each_with_index do |report_result, idx|
  report_label = report_result.key_date[:label]
  supplier_spend_data =  Report::OrderDatum.where(data_kind: 'Supplier', report_datum: report_result.report_data)  
  supplier_spends = {}
  supplier_spend_data.group_by(&:kind).each do |supplier, supplier_data|
    supplier_spends[supplier] = supplier_data.sum(&:total_spend)
    Supplier
  end
  supplier_spends.each do |supplier_name, spends_total|
    supplier_row = []
    supplier_row << report_label
    supplier_row << supplier_name
    supplier_row << spends_total
    supplier = SupplierProfile.where(company_name: supplier_name).first
    if supplier.present?
      ethical_flags.each do |flag|
        supplier_row << supplier.send(flag) ? 'YES' : 'NO'
      end
    else
      supplier_row += ethical_flags.map{|flag| '' }
    end
    csv << supplier_row
  end
end

