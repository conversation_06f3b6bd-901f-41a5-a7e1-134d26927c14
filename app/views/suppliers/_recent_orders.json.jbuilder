orders ||= @supplier_menu.recent_orders
supplier ||= @supplier_menu.supplier
grouped_rate_cards ||=   @supplier_menu.grouped_rate_cards
markup_override ||= @supplier_menu&.markup_override

json.recent_orders orders.each do |order|
  json.extract! order, :id, :name, :delivery_at, :status
  json.delivery_date order.delivery_at.strftime('%a - %d %b %Y')

  lister_options = {
    order: order,
    supplier: supplier,
  }
  order_lines = OrderLines::List.new(options: lister_options).call
  
  json.order_lines order_lines.each do |order_line|
    json.extract! order_line, :id, :menu_item_id, :serving_size_id, :quantity, :note
    json.name order_line.name.gsub(' (with extras)', '')
    menu_item = order_line.menu_item

    # rate card
    item_rate_cards = grouped_rate_cards[order_line.menu_item_id]
    rate_card = case
    when item_rate_cards.blank?
      nil
    when order_line.serving_size_id.present?
      item_rate_cards.detect{|rate_card| rate_card.serving_size_id == order_line.serving_size_id }
    when order_line.menu_item_id.present?
      item_rate_cards.first
    end

    item_price = OrderLines::CalculatePrice.new(order_line: order_line, rate_card: rate_card, markup_override: markup_override).call
    json.price number_to_currency(item_price, precision: 2)
    json.line_total number_with_precision((item_price.round(2) * order_line.quantity), precision: 2)

    grouped_extras = order_line.selected_menu_extras.present? ? OrderLines::FetchSelectedMenuExtras.new(order_line: order_line).call : []
    selected_extras = grouped_extras.map do |section, extras|
      "#{section.name.presence || 'extras'}: #{extras.map(&:name).map(&:strip).join(',')}"
    end
    json.selected_menu_extra_ids order_line.selected_menu_extras
    json.selected_extras selected_extras
    json.not_available menu_item.archived_at.present? || order_line.serving_size&.archived_at.present?

    if menu_item.image?
      json.image Cloudinary::Utils.cloudinary_url(menu_item.ci_image, width: 100, height: 100, crop: 'fill', quality: 'auto', fetch_format: 'auto')
    else
      json.image nil
    end
  end
end