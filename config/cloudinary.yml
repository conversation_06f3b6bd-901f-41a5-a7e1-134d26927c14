---
# sandbox: &sandbox # saved with EmpireOne I presume - VM
#   cloud_name: yordar-d
#   api_key: '682459832441422'
#   api_secret: lUc5K7sqLj6j6wpC_u-TIE3d-bQ
#   enhance_image_tag: true
#   static_image_support: false
sandbox: &sandbox
  cloud_name: yordar-dev
  api_key: 955616158238441
  api_secret: hWKvz3JtKqjL7JFhDNPOtFZZ0ZA
  enhance_image_tag: true
  static_image_support: false
  upload_url: 'https://api.cloudinary.com/v1_1/yordar-dev/image/upload'
  upload_preset: 'tzo8vi5a'
live: &live
  cloud_name: yordar-p
  api_key: '798421961825869'
  api_secret: SBIdOMYdSCSQs6Z5xqkTdvBspnw
  enhance_image_tag: true
  static_image_support: true
  secure: true
  upload_url: 'https://api.cloudinary.com/v1_1/yordar-p/image/upload'
  upload_preset: 'dul2av7b'
development:
  <<: *sandbox
test:
   <<: *sandbox
production:
  <<: *live
staging:
   <<: *live

