require 'rails_helper'

RSpec.describe Orders::Adjust, type: :service, orders: true do

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order) { create(:order, :delivered, customer_profile: customer, order_type: 'one-off') }

  let!(:order_params) { { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days } }

  before do
    # mock document generator
    document_generator = delayed_document_generator = double(Documents::Generate::CustomerOrderDetails)
    allow(Documents::Generate::CustomerOrderDetails).to receive(:new).and_return(document_generator)
    allow(document_generator).to receive(:delay).and_return(delayed_document_generator)
    allow(delayed_document_generator).to receive(:call).and_return(true)

    # mock notifications sender
    notifications_sender = delayed_notifications_sender = double(Orders::Notifications::SendOrderAdjustedSupplierNotifications)
    allow(Orders::Notifications::SendOrderAdjustedSupplierNotifications).to receive(:new).and_return(notifications_sender)
    allow(notifications_sender).to receive(:delay).and_return(delayed_notifications_sender)
    allow(delayed_notifications_sender).to receive(:call).and_return(true)
  end

  it 'changes the attributes as passed' do
    order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer).call
    expect(order_adjuster).to be_success

    adjusted_order = order_adjuster.order
    expect(adjusted_order.id).to eq(order.id) # its the same order
    expect(adjusted_order.name).to eq(order_params[:name])
    expect(adjusted_order.delivery_at).to eq(order_params[:delivery_at])
    expect(adjusted_order.status).to eq('delivered')
  end

  it 'changes a recurring order details as is without changing order_type' do
    order.update_columns(order_type: 'recurrent')
    expect(order.is_recurrent?).to be_truthy

    order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer).call
    expect(order_adjuster).to be_success

    adjusted_order = order_adjuster.order
    expect(adjusted_order.is_recurrent?).to be_truthy
  end

  context 'an already invoiced order' do
    let!(:invoice) { create(:invoice, :random, customer_profile: customer) }

    before do
      order.update_column(:invoice_id, invoice.id)
    end

    it 'doesn\'t allow an already invoiced order to be adjusted' do
      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer).call

      expect(order_adjuster).to_not be_success
      expect(order_adjuster.errors).to include('Invoice Cannot be saved as invoice has been issued')
    end

    it 'allow an admin to adjust an already invoiced order to be adjusted' do
      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer, is_admin: true).call

      expect(order_adjuster).to be_success      
    end
  end

  context 'Wooloworths order', woolworths: true do
    let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

    it 'changes a woolworths order with passed in attributes' do
      woolworths_order_id = SecureRandom.hex(7)
      woolworths_order_params = order_params.merge({ associated_woolworths_order_attributes: { id: woolworths_order.id, woolworths_order_id: woolworths_order_id } })
      order_adjuster = Orders::Adjust.new(order: order, order_params: woolworths_order_params, customer: customer).call

      expect(order_adjuster).to be_success

      adjusted_order = order_adjuster.order
      expect(adjusted_order.id).to eq(order.id) # its the same order
      expect(adjusted_order.status).to eq('delivered')
      expect(woolworths_order.reload.woolworths_order_id).to eq(woolworths_order_id)
    end
  end
  
  it 're-generates the customer order details PDF' do
    expect(Documents::Generate::CustomerOrderDetails).to receive(:new).with(order: order)

    order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer).call
    expect(order_adjuster).to be_success
  end

  it 'sends an order-adjusted email to all of its suppliers' do
    expect(Orders::Notifications::SendOrderAdjustedSupplierNotifications).to receive(:new).with(order: order)

    order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer).call
    expect(order_adjuster).to be_success
  end

  context 'with purchase order detials', purchase_orders: true do
    let!(:customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

    it 'adds the purchase order based on passed in ID' do
      order_params_with_cpo = order_params.merge({ cpo_id: customer_purchase_order.id })
      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params_with_cpo, customer: customer).call

      expect(order_adjuster).to be_success
      adjusted_order = order_adjuster.order
      expect(adjusted_order.cpo_id).to eq(customer_purchase_order.id)
      expect(adjusted_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'saves the purchase order based on passed in PO number' do
      order_params_with_cpo = order_params.merge({ cpo_id: customer_purchase_order.po_number })
      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params_with_cpo, customer: customer).call

      expect(order_adjuster).to be_success
      submitted_order = order_adjuster.order
      expect(submitted_order.cpo_id).to eq(customer_purchase_order.id)
      expect(submitted_order.po_number).to eq(customer_purchase_order.po_number)
    end

    it 'creates and saves a new customer purchase order based on passed in PO number' do
      order_params_with_cpo = order_params.merge({ cpo_id: customer_purchase_order.po_number + SecureRandom.hex(2) })
      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params_with_cpo, customer: customer).call

      expect(order_adjuster).to be_success
      submitted_order = order_adjuster.order
      expect(submitted_order.customer_purchase_order).to be_present
      expect(submitted_order.customer_purchase_order.customer_profile).to eq(order.customer_profile)
      expect(submitted_order.po_number).to eq(order_params_with_cpo[:cpo_id])
    end

    it 'removes an exsiting purchase order if not passed in params' do
      order.update_column(:cpo_id, customer_purchase_order.id)

      order_params_with_no_cpo = order_params.merge({ cpo_id: nil })
      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params_with_no_cpo, customer: customer).call

      expect(order_adjuster).to be_success
      submitted_order = order_adjuster.order
      expect(submitted_order.customer_purchase_order).to be_blank
    end
  end

  context 'errors' do
    it 'can only adjust a delivered order' do
      order.update_column(:status, %w[new amended confirmed skipped cancelled].sample)

      order_adjuster = Orders::Adjust.new(order: order, order_params: order_params, customer: customer).call

      expect(order_adjuster).to_not be_success
      expect(order_adjuster.errors).to include('Can only adjust a delivered order')
    end
  end

end
