require 'rails_helper'

RSpec.describe Orders::Amend, type: :service, orders: true do

  let!(:customer) { create(:customer_profile, :random) }

  let!(:lead_time_fetcher) { double(Orders::GetMaximumLeadTime) }
  let!(:supplier_spends_fetcher) { double(Orders::GetSupplierSpends) }

  before do
    # mock that account manager notifications
    account_mananger_notifications_sender = delayed_account_mananger_notifications_sender = double(Orders::Notifications::NotifyAccountManagers)
    allow(Orders::Notifications::NotifyAccountManagers).to receive(:new).and_return(account_mananger_notifications_sender)
    allow(account_mananger_notifications_sender).to receive(:delay).and_return(delayed_account_mananger_notifications_sender)
    allow(delayed_account_mananger_notifications_sender).to receive(:call)

    # mock lead time fetcher
    allow(Orders::GetMaximumLeadTime).to receive(:new).and_return(lead_time_fetcher)
    valid_lead_time_response = OpenStruct.new(can_process?: true)
    allow(lead_time_fetcher).to receive(:call).and_return(valid_lead_time_response)

    # mock supplier spends
    allow(Orders::GetSupplierSpends).to receive(:new).and_return(supplier_spends_fetcher)
    valid_supplier_spend_response = OpenStruct.new(is_under?: false)
    allow(supplier_spends_fetcher).to receive(:call).and_return(valid_supplier_spend_response)

    # mock event log creator
    event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:call).and_return(true)

    # mock saving major category
    major_category_saver = delayed_major_category_saver = double(Orders::RetrieveMajorOrderCategory)
    allow(Orders::RetrieveMajorOrderCategory).to receive(:new).and_return(major_category_saver)
    allow(major_category_saver).to receive(:delay).and_return(delayed_major_category_saver)
    allow(delayed_major_category_saver).to receive(:call).and_return(true)
  end

  context 'one-off order' do
    let!(:order) { create(:order, :new, customer_profile: customer, order_type: 'one-off') }

    it 'amends the attributes as passed' do
      order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days }
      order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

      expect(order_amender).to be_success

      amended_order = order_amender.order
      expect(amended_order.id).to eq(order.id) # its the same order
      expect(amended_order.name).to eq(order_params[:name])
      expect(amended_order.delivery_at).to eq(order_params[:delivery_at])
      expect(amended_order.status).to eq('amended')
    end

    context 'Wooloworths order', woolworths: true do
      let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

      it 'amends a woolworths order with passed in attributes' do
        woolworths_order_id = SecureRandom.hex(7)
        order_params = { associated_woolworths_order_attributes: { id: woolworths_order.id, woolworths_order_id: woolworths_order_id } }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success

        amended_order = order_amender.order
        expect(amended_order.id).to eq(order.id) # its the same order
        expect(amended_order.status).to eq('amended')
        expect(woolworths_order.reload.woolworths_order_id).to eq(woolworths_order_id)
      end
    end

    context 'with order lines' do
      let!(:supplier1) { create(:supplier_profile, :random) }
      let!(:supplier2) { create(:supplier_profile, :random) }

      let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: supplier1) }
      let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: supplier2) }

      before do
        notifications_sender = delayed_notifications_sender = double(Orders::Notifications::SendOrderAmendedSupplierNotifications)
        allow(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).and_return(notifications_sender)
        allow(notifications_sender).to receive(:delay).and_return(delayed_notifications_sender)
        allow(delayed_notifications_sender).to receive(:call).and_return(true)
      end

      it 'sends an order-amended email to all of its suppliers' do
        expect(Orders::Notifications::SendOrderAmendedSupplierNotifications).to receive(:new).with(order: order, notify_now: true)

        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call
        expect(order_amender).to be_success
      end
    end

    it 'notifies account managers' do
      expect(Orders::Notifications::NotifyAccountManagers).to receive(:new).with(order: order)

      order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days }
      order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call
      expect(order_amender).to be_success
    end

    context 'Event Logs', event_logs: true do
      let!(:order_params) { { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days } }
      it 'does not request to create an Event Log for order amended without any errors' do
        expect(EventLogs::Create).to_not receive(:new).with(event_object: order, event: anything, severity: anything)

        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call
        expect(order_amender).to be_success
      end

      context 'when order is amended after supplier cutoff' do
        before do
          invalid_lead_time_response = OpenStruct.new(can_process?: false)
          allow(lead_time_fetcher).to receive(:call).and_return(invalid_lead_time_response)
        end

        it 'request an Event Log for order amended be created with additional lead time info' do
          expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'order-amended', severity: 'warning', after_cutoff: true)

          order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call
          expect(order_amender).to be_success
        end
      end # Order within supplier lead time

      context 'when order is amended below supplier minimum spends' do
        let!(:supplier) { create(:supplier_profile, :random) }
        let!(:supplier_spend) { SupplierSpend.new(supplier: supplier, total_spend: 100, minimum_spend: 200) }

        before do
          invalid_spend_response = OpenStruct.new(is_under?: true, supplier_spends: [supplier_spend])
          allow(supplier_spends_fetcher).to receive(:call).and_return(invalid_spend_response)
        end

        it 'request an Event Log for order amended be created with additional spends info' do
          expected_supplier_spend = {
            name: supplier.name,
            minimum: supplier_spend.minimum_spend,
            remaining: (supplier_spend.minimum_spend - supplier_spend.total_spend),
          }
          expect(EventLogs::Create).to receive(:new).with(event_object: order, event: 'order-amended', severity: 'warning', under_supplier_minimum: true, supplier_spends: [expected_supplier_spend])

          order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call
          expect(order_amender).to be_success
        end
      end # Order below supplier minimums

      it 'makes a request to save the major category of an order' do
        expect(Orders::RetrieveMajorOrderCategory).to receive(:new).with(order: order, save_category: true)

        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call
        expect(order_amender).to be_success
      end
    end # Event Logs

    context 'with purchase order detials', purchase_orders: true do
      let!(:customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

      it 'adds the purchase order based on passed in ID' do
        order_params = { cpo_id: customer_purchase_order.id }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.cpo_id).to eq(customer_purchase_order.id)
        expect(amended_order.po_number).to eq(customer_purchase_order.po_number)
      end

      it 'saves the purchase order based on passed in PO number' do
        order_params = { cpo_id: customer_purchase_order.po_number }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.cpo_id).to eq(customer_purchase_order.id)
        expect(amended_order.po_number).to eq(customer_purchase_order.po_number)
      end

      it 'creates and saves a new customer purchase order based on passed in PO number' do
        order_params = { cpo_id: customer_purchase_order.po_number + SecureRandom.hex(2) }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.customer_purchase_order).to be_present
        expect(amended_order.customer_purchase_order.customer_profile).to eq(order.customer_profile)
        expect(amended_order.po_number).to eq(order_params[:cpo_id])
      end

      it 'removes an exsiting purchase order if not passed in params' do
        order.update_column(:cpo_id, customer_purchase_order.id)

        order_params = { cpo_id: nil }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.customer_purchase_order).to be_blank
      end
    end # with PO

    context 'with GST-free purchase order detials', purchase_orders: true do
      let!(:gst_free_customer_purchase_order) { create(:customer_purchase_order, customer_profile: customer, po_number: SecureRandom.hex(7)) }

      it 'adds the purchase order based on passed in ID' do
        order_params = { gst_free_cpo_id: gst_free_customer_purchase_order.id }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.gst_free_cpo_id).to eq(gst_free_customer_purchase_order.id)
        expect(amended_order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
      end

      it 'saves the purchase order based on passed in PO number' do
        order_params = { gst_free_cpo_id: gst_free_customer_purchase_order.po_number }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.gst_free_cpo_id).to eq(gst_free_customer_purchase_order.id)
        expect(amended_order.gst_free_po_number).to eq(gst_free_customer_purchase_order.po_number)
      end

      it 'creates and saves a new customer purchase order based on passed in PO number' do
        order_params = { gst_free_cpo_id: gst_free_customer_purchase_order.po_number + SecureRandom.hex(2) }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.gst_free_customer_purchase_order).to be_present
        expect(amended_order.gst_free_customer_purchase_order.customer_profile).to eq(order.customer_profile)
        expect(amended_order.gst_free_po_number).to eq(order_params[:gst_free_cpo_id])
      end

      it 'removes an exsiting purchase order if not passed in params' do
        order.update_column(:gst_free_cpo_id, gst_free_customer_purchase_order.id)

        order_params = { gst_free_cpo_id: nil }
        order_amender = Orders::Amend.new(order: order, order_params: order_params, customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.gst_free_customer_purchase_order).to be_blank
      end
    end # with GST Free PO
  end

  context 'recurrent orders' do
    let!(:order1) { create(:order, :new) }
    let!(:order2) { create(:order, :new) }
    let!(:order3) { create(:order, :new) }

    before do
      [order1, order2, order3].each_with_index do |order, idx|
        new_delivery_at = order1.delivery_at + idx.weeks
        order.update_columns(recurrent_id: order1.id, template_id: order1.id, order_type: 'recurrent', delivery_at: new_delivery_at, pattern: '1.week')
      end
    end

    context 'save as one-off' do
      it 'amends the attributes as passed' do
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'one-off', customer: customer).call

        expect(order_amender).to be_success

        amended_order = order_amender.order
        expect(amended_order.id).to eq(order2.id)
        expect(amended_order.name).to eq(order_params[:name])
        expect(amended_order.delivery_at).to eq(order_params[:delivery_at])
        expect(amended_order.status).to eq('amended')
      end

      it 'does not update subsequent or previous orders, if changed order not a template' do
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'one-off', customer: customer).call

        expect(order_amender).to be_success

        [order1, order3].each(&:reload)
        expect(order1.name).to_not eq(order_params[:name])
        expect(order3.name).to_not eq(order_params[:name])

        expect(order1.delivery_at).to_not eq(order_params[:delivery_at])
        expect(order3.delivery_at).to_not eq(order_params[:delivery_at])

        expect(order1.template_id).to eq(order1.id)
        expect(order3.template_id).to eq(order1.id)
      end

      it 'updates subsequent orders as a one off edit for a template order' do # tested in Orders::Recurring::SaveAsOneOff spec
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        order_amender = Orders::Amend.new(order: order1, order_params: order_params, mode: 'one-off', customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.template_id).to eq(order1.id)

        [order2, order3].each(&:reload)
        # no changes in data
        expect(order2.name).to_not eq(order_params[:name])
        expect(order3.name).to_not eq(order_params[:name])
        expect(order2.delivery_at).to_not eq(order_params[:delivery_at])
        expect(order3.delivery_at).to_not eq(order_params[:delivery_at])

        # template is changed to next available
        expect(order2.template_id).to eq(order2.id)
        expect(order3.template_id).to eq(order2.id)
      end

      it 'does not update template of a non-template one-off change' do # tested in Orders::Recurring::SaveAsOneOff spec
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'one-off', customer: customer).call

        expect(order_amender).to be_success
        amended_order = order_amender.order
        expect(amended_order.template_id).to eq(order1.id) # does not change template
      end
    end

    context 'save subsequent' do
      it 'amends the attributes of the orders as passed' do
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'subsequent', customer: customer).call

        expect(order_amender).to be_success

        amended_order = order_amender.order
        expect(amended_order.id).to eq(order2.id)
        expect(amended_order.name).to eq(order_params[:name])
        expect(amended_order.delivery_at).to eq(order_params[:delivery_at])
        expect(amended_order.status).to eq('amended')
      end

      it 'updates attributes of subsequent orders as passed' do # tested in Orders::Recurring::UpdateSubsequentOrders spec
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 10.days }
        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'subsequent', customer: customer).call

        expect(order_amender).to be_success

        [order1, order3].each(&:reload)
        expect(order3.name).to eq(order_params[:name])

        expect(order1.name).to_not eq(order_params[:name]) # no change to previous orders
      end

      it 'updates delivery_at of subsequent orders as calculated using difference between originial order delivery_at and new delivery_at' do # tested in Orders::Recurring::UpdateSubsequentOrders spec
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        difference = (order_params[:delivery_at] - order2.delivery_at.dup)
        expected_delivery = order3.delivery_at.dup + difference

        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'subsequent', customer: customer).call

        expect(order_amender).to be_success

        [order1, order3].each(&:reload)
        expect(order3.delivery_at.to_s).to eq(expected_delivery.to_s)

        expect(order1.delivery_at).to eq(order1.delivery_at) # no change to previous orders
      end

      it 'updates template of subsequent orders only' do # tested in Orders::Recurring::UpdateSubsequentOrders spec
        order_params = { name: Faker::Name.name, delivery_at: Time.zone.now + 13.days }
        order_amender = Orders::Amend.new(order: order2, order_params: order_params, mode: 'subsequent', customer: customer).call

        expect(order_amender).to be_success
        [order1, order3].each(&:reload)

        amended_order = order_amender.order
        expect(amended_order.template_id).to eq(order2.id) # sets new template
        expect(order3.template_id).to eq(order2.id)

        expect(order1.template_id).to eq(order1.id) # no change to previous order
      end
    end
  end
end
