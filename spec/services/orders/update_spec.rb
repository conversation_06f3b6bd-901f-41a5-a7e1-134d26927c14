require 'rails_helper'

RSpec.describe Orders::Update, type: :service, orders: true do

  let!(:order) { create(:order, :draft) }
  let!(:suburb) { create(:suburb, :random) }

  it 'updates the order delivery charge' do
    order_params = { no_delivery_charge: true }
    order_updater = Orders::Update.new(order: order, order_params: order_params).call

    expect(order_updater).to be_success
    expect(order.reload).to be_no_delivery_charge
  end

  it 'updates delivery details' do
    order_params = { delivery_address_level: Faker::Address.building_number, delivery_address: Faker::Address.street_address, delivery_suburb_id: suburb.id, delivery_instruction: Faker::Lorem.sentence, delivery_at: Time.zone.now }
    order_updater = Orders::Update.new(order: order, order_params: order_params).call

    expect(order_updater).to be_success
    order.reload
    expect(order.delivery_address_level).to eq(order_params[:delivery_address_level])
    expect(order.delivery_address).to eq(order_params[:delivery_address])
    expect(order.delivery_suburb).to eq(suburb)
    expect(order.delivery_instruction).to eq(order_params[:delivery_instruction])
    expect(order.delivery_at).to eq(order_params[:delivery_at])
  end

  it 'updates delivery details from passed in suburb' do
    another_suburb = create(:suburb, :random)
    order_params = { delivery_address: Faker::Address.street_address, delivery_suburb_id: suburb.id }
    order_updater = Orders::Update.new(order: order, order_params: order_params, suburb: another_suburb).call

    expect(order_updater).to be_success
    order.reload
    expect(order.delivery_suburb).to_not eq(suburb)
    expect(order.delivery_suburb).to eq(another_suburb)
  end

  context 'with order_supplier details' do
    let!(:supplier) { create(:supplier_profile, :random) }
    let!(:order_supplier_params) do
      {
        supplier_profile_id: supplier.id,
        delivery_fee_override: rand(20.9..40.2),
      }
    end

    let!(:order_params) { { order_supplier: order_supplier_params } }

    it 'creates order_supplier records for the order with passed in info' do
      expect(order.order_suppliers).to be_blank
      order_updater = Orders::Update.new(order: order, order_params: order_params).call

      expect(order_updater).to be_success
      expect(order_updater.order.order_suppliers).to be_present

      created_order_supplier = order_updater.order.order_suppliers.first
      expect(created_order_supplier.supplier_profile).to eq(supplier)
      expect(created_order_supplier.delivery_fee_override.round(2).to_s).to eq(order_supplier_params[:delivery_fee_override].round(2).to_s)
    end

    context 'order status' do
      it 'updates the order status to amended if original order status is any of new/confirmed/skipped/paused/cancelled' do
        order.update_column(:status, %w[new confirmed skipped paused cancelled].sample)

        order_updater = Orders::Update.new(order: order, order_params: order_params).call
        expect(order_updater).to be_success

        updated_order = order_updater.order
        expect(updated_order.status).to eq('amended')
      end

      it 'does not update order status to amended order status is not new/confirmed/skipped/paused/cancelled' do
        order.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[new confirmed skipped paused cancelled amended]).sample)

        order_updater = Orders::Update.new(order: order, order_params: order_params).call
        expect(order_updater).to be_success

        updated_order = order_updater.order
        expect(updated_order.status).to_not eq('amended')
      end
    end

    context 'with existing order supplier' do
      let!(:order_supplier) { create(:order_supplier, :random, supplier_profile: supplier, order: order) }
      let!(:order_supplier_params) do
        {
          id: order_supplier.id,
          supplier_profile_id: supplier.id,
          delivery_fee_override: rand(20.9..40.2),
        }
      end

      it 'updates the order_supplier record for the order with passed in info' do
        order_updater = Orders::Update.new(order: order, order_params: order_params).call

        expect(order_updater).to be_success
        expect(order_updater.order.order_suppliers).to be_present

        updated_order_supplier = order_updater.order.order_suppliers.first
        expect(updated_order_supplier.id).to eq(order_supplier.id)
        expect(updated_order_supplier.delivery_fee_override.round(2).to_s).to eq(order_supplier_params[:delivery_fee_override].round(2).to_s)
      end

      it 'updates the order_supplier record even without the passed in order_supplier ID' do
        non_id_order_params = order_params.merge({ order_supplier: order_supplier_params.except(:id) })
        order_updater = Orders::Update.new(order: order, order_params: non_id_order_params).call

        expect(order_updater).to be_success
        expect(order_updater.order.order_suppliers).to be_present

        updated_order_supplier = order_updater.order.order_suppliers.first
        expect(updated_order_supplier.id).to eq(order_supplier.id)
        expect(updated_order_supplier.delivery_fee_override.round(2).to_s).to eq(order_supplier_params[:delivery_fee_override].round(2).to_s)
      end

      it 'errors out if pass in supplier ID is different to stored order supplier\'s supplier ID' do
        supplier2 = create(:supplier_profile, :random)
        non_id_order_params = order_params.merge({ order_supplier: order_supplier_params.merge({ supplier_profile_id: supplier2.id }) })
        order_updater = Orders::Update.new(order: order, order_params: non_id_order_params).call

        expect(order_updater).to_not be_success
        expect(order_updater.errors).to include('Anomalous order supplier update')
      end
    end
  end

  context 'Woolworths order', woolworths: true do
    context 'with is_woolworths_order param' do
      let!(:woolworths_order_params) do
        {
          delivery_address_level: rand(1..10).to_s,
          delivery_address: Faker::Address.street_address,
          delivery_suburb_id: suburb.id,
          delivery_at: Time.zone.now + 2.days,
          is_woolworths_order: true
        }
      end

      let!(:woolworths_order_attacher) { double(Woolworths::Order::AttachToOrder) }
      let!(:woolworths_cart_emptyer) { double(Woolworths::API::EmptyTrolley) }

      before do
        # mock woolworths order attacher
        allow(Woolworths::Order::AttachToOrder).to receive(:new).and_return(woolworths_order_attacher)
        allow(woolworths_order_attacher).to receive(:call).and_return(true)

        # mock woolworths cart emptyer
        allow(Woolworths::API::EmptyTrolley).to receive(:new).and_return(woolworths_cart_emptyer)
        allow(woolworths_cart_emptyer).to receive(:call).and_return(true)

        # mock woolworths delivery details processor
        woolworths_details_processor = delayed_woolworths_details_processor = double(Woolworths::ProcessOrderDeliveryDetails)
        allow(Woolworths::ProcessOrderDeliveryDetails).to receive(:new).and_return(woolworths_details_processor)
        allow(woolworths_details_processor).to receive(:delay).and_return(delayed_woolworths_details_processor)
        allow(delayed_woolworths_details_processor).to receive(:call).and_return(true)

        # mock woolworths order lines syncer
        order_lines_syncer = double(Woolworths::Order::SyncOrderLines)
        allow(Woolworths::Order::SyncOrderLines).to receive(:new).and_return(order_lines_syncer)
        allow(order_lines_syncer).to receive(:call).and_return(true)
      end

      it 'attaches a Woolworths Order to the order' do
        expect(Woolworths::Order::AttachToOrder).to receive(:new).with(order: order)

        order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call
        expect(order_updater).to be_success
      end

      it 'requests the Woolworths Cart to be emptyied' do
        expect(Woolworths::API::EmptyTrolley).to receive(:new).with(order: order)

        order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call
        expect(order_updater).to be_success
      end

      it 'requests Woolworths Process Delivery Details' do
        expect(Woolworths::ProcessOrderDeliveryDetails).to receive(:new).with(order: order)

        order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call
        expect(order_updater).to be_success
      end

      it 'requests Woolworths Order Lines to be Synced' do
        expect(Woolworths::Order::SyncOrderLines).to receive(:new).with(order: order)

        order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call
        expect(order_updater).to be_success
      end

      it 'does not process Woolworths Order if any delivery details are misssing' do
        missing_field = %i[delivery_address delivery_suburb_id delivery_at].sample
        order.update_column(missing_field, nil)

        expect(Woolworths::Order::AttachToOrder).to_not receive(:new)
        expect(Woolworths::API::EmptyTrolley).to_not receive(:new)
        expect(Woolworths::ProcessOrderDeliveryDetails).to_not receive(:new)
        expect(Woolworths::Order::SyncOrderLines).to_not receive(:new)

        order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params.except(missing_field)).call
        expect(order_updater).to_not be_success
      end

      it 'does not process Woolworths Order if order already is attached to a Woolworths order' do
        create(:woolworths_order, :random, order: order) # attach woolworths_order to the order
        order.reload

        expect(Woolworths::Order::AttachToOrder).to_not receive(:new)
        expect(Woolworths::API::EmptyTrolley).to_not receive(:new)
        expect(Woolworths::ProcessOrderDeliveryDetails).to_not receive(:new)
        expect(Woolworths::Order::SyncOrderLines).to_not receive(:new)

        order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call
        expect(order_updater).to be_success
      end

      context 'with Woolworths errors' do
        before do
          no_available_account_error = [true, false].sample
          connection_error = [true, false].sample
          no_account_error = [true, false].sample

          case
          when no_available_account_error
            allow(woolworths_order_attacher).to receive(:call).and_raise(Woolworths::Order::AttachToOrder::NoAccountsAvailableError.new)
          when connection_error
            allow(woolworths_cart_emptyer).to receive(:call).and_raise(Woolworths::API::Connection::ConnectionError.new)
          when no_account_error
            allow(woolworths_cart_emptyer).to receive(:call).and_raise(Woolworths::API::Connection::NoAccountError.new)
          else
            allow(woolworths_cart_emptyer).to receive(:call).and_raise(Woolworths::API::EmptyTrolley::EmptyTrolleyError.new)
          end
        end

        it 'returns with errors' do
          order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call

          expect(order_updater).to_not be_success
          expect(order_updater.errors).to include('The Woolworths monkeys are busy right now, please try again in a few minutes')
        end

        it 'does not process Woolworths delivery details' do
          expect(Woolworths::ProcessOrderDeliveryDetails).to_not receive(:new)
          order_updater = Orders::Update.new(order: order, order_params: woolworths_order_params).call

          expect(order_updater).to_not be_success
        end
      end # Woolworths Errors
    end # is_woolworths_order param

    context 'with attached woolworths order' do
      let!(:woolworths_order) { create(:woolworths_order, :random, order: order) }

      before do
        delivery_setter = double(Woolworths::API::SetDeliveryWindow)
        allow(Woolworths::API::SetDeliveryWindow).to receive(:new).and_return(delivery_setter)
        allow(delivery_setter).to receive(:call).and_return(true)
      end

      context 'with a passed in delivery window ID' do
        let!(:delivery_window_id) { rand(1000) }
        let!(:order_params) do
          {
            associated_woolworths_order_attributes: {
              id: woolworths_order.id,
              delivery_window_id: delivery_window_id
            }
          }
        end

        it 'updates woolworths_order\'s delivery window' do
          order_updater = Orders::Update.new(order: order, order_params: order_params).call

          expect(order_updater).to be_success
          woolworths_order.reload
          expect(woolworths_order.delivery_window_id).to eq(delivery_window_id)
        end

        it 'makes a request to set the delivery window in Woolworths' do
          expect(Woolworths::API::SetDeliveryWindow).to receive(:new).with(order: order)

          order_updater = Orders::Update.new(order: order, order_params: order_params).call
          expect(order_updater).to be_success
        end
      end

      it 'updates woolworths_order\'s Woolworths Order ID data' do
        woolworths_order_id = SecureRandom.hex(7)
        order_params = { associated_woolworths_order_attributes: { id: woolworths_order.id, woolworths_order_id: woolworths_order_id } }
        order_updater = Orders::Update.new(order: order, order_params: order_params).call

        expect(order_updater).to be_success
        woolworths_order.reload
        expect(woolworths_order.woolworths_order_id).to eq(woolworths_order_id)
      end
    end
  end

  context 'for order with dear suppliers', dear: true do
    let(:dear_supplier) { create(:supplier_profile, :random, :with_dear_account) }
    let!(:order_line1) { create(:order_line, :random, order: order, supplier_profile: dear_supplier, status: 'accepted') }

    let(:normal_supplier) { create(:supplier_profile, :random) }
    let!(:order_line2) { create(:order_line, :random, order: order, supplier_profile: normal_supplier, status: 'accepted') }

    before do
      order.update_column(:status, %w[new amended].sample)
      order.reload
    end

    before do
      # mock Dear Order Syncer
      order_syncer = delayed_order_syncer = double(Dear::SyncOrder)
      allow(Dear::SyncOrder).to receive(:new).and_return(order_syncer)
      allow(order_syncer).to receive(:delay).and_return(delayed_order_syncer)
      allow(delayed_order_syncer).to receive(:call).and_return(true)
    end

    it 'syncs the order in Dear for the dear suppliers only' do
      expect(Dear::SyncOrder).to receive(:new).with(order: order, supplier: dear_supplier)
      expect(Dear::SyncOrder).to_not receive(:new).with(order: order, supplier: normal_supplier)

      order_updater = Orders::Update.new(order: order).call
      expect(order_updater).to be_success
    end

    it 'syncs the order in Dear for supplier with no order lines but an attached dear sale' do
      order_line1.update_column(:supplier_profile_id, normal_supplier.id)
      create(:dear_sale, :random, order: order, supplier_profile: dear_supplier) # create a dear sale

      expect(Dear::SyncOrder).to receive(:new).with(order: order, supplier: dear_supplier)

      order_updater = Orders::Update.new(order: order).call
      expect(order_updater).to be_success
    end

    it 'does not sync order if the dear account is in-active' do
      dear_supplier.dear_account.update_column(:active, false)

      expect(Dear::SyncOrder).to_not receive(:new)

      order_updater = Orders::Update.new(order: order).call
      expect(order_updater).to be_success
    end

    it 'only syncs order if the order status is new|amended' do
      order.update_column(:status, %w[draft pending confirmed delivered skipped cancelled].sample)
      puts "Status #{order.status}"
      expect(Dear::SyncOrder).to_not receive(:new)

      order_updater = Orders::Update.new(order: order).call
      expect(order_updater).to be_success
    end
  end

  context 'with a coupon' do
    let(:coupon) { create(:coupon, :random) }

    it 'applies a code if not already present' do
      order_params = { coupon_code: coupon.code }
      order_updater = Orders::Update.new(order: order, order_params: order_params).call

      expect(order_updater).to be_success
      expect(order.reload.coupon).to eq(coupon)
    end

    it 'returns any errors if coupon cannot be attached' do
      coupon.update_column(:valid_until, Time.zone.now - 1.day) # expired coupon
      order_params = { coupon_code: coupon.code }
      order_updater = Orders::Update.new(order: order, order_params: order_params).call

      expect(order_updater).to_not be_success
      expect(order_updater.errors).to include('Coupon has expired')
    end
  end

  context 'order totals' do
    let(:credit_card) { create(:credit_card, :random) }
    let(:totals) { double(Orders::CalculateCustomerTotals) }
    before do
      allow(Orders::CalculateCustomerTotals).to receive(:new).and_return(totals)
      allow(totals).to receive(:call).and_return(true)
    end

    it 're-calculates totals when TOTAL_DEPENDENT_FIELDS are updated' do
      expect(Orders::CalculateCustomerTotals).to receive(:new).with(order: order, save_totals: true)

      no_delivery_charge_changed = [true, false].sample
      credit_card_changed = [true, false].sample
      charge_to_minimum_changed = [true, false].sample
      order_params = case
      when no_delivery_charge_changed
        { no_delivery_charge: !order.no_delivery_charge }
      when credit_card_changed
        { credit_card_id: credit_card.id }
      when charge_to_minimum_changed
        { charge_to_minimum: !order.charge_to_minimum }
      else # order_supplier changed
        supplier = create(:supplier_profile, :random)
        { order_supplier: { supplier_profile_id: supplier.id, delivery_fee_override: rand(20..40) } }
      end
      order_updater = Orders::Update.new(order: order, order_params: order_params).call

      expect(order_updater).to be_success
    end

    it 'does not re-calculates totals when no_delivery_charge is not updated' do
      expect(Orders::CalculateCustomerTotals).to_not receive(:new)

      order_params = { delivery_address_level: rand(10) }
      order_updater = Orders::Update.new(order: order, order_params: order_params).call

      expect(order_updater).to be_success
    end
  end

end
