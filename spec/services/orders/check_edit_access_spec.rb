require 'rails_helper'

RSpec.describe Orders::CheckEditAccess, type: :service, orders: true do

  let!(:order_customer) { create(:customer_profile, :random, :with_user) }
  let!(:order) { create(:order, :new, customer_profile: order_customer) }

  let!(:order_customer_user) { order_customer.user }
  let!(:lead_time_fetcher) { double(Orders::GetMaximumLeadTime) }

  before do
    # mock max lead time fetcher
    allow(Orders::GetMaximumLeadTime).to receive(:new).and_return(lead_time_fetcher)
    can_process_response = OpenStruct.new(can_process?: true)
    allow(lead_time_fetcher).to receive(:call).and_return(can_process_response)
  end

  it 'lets the customer of the order edit their own upcomming orders' do
    access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_customer_user).call

    expect(access_checker).to be_success
  end

  it 'returns with errors if order is marked as delivered' do
    order.update_column(:status, 'delivered')
    access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_customer_user).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('Order is already marked as delivered')  
  end

  it 'returns with errors if order is a woolworths order', woolworths: true do
    create(:woolworths_order, :random, order: order)
    access_checker = Orders::CheckEditAccess.new(order: order.reload, current_user: order_customer_user).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('Woolworths Orders cannot be edited! Please contact Yordar Admin.')  
  end

  it 'return with errors if order is missing' do
    access_checker = Orders::CheckEditAccess.new(order: nil, current_user: order_customer_user).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('Cannot edit a missing order')
  end

  it 'return with errors if user is missing' do
    access_checker = Orders::CheckEditAccess.new(order: order, current_user: nil).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('Cannot edit order without being logged in as a valid Customer')
  end

  it 'return with errors if order is not within any submitted status' do
    order.update_column(:status, (Order::VALID_ORDER_STATUSES.dup - %w[quoted new amended confirmed pending delivered cancelled skipped paused]).sample)
    access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_customer_user).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('The order is not saved yet!')
  end

  it 'return with errors if order is a custom order' do
    order.update_column(:order_variant, 'event_order')
    access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_customer_user).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('Custom Orders cannot be edited')
  end

  it 'return with errors if order does not belong to current user (as a customer)' do
    customer2 = create(:customer_profile, :random, :with_user)
    supplier = create(:supplier_profile, :random, :with_user)

    non_order_user = [customer2, supplier].sample.user
    access_checker = Orders::CheckEditAccess.new(order: order, current_user: non_order_user).call

    expect(access_checker).to_not be_success
    expect(access_checker.errors).to include('You do not have access to the orders of this customer!')
  end

  context 'as a Yordar Admin / All Customer Admin' do
    let!(:admin_user) { create(:user, :random, admin: true, super_admin: [true, false].sample) }
    let!(:all_customer_admin) { create(:user, :random, allow_all_customer_access: true) }

    let!(:order_admin_user) { [admin_user, all_customer_admin].sample }

    it 'lets them edit a delivered orders if it is not invoiced' do
      order.update_column(:status, 'delivered')
      access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_admin_user).call

      expect(access_checker).to be_success
    end

    it 'lets them edit a delivered orders even if invoiced' do
      order_invoice = create(:invoice, :random)
      order.update_columns(status: 'delivered', invoice_id: order_invoice.id)
      access_checker = Orders::CheckEditAccess.new(order: order.reload, current_user: order_admin_user).call

      expect(access_checker).to be_success
    end

    it 'lets them edit a Woolworths order', woolworths: true do
      create(:woolworths_order, :random, order: order)
      access_checker = Orders::CheckEditAccess.new(order: order.reload, current_user: order_admin_user).call

      expect(access_checker).to be_success
    end

    it 'returns errors when an all-customer-admin tries to edit an order of an in-active customer' do
      order_customer.user.update_column(:is_active, false)
      access_checker = Orders::CheckEditAccess.new(order: order.reload, current_user: all_customer_admin).call

      expect(access_checker).to_not be_success
      expect(access_checker.errors).to include('You do not have access to the orders of this customer!')
    end

    context 'with failed lead times check' do
      before do
        cannot_process_response = OpenStruct.new(can_process?: false)
        allow(lead_time_fetcher).to receive(:call).and_return(cannot_process_response)
      end

      it 'lets them edit an order past its lead time' do
        access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_admin_user).call

        expect(access_checker).to be_success
      end
    end
  end

  context 'as Customer Team Admin' do
    let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }
    let!(:admin_access_permission) { create(:access_permission, admin: customer_team_admin, customer_profile: order_customer) }

    let!(:company_team_admin_user) { customer_team_admin.reload.user }

    it 'lets the customer of the order edit their own upcomming orders' do
      access_checker = Orders::CheckEditAccess.new(order: order, current_user: company_team_admin_user).call

      expect(access_checker).to be_success
    end

    it 'returns with errors if the access permissions is not active' do
      admin_access_permission.update_column(:active, false)

      access_checker = Orders::CheckEditAccess.new(order: order, current_user: company_team_admin_user).call

      expect(access_checker).to_not be_success
      expect(access_checker.errors).to include('You do not have access to the orders of this customer!')
    end
  end

  context 'Order Lead Time check' do
    it 'makes a request to check the lead time' do
      expect(Orders::GetMaximumLeadTime).to receive(:new).with(order: order)

      access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_customer_user).call
    end

    context 'with failed lead times check' do
      before do
        cannot_process_response = OpenStruct.new(can_process?: false)
        allow(lead_time_fetcher).to receive(:call).and_return(cannot_process_response)
      end

      it 'returns with errors if the order has reached supplier lead time' do
        access_checker = Orders::CheckEditAccess.new(order: order, current_user: order_customer_user).call

        expect(access_checker).to_not be_success
        expect(access_checker.errors).to include('Unfortunately this order has passed it\'s lead time. Please contact support if this is an issue.')
      end
    end
  end

end