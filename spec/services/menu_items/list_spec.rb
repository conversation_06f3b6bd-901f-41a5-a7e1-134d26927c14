require 'rails_helper'

RSpec.describe MenuItems::List, type: :service, menu_items: true, menu_sections: true do
  subject { MenuItems::List }

  let(:supplier1) { create(:supplier_profile, :random) }
  let!(:menu_section11) { create(:menu_section, :random, supplier_profile: supplier1) }
  let!(:menu_item111) { create(:menu_item, :random, name: 'Soda Cans 10x140ml', menu_section: menu_section11, supplier_profile: supplier1, team_order_only: true, team_order: true, weight: 20) }
  let!(:menu_item112) { create(:menu_item, :random, menu_section: menu_section11, supplier_profile: supplier1, is_hidden: true, weight: 30) }

  let!(:menu_section12) { create(:menu_section, :random, supplier_profile: supplier1, name: 'C<PERSON>Tom') }
  let!(:menu_item121) { create(:menu_item, :random, menu_section: menu_section12, supplier_profile: supplier1, archived_at: Time.now, weight: 21) }
  let!(:menu_item122) { create(:menu_item, :random, menu_section: menu_section12, supplier_profile: supplier1, team_order: true, weight: 11) }

  let(:supplier2) { create(:supplier_profile, :random) }
  let!(:menu_section21) { create(:menu_section, :random, supplier_profile: supplier2) }
  let!(:menu_item211) { create(:menu_item, :random, name: 'Soda Cans 50x120ml', menu_section: menu_section21, supplier_profile: supplier2, team_order_only: true, weight: 15) }

  it 'lists all menu items by default' do
    lister_options = {}
    menu_items = subject.new(options: lister_options).call
    expect(menu_items).to include(menu_item111, menu_item112, menu_item121, menu_item122, menu_item211)
  end

  it 'lists items for a single menu section' do
    lister_options = { menu_section: menu_section11 }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item112)
    expect(menu_items).to_not include(menu_item121, menu_item122, menu_item211)
  end

  it 'lists items for multiple menu sections' do
    lister_options = { menu_sections: [menu_section12, menu_section21] }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item121, menu_item122, menu_item211)
    expect(menu_items).to_not include(menu_item111, menu_item112)
  end

  it 'does not list menu itens if the menu item supplier is not the same as the menu section supplier' do
    menu_item112.update_column(:supplier_profile_id, supplier2.id)
    lister_options = { menu_section: menu_section11 }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111)
    expect(menu_items).to_not include(menu_item112)
  end

  it 'lists the menu items for a supplier' do
    lister_options = { supplier: supplier1 }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item112, menu_item121, menu_item122)
    expect(menu_items).to_not include(menu_item211)
  end

  it 'lists the menu items for multiple suppliers' do
    lister_options = { suppliers: [supplier1, supplier2] }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item112, menu_item121, menu_item122, menu_item211)
  end

  it 'lists the menu items that are visibile (not-hidden)' do
    lister_options = { show_visible: true }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item121, menu_item122, menu_item211)
    expect(menu_items).to_not include(menu_item112)
  end

  it 'lists the menu items that are visibile (not-hidden) also considering the menu section\'s hidden state' do
    menu_section12.update_column(:is_hidden, true)
    lister_options = { show_visible: true }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111)
    expect(menu_items).to_not include(menu_item112, menu_item121, menu_item122)
  end

  it 'lists the menu items that are active (not-archived) state' do
    lister_options = { show_active: true }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item112, menu_item122, menu_item211)
    expect(menu_items).to_not include(menu_item121)
  end

  it 'lists the menu items that are active (not-archived) also considering the menu section\'s archive state' do
    menu_section21.update_column(:archived_at, Time.zone.now)
    lister_options = { show_active: true }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item112, menu_item122)
    expect(menu_items).to_not include(menu_item121, menu_item211)
  end

  it 'only lists the menu items which are available for normal orders' do
    lister_options = { order_type: 'normal' }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item112, menu_item121, menu_item122)
    expect(menu_items).to_not include(menu_item111, menu_item211)
  end

  it 'list the menu items which available for a "team order"', team_orders: true do
    lister_options = { order_type: 'team_order' }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item122, menu_item211)
    expect(menu_items).to_not include(menu_item112, menu_item121)
  end

  context 'filter by query' do
    it 'list the menu items with name containing the search query' do
      lister_options = { query: 'SoDa cAn' } # random case
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item211)
      expect(menu_items).to_not include(menu_item112, menu_item121, menu_item122)
    end

    it 'list the menu items with name and description containing the search query if through-search is passed' do
      lister_options = { query: 'SoDa 0x1' } # random case - random sort
      simple_search_menu_items = subject.new(options: lister_options).call

      expect(simple_search_menu_items).to_not include(menu_item111, menu_item112, menu_item121, menu_item122, menu_item211)

      thorough_lister_options = lister_options.merge({ thorough_search: true }) # random case - random sort - thorough
      thorough_search_menu_items = subject.new(options: thorough_lister_options).call
      
      expect(thorough_search_menu_items).to include(menu_item111, menu_item211)
      expect(thorough_search_menu_items).to_not include(menu_item112, menu_item121, menu_item122)
    end

    context 'with serving sizes' do
      let!(:serving_size112) { create(:serving_size, :random, menu_item: menu_item112, name: 'Fanta 20x30ml') }
      let!(:serving_size211) { create(:serving_size, :random, menu_item: menu_item211, name: 'Fanta 10x30ml', archived_at: Time.zone.now) }

      it 'list the menu items with non-archived serving sizes name containing the search query' do
        lister_options = { query: 'Fanta' } # random case
        menu_items = subject.new(options: lister_options).call

        expect(menu_items).to include(menu_item112)
        expect(menu_items).to_not include(menu_item111, menu_item121, menu_item122, menu_item211)
      end

      it 'list the menu items with with non-archived serving sizes name containing the search query even if through-search is passed', skip: 'Disabled serving size query on thorough search' do
        lister_options = { query: 'fAnta 0x3' } # random case - random sort
        simple_search_menu_items = subject.new(options: lister_options).call

        expect(simple_search_menu_items).to_not include(menu_item111, menu_item112, menu_item121, menu_item122, menu_item211)

        thorough_lister_options = lister_options.merge({ thorough_search: true }) # random case - random sort - thorough
        thorough_search_menu_items = subject.new(options: thorough_lister_options).call
        
        expect(thorough_search_menu_items).to include(menu_item112)
        expect(thorough_search_menu_items).to_not include(menu_item111, menu_item121, menu_item122, menu_item211)
      end
    end

    context 'with menu item names containing mapped names' do
      let!(:query_key) { MenuItems::List::QUERY_MAPS.keys.sample }

      before do
        [menu_item111, menu_item121].each do |item|
          item.update_column(:name, "suffix #{MenuItems::List::QUERY_MAPS[query_key].sample} prefix")
        end
      end

      it 'finds the items based on mapped names' do
        lister_options = { query: query_key } # random case
        menu_items = subject.new(options: lister_options).call

        expect(menu_items).to include(menu_item111, menu_item121)
        expect(menu_items).to_not include(menu_item112, menu_item122, menu_item211)
      end

      context 'with serving sizes' do
        let!(:serving_size112) { create(:serving_size, :random, menu_item: menu_item112, name: "suffix #{MenuItems::List::QUERY_MAPS[query_key].sample} prefix") }
        let!(:serving_size211) { create(:serving_size, :random, menu_item: menu_item211, name: "suffix #{MenuItems::List::QUERY_MAPS[query_key].sample} prefix", archived_at: Time.zone.now) }

        it 'finds the items based on mapped names against serving sizes' do
          lister_options = { query: query_key } # random case
          menu_items = subject.new(options: lister_options).call

          expect(menu_items).to include(menu_item111, menu_item121, menu_item112)
          expect(menu_items).to_not include(menu_item122, menu_item211)
        end
      end
    end # mapped queries
  end

  it 'does not list menu items from menu sections with name \'custom\'' do
    lister_options = { ignore_custom_menu_sections: true }
    menu_items = subject.new(options: lister_options).call

    expect(menu_items).to include(menu_item111, menu_item112, menu_item211)
    expect(menu_items).to_not include(menu_item121, menu_item122)
  end

  it 'orders the list of menu items based on passed in params' do
    lister_options = { order_by: :weight }
    menu_items = subject.new(options: lister_options).call
    expect(menu_items.map(&:weight)).to eq([11, 15, 20, 21, 30])

    lister_options = { order_by: { weight: :desc } }
    menu_items = subject.new(options: lister_options).call
    expect(menu_items.map(&:weight)).to eq([30, 21, 20, 15, 11])
  end

  it 'paginates the list of menu items' do
    # page 2
    lister_options = { order_by: :weight, page: 2, limit: 2 }
    menu_items = subject.new(options: lister_options).call
    expect(menu_items.map(&:weight)).to eq([20, 21]) # page 2

    # page 3
    lister_options = { order_by: :weight, page: 3, limit: 2 }
    menu_items = subject.new(options: lister_options).call
    expect(menu_items.map(&:weight)).to eq([30])
  end

  it 'limits the menu items based on passed in limit param' do
    lister_options = { limit: 2 }

    menu_items = subject.new(options: lister_options).call

    expect(menu_items.size).to eq(2)
  end

  context 'with categories' do
    let(:catering_category) { create(:category, :random, group: 'catering-services') }
    let(:home_delivery_category) { create(:category, :random, group: 'home-deliveries') }

    before do
      menu_section11.categories = [catering_category]
      menu_section12.categories = [home_delivery_category]
      menu_section21.categories = [home_delivery_category, catering_category]
    end

    it 'lists only menu items from the home deliveries menu sections if is_home_delivery is true' do
      lister_options = { is_home_delivery: true }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item121, menu_item122, menu_item211)
      expect(menu_items).to_not include(menu_item111, menu_item112)
    end

    it 'lists menu items from the menu sections with at least 1 non-home deliveries menu sections if is_home_delivery is false' do
      lister_options = { is_home_delivery: false }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item211)
      expect(menu_items).to_not include(menu_item121, menu_item122)
    end

    it 'lists only menu items from the menu sections with no category if is_home_delivery is false' do
      menu_section12.categories = []
      menu_section21.categories = []
      lister_options = { is_home_delivery: false }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item121, menu_item122, menu_item211)
    end
  end

  context 'filter in stock items' do
    before do
      menu_item111.update_column(:stock_quantity, 20)
      menu_item112.update_column(:stock_quantity, nil)
      menu_item121.update_column(:stock_quantity, 0)
      menu_item122.update_column(:stock_quantity, 1)
      menu_item211.update_column(:stock_quantity, nil)
    end

    it 'lists only menu items with a non-zero stock quantity value' do
      lister_options = { show_in_stock: true }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item122) # non-zero stock quantity
      expect(menu_items).to include(menu_item112, menu_item211) # non-zero but nil stock quantity
      expect(menu_items).to_not include(menu_item121) # zero stock quantity
    end
  end

  context 'Woolworths Products (with/without availability)', woolworths: true do
    let(:woolworths_supplier) { create(:supplier_profile, :random) }
    let(:nsw_store_id) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.detect{|_, v| v[:state] == 'NSW' }.first } # NSW
    let(:vic_store_id) { Rails.configuration.woolworths.fulfilment_stores.mapped_stores.detect{|_, v| v[:state] == 'VIC' }.first } # VIC

    let(:suburb_nsw) { create(:suburb, :random, state: 'NSW') }
    let(:suburb_vic) { create(:suburb, :random, state: 'VIC') }

    let!(:menu_section_w1) { create(:menu_section, :random, supplier_profile: woolworths_supplier) }
    let!(:menu_item_w11) { create(:menu_item, :random, menu_section: menu_section_w1, supplier_profile: woolworths_supplier) }
    let!(:menu_item_w12) { create(:menu_item, :random, menu_section: menu_section_w1, supplier_profile: woolworths_supplier) }
    let!(:menu_item_w13) { create(:menu_item, :random, menu_section: menu_section_w1, supplier_profile: woolworths_supplier) }

    let!(:store_availability1) { create(:woolworths_store_availability, :random, menu_item: menu_item_w11, store_id: nsw_store_id) }
    let!(:store_availability2) { create(:woolworths_store_availability, :random, menu_item: menu_item_w12, store_id: vic_store_id) }
    let!(:store_availability3) { create(:woolworths_store_availability, :random, menu_item: menu_item_w13, store_id: nsw_store_id) }

    before do
      # define the above created supplier as Woolworths Supplier
      allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(woolworths_supplier.id)
    end

    it 'list all items irrespective of store availability' do
      lister_options = { supplier: woolworths_supplier, suburb: suburb_nsw }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item_w11, menu_item_w12, menu_item_w13)
      expect(menu_items).to_not include(menu_item111, menu_item112, menu_item121, menu_item211) # other suppliers

      lister_options = { supplier: woolworths_supplier, suburb: suburb_vic }
      expect(menu_items).to include(menu_item_w11, menu_item_w12, menu_item_w13)
      expect(menu_items).to_not include(menu_item111, menu_item112, menu_item121, menu_item211) # other suppliers
    end

    it 'lists the menu items that have a store availability in the specified suburb\'s state', skip: 'reverted to not checking store availabiltity' do
      lister_options = { supplier: woolworths_supplier, suburb: suburb_nsw }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item_w11, menu_item_w13)
      expect(menu_items).to_not include(menu_item_w12)

      lister_options = { supplier: woolworths_supplier, suburb: suburb_vic }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item_w12)
      expect(menu_items).to_not include(menu_item_w11, menu_item_w13)

      expect(menu_items).to_not include(menu_item111, menu_item112, menu_item121, menu_item211)
    end

    it 'lists the items of other supplier\'s if passed in' do
      lister_options = { suppliers: [supplier2, woolworths_supplier], suburb: suburb_nsw }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item_w11, menu_item_w12, menu_item_w13)
      expect(menu_items).to include(menu_item211) # other suppliers
      expect(menu_items).to_not include(menu_item111, menu_item112, menu_item121) # other suppliers but not listed
    end
  end

  context 'favourites' do
    let(:customer) { create(:customer_profile, :random, :with_user) }
    let!(:favourite1) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item111, created_at: Time.zone.now - 1.day) }
    let!(:favourite2) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item122, created_at: Time.zone.now) }
    let!(:favourite3) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item211, created_at: Time.zone.now - 2.days) }

    it 'lists the menu items marked as favourite by the customer (sorted by LIFO)' do
      lister_options = { profile: customer, show_favourites: true }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item122, menu_item211)
      expect(menu_items).to_not include(menu_item112, menu_item121)

      expect(menu_items[0]).to eq(menu_item122)
      expect(menu_items[1]).to eq(menu_item111)
      expect(menu_items[2]).to eq(menu_item211)
    end

    it 'does not list any menu items if passed in profile isn\'t a customer and trying to show favourites' do
      supplier = create(:supplier_profile, :random)
      lister_options = { profile: supplier, show_favourites: true }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to be_empty
    end
  end

  context 'meal plan items (filter options of mealUUID)', meal_plans: true do
    let!(:meal_plan_category) { create(:category, :random) }

    before do
      meal_plan_category.update_column(:slug, Category::MEAL_PLAN_CATEGORY_SLUGS.sample)
      [menu_section11, menu_section21].each do |menu_section|
        menu_section.update(categories: [meal_plan_category])
      end
    end

    it 'only list items belonging to menu section with meal plan categories' do
      lister_options = { mealUUID: SecureRandom.uuid }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item211)
      expect(menu_items).to_not include(menu_item121, menu_item122)
    end
  end

  context 'with customer menu section visibility (via a company)' do
    let(:company1) { create(:company, :random) }
    let(:company2) { create(:company, :random) }
    let(:customer1) { create(:customer_profile, :random, :with_user, company: company1) }
    let(:customer2) { create(:customer_profile, :random, :with_user, company: company2) }

    before do
      menu_section12.companies = [company2]
    end

    it 'list only the menu items by menu sections that can be viewed by a customer' do
      lister_options = { profile: customer1 }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item211)
      expect(menu_items).to_not include(menu_item121, menu_item122)
    end

    it 'lists the menu items by menu sections that can be viewed by a customer' do
      lister_options = { profile: customer2 }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item211, menu_item121, menu_item122)
    end

    it 'list only the menu items that have no customer/company scope if no customer is passed' do
      lister_options = { profile: nil }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item211)
      expect(menu_items).to_not include(menu_item121, menu_item122)
    end

    it 'lists all menu items if the lister is admin' do
      lister_options = { is_admin: true }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item121, menu_item122, menu_item211)
    end

    it 'lists all menu items if the lister is for cache' do
      lister_options = { for_cache: true }
      menu_items = subject.new(options: lister_options).call

      expect(menu_items).to include(menu_item111, menu_item112, menu_item121, menu_item122, menu_item211)
    end
  end

end
