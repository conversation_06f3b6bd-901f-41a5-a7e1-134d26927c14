require 'rails_helper'

RSpec.describe Invoices::Notifications::SendOverdueNotifications, notifications: true, invoices: true do

  let!(:notifiable_time) { Time.zone.now.beginning_of_week + 3.days + rand(1..12).hours }

  let!(:invoice1) { create(:invoice, :random) }
  let!(:invoice2) { create(:invoice, :random) }
  let!(:invoice3) { create(:invoice, :random) }
  let!(:invoice4) { create(:invoice, :random) }

  let!(:invoices) { [invoice1, invoice2, invoice3, invoice4] }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:customer2) { create(:customer_profile, :random) }

  let!(:order11) { create(:order, :delivered, customer_profile: customer1, update_with_invoice: true, invoice: invoice1) }
  let!(:order12) { create(:order, :delivered, customer_profile: customer1, update_with_invoice: true, invoice: invoice2) }

  let!(:order23) { create(:order, :delivered, customer_profile: customer2, update_with_invoice: true, invoice: invoice3) }
  let!(:order24) { create(:order, :delivered, customer_profile: customer2, update_with_invoice: true, invoice: invoice4) }

  let!(:email_sender) { double(Customers::Emails::SendOverdueInvoiceEmail) }

  before do
    # mock email sender
    allow(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).and_return(email_sender)
    valid_email_response = OpenStruct.new(success?: true, sent_notification: 'sent-email', errors: [])
    allow(email_sender).to receive(:call).and_return(valid_email_response)

    # mock slack notifier
    allow(SlackNotifier).to receive(:send).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  Invoices::Notifications::SendOverdueNotifications::DAY_THRESHOLDS.each do |kind, number_of_days|
    context "#{kind} email" do
      let!(:kind) { kind }

      before do
        invoices.each do |invoice|
          invoice.update_column(:due_at, notifiable_time - number_of_days.days)
        end
      end

      it "makes a request to send overdue email to customer with invoices which are overdue by #{number_of_days} days" do
        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer1, invoices: [invoice1, invoice2].sort_by(&:due_at), kind: kind)
        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer2, invoices: [invoice3, invoice4].sort_by(&:due_at), kind: kind)

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_customers).to include(customer1, customer2)
        expect(notifications_sender.notified_invoices).to include(invoice1, invoice2, invoice3, invoice4)
      end

      it 'does not send notifications for non-confirmed unpaid invoices' do
        [invoice1, invoice4].each do |invoice|
          invoice.update_column(:status, (Invoice::VALID_STATUSES - ['confirmed']).sample)
        end

        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer1, invoices: [invoice2], kind: kind)
        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer2, invoices: [invoice3], kind: kind)

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_customers).to include(customer1, customer2)
        expect(notifications_sender.notified_invoices).to include(invoice2, invoice3)
        expect(notifications_sender.notified_invoices).to_not include(invoice1, invoice4)
      end

      it "does not send notification for invoices which are not overdue by #{number_of_days} days" do
        [invoice2, invoice3].each do |invoice|
          invoice.update_column(:due_at, [(notifiable_time + number_of_days.days), notifiable_time, (notifiable_time - (number_of_days + 1).days), (notifiable_time + (number_of_days - 1).days)].sample)
        end

        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer1, invoices: [invoice1], kind: kind)
        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer2, invoices: [invoice4], kind: kind)

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_customers).to include(customer1, customer2)
        expect(notifications_sender.notified_invoices).to include(invoice1, invoice4)
        expect(notifications_sender.notified_invoices).to_not include(invoice2, invoice3)
      end

      it 'does not send notifications for invoice which are not unpaid' do
        [invoice1, invoice4].each do |invoice|
          invoice.update_column(:payment_status, (Invoice::VALID_PAYMENT_STATUSES - ['unpaid']).sample)
        end

        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer1, invoices: [invoice2], kind: kind)
        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer2, invoices: [invoice3], kind: kind)

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_customers).to include(customer1, customer2)
        expect(notifications_sender.notified_invoices).to include(invoice2, invoice3)
        expect(notifications_sender.notified_invoices).to_not include(invoice1, invoice4)
      end

      it 'does not send notifications for invoices which are tagged as do_not_notify' do
        [invoice2, invoice3].each do |invoice|
          invoice.update_column(:do_not_notify, true)
        end

        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer1, invoices: [invoice1], kind: kind)
        expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer2, invoices: [invoice4], kind: kind)

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

        expect(notifications_sender).to be_success
        expect(notifications_sender.notified_customers).to include(customer1, customer2)
        expect(notifications_sender.notified_invoices).to include(invoice1, invoice4)
        expect(notifications_sender.notified_invoices).to_not include(invoice2, invoice3)
      end

      it "logs `Overdue Invoice` event for every overdue invoice along with overdue info of #{number_of_days} days", event_logs: true do
        expect(EventLogs::Create).to receive(:new).with(event_object: invoice1, event: 'invoice-overdue', overdue_by: "#{number_of_days} days")
        expect(EventLogs::Create).to receive(:new).with(event_object: invoice2, event: 'invoice-overdue', overdue_by: "#{number_of_days} days")
        expect(EventLogs::Create).to receive(:new).with(event_object: invoice3, event: 'invoice-overdue', overdue_by: "#{number_of_days} days")
        expect(EventLogs::Create).to receive(:new).with(event_object: invoice4, event: 'invoice-overdue', overdue_by: "#{number_of_days} days")

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call
        expect(notifications_sender).to be_success
      end

      it 'sends a slack notification' do
        expect(SlackNotifier).to receive(:send).with(":warning: The following 2 customers have invoices which are overdue by *#{number_of_days} days*", attachments: anything)

        notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call
        expect(notifications_sender).to be_success
      end

      context 'when email sending fails' do
        before do
          invalid_response = OpenStruct.new(success?: false, errors: ['email-sending-error'])
          allow(email_sender).to receive(:call).and_return(invalid_response)
        end

        it 'returns with email sending errors' do
          notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

          expect(notifications_sender).to_not be_success
          expect(notifications_sender.errors).to include('email-sending-error')
        end
      end

      context 'on a weekend' do
        let!(:weekend_notifiable_time) { Time.zone.now.beginning_of_week - [1, 2].sample.days }

        before do
          invoices.each do |invoice|
            invoice.update_column(:due_at, weekend_notifiable_time - number_of_days.days)
          end
        end

        it 'does not send any notifications' do
          notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: weekend_notifiable_time).call

          expect(notifications_sender).to_not be_success
          expect(notifications_sender.errors).to include('Cannot send overdue notifications on a Weekend')
        end
      end

      context 'on a monday' do
        let!(:monday_notifiable_time) { Time.zone.now.beginning_of_week + rand(1..12).hours }

        before do
          [invoice1, invoice4].each do |invoice|
            invoice.update_column(:due_at, monday_notifiable_time - number_of_days.days)
          end
          invoice2.update_column(:due_at, monday_notifiable_time - number_of_days.days - 1.day) # overdue on a Sunday
          invoice3.update_column(:due_at, monday_notifiable_time - number_of_days.days - 2.days) # overdue on a Saturday
        end

        it 'sends notifications for invoices which will be deemed overdue over the weekend' do
          notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: monday_notifiable_time).call

          expect(notifications_sender).to be_success
          expect(notifications_sender.notified_customers).to include(customer1, customer2)
          expect(notifications_sender.notified_invoices).to include(invoice1, invoice2, invoice3, invoice4)
        end
      end # on a monday


      context 'with non-invoicing customer', non_invoicing_customer: true do
        before do
          allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :non_invoicing_customer_id).and_return(customer2.id)
        end

        it 'does not notify overdue invoices of a customer tagged as non-invoicing customer' do
         expect(Customers::Emails::SendOverdueInvoiceEmail).to receive(:new).with(customer: customer1, invoices: [invoice1, invoice2].sort_by(&:due_at), kind: kind)
         expect(Customers::Emails::SendOverdueInvoiceEmail).to_not receive(:new).with(customer: customer2, invoices: [invoice3, invoice4].sort_by(&:due_at), kind: kind)

         notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: kind, time: notifiable_time).call

         expect(notifications_sender).to be_success
         expect(notifications_sender.notified_customers).to include(customer1)
         expect(notifications_sender.notified_customers).to_not include(customer2)
         expect(notifications_sender.notified_invoices).to include(invoice1, invoice2)
         expect(notifications_sender.notified_invoices).to_not include(invoice3, invoice4)
        end
      end # non-invoicing customer
    end # Day Threshold email
  end # Day Threshold loop

  it 'returns with error if the kind is not valid' do
    invalid_kind = 'INVALID-KIND'
    notifications_sender = Invoices::Notifications::SendOverdueNotifications.new(kind: invalid_kind, time: notifiable_time).call

    expect(notifications_sender).to_not be_success
    expect(notifications_sender.errors).to include("Cannot send an invalid kind of overdue notification = #{invalid_kind}")
  end
end
