require 'rails_helper'

RSpec.describe Invoices::GenerateOrderInvoices, type: :service, orders: true, invoices: true do

  let!(:customer1) { create(:customer_profile, :random, :with_flags) }
  let!(:billing_details1) { create(:billing_details, :random, customer_profile: customer1) }

  let!(:customer2) { create(:customer_profile, :random) }
  let!(:billing_details2) { create(:billing_details, :random, customer_profile: customer2) }

  # create at least 3 pay on account cards, to let other cards get id > 3
  let!(:pay_on_account_credit_card1) { create(:credit_card, :random) }
  let!(:pay_on_account_credit_card2) { create(:credit_card, :random) }
  let!(:pay_on_account_credit_card3) { create(:credit_card, :random) }

  let!(:order11) { create(:order, :delivered, customer_profile: customer1) }
  let!(:order12) { create(:order, :delivered, customer_profile: customer1) }
  let!(:order13) { create(:order, :delivered, customer_profile: customer1) }

  let!(:order21) { create(:order, :delivered, customer_profile: customer2) }
  let!(:order22) { create(:order, :delivered, customer_profile: customer2) }
  let!(:order23) { create(:order, :delivered, customer_profile: customer2) }

  before do
    invoice_generator = double(Invoices::GenerateInvoice)
    allow(Invoices::GenerateInvoice).to receive(:new).and_return(invoice_generator)
    generation_result = OpenStruct.new(success?: true, generated_invoice: rand(20))
    allow(invoice_generator).to receive(:call).and_return(generation_result)
  end

  context 'card only order invoices' do
    let!(:stripe_card) { create(:credit_card, :valid_stripe_payment) }
    let!(:eway_card) { create(:credit_card, :valid_eway_payment) }

    before do
      order12.update_column(:credit_card_id, stripe_card.id)
      order23.update_column(:credit_card_id, eway_card.id)
    end

    it 'only generates invoices for orders with credit cards' do
      [order12, order23].each do |card_order|
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [card_order], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      end
      [order11, order13, order21, order22].each do |non_card_order|
        expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [non_card_order], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      end

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: 'cards-only').call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(2)
    end

    it 'does not generate invoices for orders with card set as pay_on_account' do
      eway_card.update_column(:pay_on_account, true)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: 'cards-only').call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoices for orders with card set as auto_pay_invoice' do
      stripe_card.update_column(:auto_pay_invoice, true)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: 'cards-only').call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end
  end

  context 'with billing frequency of instantly' do
    let!(:billing_frequency) { 'instantly' }

    before do
      billing_details1.update_column(:frequency, billing_frequency)
      billing_details2.update_column(:frequency, (BillingDetails::VALID_FREQUENCIES - [billing_frequency]).sample)
    end

    it 'individually generates invoices for customers with specified billing frequency' do
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order21], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order22], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(3)
    end

    it 'only picks up delivered orders for invoicing' do
      order12.update_column(:status, %w[draft pending amended confirmed].sample)

      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(2)
    end

    it 'picks up orders marked as invoice_individually even if customer customer has a billing preference of weekly or monthly' do
      order21.update_column(:invoice_individually, true)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order21], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
    end

    it 'does not generate invoices for orders with attached credit card' do
      credit_card = create(:credit_card, :valid_stripe_payment)
      order11.update_column(:credit_card_id, credit_card.id)

      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(2)
    end

    context 'with errors during Invoice generation' do
      before do
        invoice_generator = double(Invoices::GenerateInvoice)
        allow(Invoices::GenerateInvoice).to receive(:new).and_return(invoice_generator)
        unsuccessfull_generation_result = OpenStruct.new(success?: false, generated_invoice: false, errors: ['failed'])
        allow(invoice_generator).to receive(:call).and_return(unsuccessfull_generation_result)
      end

      it 'returns errors raised during invoice generation' do
        invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
        expect(invoices_generator).to_not be_success
        expect(invoices_generator.errors).to include('failed')
      end
    end

    context 'with no billing details but orders' do # very edge case
      before do
        # billing_details1.destroy
        billing_details2.destroy
      end

      it 'individually generates invoices for customers with no billing frequency' do
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order21], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order22], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

        invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
        expect(invoices_generator).to be_success
        expect(invoices_generator.generated_invoices.size).to eq(6)
      end
    end # with no billing details

    context 'for non-invoicing customer', non_invoicing_customer: true do
      before do
        [billing_details1, billing_details2].each do |billing_detail|
          billing_detail.update_column(:frequency, billing_frequency)
        end
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :non_invoicing_customer_id).and_return(customer1.id)
      end

      it 'does not generate invoices for the non-invoicing customer' do
        expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order21], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order22], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

        invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
        expect(invoices_generator).to be_success
        expect(invoices_generator.generated_invoices.size).to eq(3)
      end
    end # with non invocing customer
  end # billing frequency of instantly

  context 'with billing frequency of weekly' do
    let!(:billing_frequency) { 'weekly' }

    before do
      [order11, order12, order13].each do |order|
        order.update_column(:delivery_at, Time.zone.now - 1.week - 3.hours)
      end
      billing_details1.update_column(:frequency, billing_frequency)
      billing_details2.update_column(:frequency, (BillingDetails::VALID_FREQUENCIES - [billing_frequency]).sample)
    end

    it 'only generates invoices for customers with specified billing frequency'  do
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order21, order22, order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoices not delivered in the last week' do
      order12.update_column(:delivery_at, Time.zone.now - 2.weeks)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoice for orders which are marked to be invoiced individually' do
      order12.update_column(:invoice_individually, true)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'only picks up delivered orders for invoicing' do
      order12.update_column(:status, %w[draft pending amended confirmed].sample)

      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order22, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoices for orders with attached credit card' do
      credit_card = create(:credit_card, :valid_stripe_payment)
      order11.update_column(:credit_card_id, credit_card.id)

      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    context 'the company invoices by PO' do
      let!(:company) { create(:company, :random, invoice_by_po: true) }
      let!(:customer_po) { create(:customer_purchase_order, :random, customer_profile: customer1) }

      before do
        company.customer_profiles << customer1
        [order11, order13].each do |po_order|
          po_order.update_column(:cpo_id, customer_po.id)
        end
      end

      it 'groups the orders by Purchase Order' do
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: customer_po, notify_customer: anything) # for po orders
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: nil, notify_customer: anything)

        invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
        expect(invoices_generator).to be_success
        expect(invoices_generator.generated_invoices.size).to eq(2)
      end

      context 'with GST-Free POs', gst_split_invoicing: true do
        let!(:gst_free_customer_po) { create(:customer_purchase_order, :random, customer_profile: customer1) }

        before do
          customer1.customer_flags.update_column(:has_gst_split_invoicing, true)

          order12.update_column(:cpo_id, gst_free_customer_po.id)
          order13.update_column(:gst_free_cpo_id, gst_free_customer_po.id) # order13 has both POs
        end

        it 'groups the orders by either Purchase Orders' do
          expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: customer_po, notify_customer: anything)
          expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12, order13], invoice_dates: anything, purchase_order: gst_free_customer_po, notify_customer: anything)

          invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
          expect(invoices_generator).to be_success
          expect(invoices_generator.generated_invoices.size).to eq(2)
        end
      end

      context 'with customer notifications' do
        # this test is valid for all billing frequencies
        it 'requests the invoice generator to notify customers' do
          expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: customer_po, notify_customer: true) # for po orders
          expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: nil, notify_customer: true)

          invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency, notify_customers: true).call
          expect(invoices_generator).to be_success
          expect(invoices_generator.generated_invoices.size).to eq(2)
        end
      end
    end
  end

  context 'with billing frequency of monthly' do
    let!(:billing_frequency) { 'monthly' }

    before do
      [order11, order12, order13].each do |order|
        order.update_column(:delivery_at, Time.zone.now - 1.month)
      end
      billing_details1.update_column(:frequency, billing_frequency)
      billing_details2.update_column(:frequency, (BillingDetails::VALID_FREQUENCIES - [billing_frequency]).sample)
    end

    it 'only generates invoices for customers with specified billing frequency'  do
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order21, order22, order23], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoices not delivered in the last month' do
      order13.update_column(:delivery_at, Time.zone.now - 2.months)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order12], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoice for orders which are marked to be invoiced individually' do
      order12.update_column(:invoice_individually, true)
      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'only picks up delivered orders for invoicing' do
      order12.update_column(:status, %w[draft pending amended confirmed].sample)

      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order22, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    it 'does not generate invoices for orders with attached credit card' do
      credit_card = create(:credit_card, :valid_stripe_payment)
      order11.update_column(:credit_card_id, credit_card.id)

      expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)
      expect(Invoices::GenerateInvoice).to_not receive(:new).with(invoicable_orders: [order11, order12, order13], invoice_dates: anything, purchase_order: anything, notify_customer: anything)

      invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
      expect(invoices_generator).to be_success
      expect(invoices_generator.generated_invoices.size).to eq(1)
    end

    context 'the company invoices by PO' do
      let!(:company) { create(:company, :random, invoice_by_po: true) }
      let!(:customer_po) { create(:customer_purchase_order, :random, customer_profile: customer1) }

      before do
        company.customer_profiles << customer1

        [order11, order13].each do |po_order|
          po_order.update_column(:cpo_id, customer_po.id)
        end
      end

      it 'groups the orders by Purchase Order' do
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: customer_po, notify_customer: anything) # for po orders
        expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12], invoice_dates: anything, purchase_order: nil, notify_customer: anything)

        invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
        expect(invoices_generator).to be_success
        expect(invoices_generator.generated_invoices.size).to eq(2)
      end

      context 'with GST-Free POs', gst_split_invoicing: true do
        let!(:gst_free_customer_po) { create(:customer_purchase_order, :random, customer_profile: customer1) }

        before do
          customer1.customer_flags.update_column(:has_gst_split_invoicing, true)

          order12.update_column(:cpo_id, gst_free_customer_po.id)
          order13.update_column(:gst_free_cpo_id, gst_free_customer_po.id) # order13 has both POs
        end

        it 'groups the orders by either Purchase Orders' do
          expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order11, order13], invoice_dates: anything, purchase_order: customer_po, notify_customer: anything)
          expect(Invoices::GenerateInvoice).to receive(:new).with(invoicable_orders: [order12, order13], invoice_dates: anything, purchase_order: gst_free_customer_po, notify_customer: anything)

          invoices_generator = Invoices::GenerateOrderInvoices.new(frequency: billing_frequency).call
          expect(invoices_generator).to be_success
          expect(invoices_generator.generated_invoices.size).to eq(2)
        end
      end
    end
  end # monthly billing preference

end
