require 'rails_helper'

RSpec.describe Xero::PushInvoices, type: :service, xero: true, invoices: true do

  let(:time) { Time.zone.now.beginning_of_day + rand(2..10).hours }

  let!(:customer1) { create(:customer_profile, :random, :with_user) }
  let!(:customer2) { create(:customer_profile, :random, :with_user) }

  let!(:supplier1) { create(:supplier_profile, :random, :with_user) }
  let!(:supplier2) { create(:supplier_profile, :random, :with_user) }

  let!(:customer_invoice1) { create(:invoice, :random, created_at: time + 2.hours) }
  let!(:supplier_invoice1) { create(:supplier_invoice, :random, supplier_profile: supplier1, created_at: time + 2.hours) }

  let!(:order1) { create(:order, :delivered, customer_profile: customer1, update_with_invoice: true, invoice: customer_invoice1) }
  let!(:order_supplier1) { create(:order_supplier, :random, order: order1, supplier_profile: supplier1, supplier_invoice: supplier_invoice1) }

  let!(:customer_invoice2) { create(:invoice, :random, created_at: time - 1.day) }
  let!(:supplier_invoice2) { create(:supplier_invoice, :random, supplier_profile: supplier2, created_at: time - 1.day) }

  let!(:order2) { create(:order, :delivered, customer_profile: customer2, update_with_invoice: true, invoice: customer_invoice2) }
  let!(:order_supplier2) { create(:order_supplier, :random, order: order2, supplier_profile: supplier2, supplier_invoice: supplier_invoice2) }

  let!(:customer_invoice_uploader) { double(Xero::API::UploadCustomerInvoice) }
  let!(:supplier_invoice_uploader) { double(Xero::API::UploadSupplierInvoice) }

  before do
    # mock Xero API Customer Invoice Upload
    allow(Xero::API::UploadCustomerInvoice).to receive(:new).and_return(customer_invoice_uploader)
    valid_upload_response = OpenStruct.new(success?: true)
    allow(customer_invoice_uploader).to receive(:call).and_return(valid_upload_response)

    # mock Xero API Supplier Invoice Upload
    allow(Xero::API::UploadSupplierInvoice).to receive(:new).and_return(supplier_invoice_uploader)
    valid_upload_response = OpenStruct.new(success?: true)
    allow(supplier_invoice_uploader).to receive(:call).and_return(valid_upload_response)
  end

  it 'makes requests to upload Customer Invoices' do
    expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice1)
    expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice2)

    invoices_pusher = Xero::PushInvoices.new(time: time).call

    expect(invoices_pusher).to be_success
    expect(invoices_pusher.pushed_invoices).to include(customer_invoice1, customer_invoice2)
  end

  it 'makes requests to upload Supplier Invoices' do
    expect(Xero::API::UploadSupplierInvoice).to receive(:new).with(invoice: supplier_invoice1)
    expect(Xero::API::UploadSupplierInvoice).to receive(:new).with(invoice: supplier_invoice2)

    invoices_pusher = Xero::PushInvoices.new(time: time).call

    expect(invoices_pusher).to be_success
    expect(invoices_pusher.pushed_invoices).to include(supplier_invoice1, supplier_invoice2)
  end

  it 'does not upload non-confirmed customer invoices' do
    customer_invoice2.update_column(:status, (Invoice::VALID_STATUSES - ['confirmed']).sample)
    expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice1)
    expect(Xero::API::UploadCustomerInvoice).to_not receive(:new).with(invoice: customer_invoice2)

    invoices_pusher = Xero::PushInvoices.new(time: time).call

    expect(invoices_pusher).to be_success
    expect(invoices_pusher.pushed_invoices).to_not include(customer_invoice2)
    expect(invoices_pusher.pushed_invoices).to include(customer_invoice1)
  end

  it 'does not upload invoices with no orders' do
    order1.destroy
    expect(Xero::API::UploadCustomerInvoice).to_not receive(:new).with(invoice: customer_invoice1)
    expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice2)

    expect(Xero::API::UploadSupplierInvoice).to_not receive(:new).with(invoice: supplier_invoice1)
    expect(Xero::API::UploadSupplierInvoice).to receive(:new).with(invoice: supplier_invoice2)

    invoices_pusher = Xero::PushInvoices.new(time: time).call

    expect(invoices_pusher).to be_success
    expect(invoices_pusher.pushed_invoices).to_not include(customer_invoice1, supplier_invoice1)
    expect(invoices_pusher.pushed_invoices).to include(customer_invoice2, supplier_invoice2)
  end

  context 'Customer Invoice restrictions' do
    it 'does not upload invoices where customer have xero push errors' do
      customer2.user.update_column(:xero_push_fault, true)
      expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice1)
      expect(Xero::API::UploadCustomerInvoice).to_not receive(:new).with(invoice: customer_invoice2)

      invoices_pusher = Xero::PushInvoices.new(time: time).call

      expect(invoices_pusher).to be_success
      expect(invoices_pusher.pushed_invoices).to include(customer_invoice1)
      expect(invoices_pusher.pushed_invoices).to_not include(customer_invoice2)
    end

    context 'with non-invoicing customer', non_invoicing_customer: true do
      before do
        allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :non_invoicing_customer_id).and_return(customer1.id)
      end

      it 'does not upload invoices where customer tagged as non-invoicing customer' do
        expect(Xero::API::UploadCustomerInvoice).to_not receive(:new).with(invoice: customer_invoice1)
        expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice2)

        invoices_pusher = Xero::PushInvoices.new(time: time).call

        expect(invoices_pusher).to be_success
        expect(invoices_pusher.pushed_invoices).to_not include(customer_invoice1)
        expect(invoices_pusher.pushed_invoices).to include(customer_invoice2)
      end
    end

    it 'does not upload invoices containing orders with split order id' do
      order2.update_column(:split_order_id, rand(10..100))
      expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice1)
      expect(Xero::API::UploadCustomerInvoice).to_not receive(:new).with(invoice: customer_invoice2)

      invoices_pusher = Xero::PushInvoices.new(time: time).call

      expect(invoices_pusher).to be_success
      expect(invoices_pusher.pushed_invoices).to include(customer_invoice1)
      expect(invoices_pusher.pushed_invoices).to_not include(customer_invoice2)
    end
  end

  it 'only uploads invoices created before current (time) day in retrial' do
    expect(Xero::API::UploadCustomerInvoice).to_not receive(:new).with(invoice: customer_invoice1)
    expect(Xero::API::UploadCustomerInvoice).to receive(:new).with(invoice: customer_invoice2)

    expect(Xero::API::UploadSupplierInvoice).to_not receive(:new).with(invoice: supplier_invoice1)
    expect(Xero::API::UploadSupplierInvoice).to receive(:new).with(invoice: supplier_invoice2)

    invoices_pusher = Xero::PushInvoices.new(time: time, retrial: true).call

    expect(invoices_pusher).to be_success
    expect(invoices_pusher.pushed_invoices).to_not include(customer_invoice1, supplier_invoice1)
    expect(invoices_pusher.pushed_invoices).to include(customer_invoice2, supplier_invoice2)
  end

  context 'with API Upload Errors' do
    before do
      invalid_upload_response = OpenStruct.new(success?: false, errors: ['xero-api-invoice-upload-error'])
      allow(customer_invoice_uploader).to receive(:call).and_return(invalid_upload_response)
      allow(supplier_invoice_uploader).to receive(:call).and_return(invalid_upload_response)
    end

    it 'returns with errors' do
      invoices_pusher = Xero::PushInvoices.new(time: time).call

      expect(invoices_pusher).to_not be_success
      expect(invoices_pusher.pushed_invoices).to be_blank
      expect(invoices_pusher.errored_invoices).to include(customer_invoice1, customer_invoice2, supplier_invoice1, supplier_invoice2)
      expect(invoices_pusher.errors).to include('xero-api-invoice-upload-error')
    end
  end # Upload Errors
end
