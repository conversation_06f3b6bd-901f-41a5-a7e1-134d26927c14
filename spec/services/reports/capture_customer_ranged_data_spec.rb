require 'rails_helper'

RSpec.describe Reports::CaptureCustomerRangedData, type: :service, reports: true do

  let!(:delivery_start) { (Time.zone.now - 2.weeks).beginning_of_week }

  let!(:customer1) { create(:customer_profile, :random) }
  let!(:order11) { create(:order, :delivered, name: 'order11', delivery_at: delivery_start + 1.day, customer_profile: customer1) }
  let!(:order12) { create(:order, :delivered, name: 'order12', delivery_at: delivery_start + 2.day, customer_profile: customer1) }

  let!(:customer2) { create(:customer_profile, :random) }
  let!(:order21) { create(:order, :delivered, name: 'order21', delivery_at: delivery_start + 1.day, customer_profile: customer2) }
  let!(:order22) { create(:order, :delivered, name: 'order22', delivery_at: delivery_start + 2.day, customer_profile: customer2) }

  let!(:order_line111) { create(:order_line, :random, name: 'order_line111', order: order11) }
  let!(:order_line112) { create(:order_line, :random, name: 'order_line112', order: order11) }
  let!(:order_line113) { create(:order_line, :random, name: 'order_line113', order: order11) }

  let!(:order_line121) { create(:order_line, :random, name: 'order_line121', order: order12) }

  let!(:order_line211) { create(:order_line, :random, name: 'order_line211', order: order21) }
  let!(:order_line212) { create(:order_line, :random, name: 'order_line212', order: order21) }
  let!(:order_line213) { create(:order_line, :random, name: 'order_line213', order: order21) }

  let!(:order_line221) { create(:order_line, :random, name: 'order_line221', order: order22) }

  before do
    # mock category_spends capture
    category_spend_capturer = double(Reports::CaptureCategorySpends)
    allow(Reports::CaptureCategorySpends).to receive(:new).and_return(category_spend_capturer)
    allow(category_spend_capturer).to receive(:call).and_return(true)

    # mock suppliers spend capture
    supplier_spend_capturer = double(Reports::CaptureSupplierSpends)
    allow(Reports::CaptureSupplierSpends).to receive(:new).and_return(supplier_spend_capturer)
    allow(supplier_spend_capturer).to receive(:call).and_return(true)    
  end

  it 'creates a report customer records for the passed in start date' do
    data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

    captured_report_customers = data_capturer.report_customers
    expect(captured_report_customers.map(&:source)).to include(customer1, customer2)
    expect(captured_report_customers.map(&:key_date).uniq).to eq([delivery_start.to_date])
  end

  it 'creates report data records per customer containing the orders data for that date range' do
    [order11, order12, order21, order22].each do |order|
      order.update_column(:customer_total, rand(1000..3000))
    end
    data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

    captured_report_customers = data_capturer.report_customers

    customer1_report = captured_report_customers.detect{|report| report.source == customer1 }
    customer1_report_data = customer1_report.report_data
    expect(customer1_report_data.size).to eq(1)
    expect(customer1_report_data.map(&:total_spend).uniq).to include(order11.customer_total + order12.customer_total)
    expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id, order12.id])

    customer2_report = captured_report_customers.detect{|report| report.source == customer2 }
    customer2_report_data = customer2_report.report_data
    expect(customer2_report_data.size).to eq(1)
    expect(customer2_report_data.map(&:total_spend).uniq).to include(order21.customer_total + order22.customer_total)
    expect(customer2_report_data.map(&:order_ids).uniq).to include([order21.id, order22.id])
  end

  context 'time based order status check' do
    it 'does not collect data for orders not marked as delivered when collecting data in the past' do # capture date is today
      order11.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order12.id])
      expect(customer1_report_data.map(&:order_ids).uniq).to_not include([order11.id, order12.id])
    end

    it 'includes non-delivered (yet active) orders if capture date overlaps caputring start and end dates' do
      order11.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week, capture_date: (delivery_start + 1.day).to_date).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id, order12.id])
    end    

    it 'includes non-delivered (yet active) orders when capturing data for days ending a day before capturing date' do # as orders are still remaining to be confirmed
      order11.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week, capture_date: (delivery_start.end_of_week + 1.day).to_date).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id, order12.id])
    end

    it 'does not collect data for orders not marked as delivered past yesterday' do
      order12.update_column(:status, 'confirmed')
      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week, capture_date: (delivery_start.end_of_week + 2.days).to_date).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id])
      expect(customer1_report_data.map(&:order_ids).uniq).to_not include([order11.id, order12.id])
    end
  end

  it 'does not collect data for orders not delivered within passed in range' do
    order12.update_column(:delivery_at, delivery_start + 1.month)
    data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

    customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
    customer1_report_data = customer1_report.report_data
    expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id])
    expect(customer1_report_data.map(&:order_ids).uniq).to_not include([order11.id, order12.id])
  end

  context 'orders with attached Customer Purchase Orders' do
    let!(:cpo11) { create(:customer_purchase_order, :random, customer_profile: customer1) }
    let!(:cpo21) { create(:customer_purchase_order, :random, customer_profile: customer2) }

    before do
      order11.update_column(:cpo_id, cpo11.id)
      order21.update_column(:cpo_id, cpo21.id)
    end

    it 'splits the report data records by PO' do
      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:customer_purchase_order).uniq).to include(cpo11, nil)
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id], [order12.id])

      customer2_report = data_capturer.report_customers.detect{|report| report.source == customer2 }
      customer2_report_data = customer2_report.report_data
      expect(customer2_report_data.map(&:customer_purchase_order).uniq).to include(cpo21, nil)
      expect(customer2_report_data.map(&:order_ids).uniq).to include([order21.id], [order22.id])
    end
  end

  context 'orders with categorized order lines' do
    let!(:catering_category) { create(:category, :random, group: 'catering-services') }
    let!(:generic_catering_category) { create(:category, :random, group: 'catering-services', is_generic: true) }
    let!(:kitchen_category) { create(:category, :random, group: 'kitchen-supplies') }
    let!(:generic_kitchen_category) { create(:category, :random, group: 'kitchen-supplies', is_generic: true) }

    before do
      [order_line111, order_line112, order_line213].each do |order_line|
        order_line.update_columns(category_id: catering_category.id, quantity: 10)
      end

      [order_line113, order_line211, order_line212].each do |order_line|
        order_line.update_columns(category_id: kitchen_category.id, quantity: 10)
      end
    end

    it 'splits the report data records by major category of the order (determined by category attached to the order_line)' do
      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:category).uniq).to include('catering-services', 'others')
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id], [order12.id])

      customer2_report = data_capturer.report_customers.detect{|report| report.source == customer2 }
      customer2_report_data = customer2_report.report_data
      expect(customer2_report_data.map(&:category).uniq).to include('kitchen-supplies', 'others')
      expect(customer2_report_data.map(&:order_ids).uniq).to include([order21.id], [order22.id])
    end

    it 'makes a request to capture category and supplier spends per categoried orders' do
      expect(Reports::CaptureCategorySpends).to receive(:new)
      expect(Reports::CaptureSupplierSpends).to receive(:new)

      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call
    end

    it 'splits the report data records by major category of the order (determined by category attached order line => menu_item => menu_section)' do
      menu_section1 = create(:menu_section, :random, categories: [kitchen_category])
      menu_item1 = create(:menu_item, :random, menu_section: menu_section1)
      order_line112.update_column(:category_id, nil) # no category directly attached to order line
      order_line112.update_column(:menu_item_id, menu_item1.id) # category attached to order line via menu item => menu section

      menu_section2 = create(:menu_section, :random, categories: [catering_category])
      menu_item2 = create(:menu_item, :random, menu_section: menu_section2)
      order_line212.update_column(:category_id, nil)
      order_line212.update_column(:menu_item_id, menu_item2.id)

      data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

      customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
      customer1_report_data = customer1_report.report_data
      expect(customer1_report_data.map(&:category).uniq).to include('kitchen-supplies', 'others')
      expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id], [order12.id])

      customer2_report = data_capturer.report_customers.detect{|report| report.source == customer2 }
      customer2_report_data = customer2_report.report_data
      expect(customer2_report_data.map(&:category).uniq).to include('catering-services', 'others')
      expect(customer2_report_data.map(&:order_ids).uniq).to include([order21.id], [order22.id])
    end

    context 'with custom order' do
      before do
        order12.update_columns(order_variant: 'event_order', major_category_id: generic_kitchen_category.id)
        order22.update_columns(order_variant: 'event_order', major_category_id: generic_catering_category.id)
      end

      it 'splits the report data records by major category of the order (determined by order\'s order category)' do
        data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

        customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
        customer1_report_data = customer1_report.report_data
        expect(customer1_report_data.map(&:category).uniq).to include('catering-services', 'kitchen-supplies')
        expect(customer1_report_data.map(&:category).uniq).to_not include('others')
        expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id], [order12.id])

        customer2_report = data_capturer.report_customers.detect{|report| report.source == customer2 }
        customer2_report_data = customer2_report.report_data
        expect(customer2_report_data.map(&:category).uniq).to include('kitchen-supplies', 'catering-services')
        expect(customer2_report_data.map(&:category).uniq).to_not include('others')
        expect(customer2_report_data.map(&:order_ids).uniq).to include([order21.id], [order22.id])
      end
    end # with custom orders

    context 'with order line suppliers (with flags)' do
      let!(:pantry_supplier) { create(:supplier_profile, :random, :with_flags, flags: { has_catering_services: false, has_kitchen_supplies: true }) }
      let!(:catering_supplier) { create(:supplier_profile, :random, :with_flags, flags: { has_catering_services: true, has_kitchen_supplies: false } ) }

      before do
        order_line121.update_column(:supplier_profile_id, pantry_supplier.id)
        order_line221.update_column(:supplier_profile_id, catering_supplier.id)
      end

      it 'splits the report data records by major category of the order (determined by supplier flags)' do
        data_capturer = Reports::CaptureCustomerRangedData.new(capture_start: delivery_start, capture_end: delivery_start.end_of_week).call

        customer1_report = data_capturer.report_customers.detect{|report| report.source == customer1 }
        customer1_report_data = customer1_report.report_data
        expect(customer1_report_data.map(&:category).uniq).to include('catering-services', 'kitchen-supplies')
        expect(customer1_report_data.map(&:category).uniq).to_not include('others')
        expect(customer1_report_data.map(&:order_ids).uniq).to include([order11.id], [order12.id])

        customer2_report = data_capturer.report_customers.detect{|report| report.source == customer2 }
        customer2_report_data = customer2_report.report_data
        expect(customer2_report_data.map(&:category).uniq).to include('kitchen-supplies', 'catering-services')
        expect(customer2_report_data.map(&:category).uniq).to_not include('others')
        expect(customer2_report_data.map(&:order_ids).uniq).to include([order21.id], [order22.id])
      end
    end # with order line suppliers with flags
  end # with categorized order lines

end
