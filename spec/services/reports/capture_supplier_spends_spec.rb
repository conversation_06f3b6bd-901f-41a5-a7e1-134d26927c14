require 'rails_helper'

RSpec.describe Reports::CaptureSupplierSpends, type: :service, reports: true, suppliers: true do
  let!(:report_datum) { create(:report_datum, :random) }
  let!(:country_code) { :au }

  let!(:supplier1) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier1') }
  let!(:supplier2) { create(:supplier_profile, :random, :with_flags, company_name: 'supplier2') }

  let!(:order1) { create(:order, :delivered, name: 'order1', customer_total: 300.15 ) }
  let!(:order_line11) { create(:order_line, :random, order: order1, supplier_profile: supplier1, quantity: 10, price: 15.0) }
  let!(:order_line12) { create(:order_line, :random, order: order1, supplier_profile: supplier1, quantity: 10, price: 15.0) }

  let!(:order2) { create(:order, :delivered, name: 'order2') }
  let!(:order_line21) { create(:order_line, :random, order: order2, supplier_profile: supplier2, quantity: 5, price: 5.0) }
  let!(:order_line22) { create(:order_line, :random, order: order2, supplier_profile: supplier2, quantity: 5, price: 5.0) }
  let!(:order_line23) { create(:order_line, :random, order: order2, supplier_profile: supplier1, quantity: 5, price: 5.0) }

  let!(:categorised_orders) do
    [order1, order2].map do |order|
      Reports::CaptureCategoryGroupSpends::CategorisedOrder.new(order: order, order_lines: order.reload.order_lines)
    end
  end

  it 'returns the spends per supplier' do
    spends_capturer = Reports::CaptureSupplierSpends.new(report_data: report_datum, categorised_orders: categorised_orders).call

    supplier_spends = spends_capturer.supplier_spends
    expect(supplier_spends).to be_present
    expect(supplier_spends.size).to eq(3) # 1 for order1 and 2 for order2
    expect(supplier_spends.map(&:supplier)).to include(supplier1, supplier2)
  end

  it 'saves the spends per supplier as order_data against the report_datum' do
    spends_capturer = Reports::CaptureSupplierSpends.new(report_data: report_datum, categorised_orders: categorised_orders).call

    supplier_order_data = report_datum.reload.order_data.where(data_kind: 'Supplier')

    expect(supplier_order_data).to be_present
    expect(supplier_order_data.size).to eq(2)
    expect(supplier_order_data.map(&:kind)).to include(supplier1.name, supplier2.name)

    supplier1_data = supplier_order_data.detect{|order_data| order_data.kind == supplier1.name }
    expected_supplier1_spend = order1.customer_total + [order_line23].sum{|order_line| order_line.price_inc_gst(gst_country: country_code) * order_line.quantity }
    expect(supplier1_data.total_spend.to_s).to eq(expected_supplier1_spend.to_s) # 300.15 + 27.5 = 327.65

    supplier2_data = supplier_order_data.detect{|order_data| order_data.kind == supplier2.name }
    expected_supplier2_spend = [order_line21, order_line22].sum{|order_line| order_line.price_inc_gst(gst_country: country_code) * order_line.quantity }
    expect(supplier2_data.total_spend.to_s).to eq(expected_supplier2_spend.to_s) # 27.5 + 27.5 = 55.0
  end

  context 'with delivery fees' do
    before do
      [order1, order2].each do |order|
        order.update_column(:customer_delivery, 30)
      end
    end

    it 'adds the split supplier specific delivery fee to the supplier spends' do
      delivery_with_gst = order2.customer_delivery * (1.0 + yordar_credentials(:yordar, :gst_percent, country_code).to_f)

      spends_capturer = Reports::CaptureSupplierSpends.new(report_data: report_datum, categorised_orders: categorised_orders).call

      supplier_order_data = report_datum.reload.order_data.where(data_kind: 'Supplier')
      expect(supplier_order_data).to be_present

      supplier1_data = supplier_order_data.detect{|order_data| order_data.kind == supplier1.name }
      expected_supplier1_spend = order1.customer_total + [order_line23].sum{|order_line| order_line.price_inc_gst(gst_country: :au) * order_line.quantity }
      expected_supplier1_spend += delivery_with_gst / 2
      expect(supplier1_data.total_spend.to_s).to eq(expected_supplier1_spend.to_s) # 300.15 + 27.5 + 20  = 347.65

      supplier2_data = supplier_order_data.detect{|order_data| order_data.kind == supplier2.name }
      expected_supplier2_spend = [order_line21, order_line22].sum{|order_line| order_line.price_inc_gst(gst_country: :au) * order_line.quantity }
      expected_supplier2_spend += delivery_with_gst / 2
      expect(supplier2_data.total_spend.to_s).to eq(expected_supplier2_spend.to_s)
    end
  end

  context 'with Ethical / Sustainable flags' do
    let!(:ethical_flag1) { SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.sample }
    let!(:ethical_flag2) { (SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS - [ethical_flag1]).sample }

    before do
      supplier1.supplier_flags.update_columns("#{ethical_flag1}" => true)
      supplier2.supplier_flags.update_columns("#{ethical_flag1}" => true, "#{ethical_flag2}" => true)
    end

    it 'returns the spends per supplier with the suppliers ethical_flags' do
      spends_capturer = Reports::CaptureSupplierSpends.new(report_data: report_datum, categorised_orders: categorised_orders).call

      supplier_spends = spends_capturer.supplier_spends
      expect(supplier_spends).to be_present

      supplier1_spends = supplier_spends.select{|spend| spend.supplier == supplier1 }
      expect(supplier1_spends.map(&:ethical_flags).flatten(1).uniq).to match_array([ethical_flag1])

      supplier2_spends = supplier_spends.select{|spend| spend.supplier == supplier2 }
      expect(supplier2_spends.map(&:ethical_flags).flatten(1).uniq).to match_array([ethical_flag1, ethical_flag2])
    end

    it 'saves the spends per Ethical Flag as order_data against the report_datum' do
      spends_capturer = Reports::CaptureSupplierSpends.new(report_data: report_datum, categorised_orders: categorised_orders).call

      ethical_order_data = report_datum.reload.order_data.where(data_kind: 'Ethical')

      expect(ethical_order_data).to be_present
      expect(ethical_order_data.size).to eq(2)
      expect(ethical_order_data.map(&:kind)).to include(I18n.t("supplier_profiles.tags.#{ethical_flag1}.label"), I18n.t("supplier_profiles.tags.#{ethical_flag2}.label"))

      ethical1_data = ethical_order_data.detect{|order_data| order_data.kind == I18n.t("supplier_profiles.tags.#{ethical_flag1}.label") }
      expected_ethical1_spend = order1.customer_total + [order_line21, order_line22, order_line23].sum{|order_line| order_line.price_inc_gst(gst_country: country_code) * order_line.quantity }
      expect(ethical1_data.total_spend.to_s).to eq(expected_ethical1_spend.to_s) # 327.65 + 27.5 + 27.5 + 27.5

      ethical2_data = ethical_order_data.detect{|order_data| order_data.kind == I18n.t("supplier_profiles.tags.#{ethical_flag2}.label") }
      expected_ethical2_spend = [order_line21, order_line22].sum{|order_line| order_line.price_inc_gst(gst_country: country_code) * order_line.quantity }
      expect(ethical2_data.total_spend.to_s).to eq(expected_ethical2_spend.to_s) # 27.5 + 27.5 = 55.0
    end
  end

end