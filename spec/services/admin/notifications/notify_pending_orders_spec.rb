require 'rails_helper'

RSpec.describe Admin::Notifications::NotifyPendingOrders, type: :service, notifications: true, pending_orders: true do

  subject { Admin::Notifications::NotifyPendingOrders.new(time: notification_time).call }

  let!(:notification_time) { Time.zone.now.beginning_of_week + 1.day + 2.hours } # Tuesday
  let!(:delivery_on) { notification_time + 1.day } # Wednesday

  let!(:customer) { create(:customer_profile, :random) }
  let!(:custom_order1) { create(:order, :draft, :custom_order, customer_profile: customer, delivery_at: notification_time + 1.day) }
  let!(:custom_order2) { create(:order, :draft, :custom_order, customer_profile: customer, delivery_at: notification_time + 1.day) }
  let!(:custom_order3) { create(:order, :draft, :custom_order, customer_profile: customer, delivery_at: notification_time + 1.day) }

  let!(:quote_order1) { create(:order, :quoted, customer_profile: customer, delivery_at: notification_time + 1.day) }
  let!(:quote_order2) { create(:order, :quoted, customer_profile: customer, delivery_at: notification_time + 1.day) }
  let!(:quote_order3) { create(:order, :quoted, customer_profile: customer, delivery_at: notification_time + 1.day) }

  before do
    # mock email sender
    email_sender = double(Admin::Emails::SendPendingOrdersEmail)
    allow(Admin::Emails::SendPendingOrdersEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:call)

    # mock event logger
    event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:call).and_return(true)
  end

  it 'notifies admins about all custom/quote orders that are to be delivered the next business day' do
    expect(Admin::Emails::SendPendingOrdersEmail).to receive(:new).with(pending_orders: [custom_order1, custom_order2, custom_order3, quote_order1, quote_order2, quote_order3], delivery_on: delivery_on.beginning_of_day)

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(custom_order1, custom_order2, custom_order3, quote_order1, quote_order2, quote_order3)
  end

  it 'logs a `Pending Orders Event` with pending draf/quotes custom orders or quoted normal orders' do
    expect(EventLogs::Create).to receive(:new).with(event: 'pending-orders', delivery_on: delivery_on.to_s(:full_date), custom_orders: [custom_order1, custom_order2, custom_order3].map(&:id).sort, normal_orders: [quote_order1, quote_order2, quote_order3].map(&:id).sort)

    notification_sender = subject
    expect(notification_sender).to be_success
  end

  it 'doesn\'t notify non-pending orders' do
    custom_order1.update_column(:status, %w[new confirmed amended cancelled delivered].sample)
    quote_order2.update_column(:status, %w[new confirmed amended cancelled delivered].sample)
    expect(Admin::Emails::SendPendingOrdersEmail).to receive(:new).with(pending_orders: [custom_order2, custom_order3, quote_order1, quote_order3], delivery_on: delivery_on.beginning_of_day)

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(custom_order2, custom_order3, quote_order1, quote_order3)
  end

  it 'doesn\'t notify orders which are not be delivered the next business day' do
    custom_order2.update_column(:delivery_at, [(notification_time - 1.days), notification_time, (notification_time + 2.days)].sample)
    quote_order3.update_column(:delivery_at, [(notification_time - 1.days), notification_time, (notification_time + 2.days)].sample)
    expect(Admin::Emails::SendPendingOrdersEmail).to receive(:new).with(pending_orders: [custom_order1, custom_order3, quote_order1, quote_order2], delivery_on: delivery_on.beginning_of_day)

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(custom_order1, custom_order3, quote_order1, quote_order2)
  end

  it 'doesn\'t notify orders without any customers' do
    custom_order3.update_column(:customer_profile_id, nil)
    quote_order1.update_column(:customer_profile_id, nil)
    expect(Admin::Emails::SendPendingOrdersEmail).to receive(:new).with(pending_orders: [custom_order1, custom_order2, quote_order2, quote_order3], delivery_on: delivery_on.beginning_of_day)

    notification_sender = subject
    expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(custom_order1, custom_order2, quote_order2, quote_order3)
  end

  it 'doesn\'t notify orders without any delivery date' do
    custom_order1.update_column(:delivery_at, nil)
    quote_order2.update_column(:delivery_at, nil)
    expect(Admin::Emails::SendPendingOrdersEmail).to receive(:new).with(pending_orders: [custom_order2, custom_order3, quote_order1, quote_order3], delivery_on: delivery_on.beginning_of_day)

    notification_sender = subject
     expect(notification_sender).to be_success
    expect(notification_sender.pending_orders).to include(custom_order2, custom_order3, quote_order1, quote_order3)
  end

  it 'doesn\'t send an email if there are no pending custom/quote orders' do
    [custom_order1, custom_order2, custom_order3, quote_order1, quote_order2, quote_order3].each do |order|
      order.update_column(:status, 'cancelled')
    end
    expect(Admin::Emails::SendPendingOrdersEmail).to_not receive(:new)

    notification_sender = subject
    expect(notification_sender).to_not be_success
    expect(notification_sender.errors).to include('No Pending Orders')
    expect(notification_sender.pending_orders).to be_blank
  end

  context 'on a Friday' do
    let!(:notification_time) { Time.zone.now.beginning_of_week + 4.days } # Friday

    before do
      [custom_order1, custom_order2, custom_order3, quote_order1, quote_order2, quote_order3].each do |order|
        order.update_column(:delivery_at, notification_time + [1, 2, 3].sample.days)
      end
    end

    it 'notifies admins about all custom/quote orders that are to be delivered over the weekend and Monday' do
      expect(Admin::Emails::SendPendingOrdersEmail).to receive(:new).with(pending_orders: [custom_order1, custom_order2, custom_order3, quote_order1, quote_order2, quote_order3].sort_by(&:delivery_at), delivery_on: delivery_on.beginning_of_day)

      notification_sender = subject
      expect(notification_sender.pending_orders).to include(custom_order1, custom_order2, custom_order3, quote_order1, quote_order2, quote_order3)
    end
  end

  context 'on a weekend' do
    let!(:notification_time) { Time.zone.now.beginning_of_week + [5, 6].sample.days } # Saturday or Sunday

    it 'does not notify on a weekend' do
      expect(Admin::Emails::SendPendingOrdersEmail).to_not receive(:new)

      notification_sender = subject
      expect(notification_sender).to_not be_success
      expect(notification_sender.errors).to include('Cannot notify on a weekend')
      expect(notification_sender.pending_orders).to be_blank
    end
  end

end
