require 'rails_helper'

RSpec.describe Admin::Notifications::SendReminders, type: :service, notifications: true, customers: true, reminders: true do

  subject { Admin::Notifications::SendReminders.new(time: reminder_time, frequency: frequency).call }

  let!(:reminder_time) { Time.zone.now.beginning_of_week + 1.day }

  let!(:reminder1) { create(:reminder, :random, frequency: frequency) }
  let!(:reminder2) { create(:reminder, :random, frequency: frequency) }
  let!(:email_sender) { double(Admin::Emails::SendReminderEmail) }

  before do
    # mock email sender
    allow(Admin::Emails::SendReminderEmail).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, sent_email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)
  end

  context 'weekly reminders' do
    let!(:frequency) { 'weekly' }
  
    before do
      reminder1.update_column(:starting_at, reminder_time - rand(1..10).weeks)
      reminder2.update_column(:starting_at, reminder_time - rand(1..10).weeks)
    end
    
    it 'sends emails for each weekly reminder' do
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(2)
      expect(reminders_sender.sent_notifications).to include(reminder1, reminder2)
    end

    it 'does not send emails for non-weekly reminders' do
      reminder2.update_column(:frequency, (Reminder::VALID_FREQUENCIES - [frequency]).sample)

      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder1)
      expect(reminders_sender.sent_notifications).to_not include(reminder2)
    end

    it 'does not send emails not having starting date on the same week as reminder week' do
      reminder1.update_column(:starting_at, reminder_time - rand(1..10).weeks + 2.days)

      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder2)
      expect(reminders_sender.sent_notifications).to_not include(reminder1)
    end
  end # weekly

  context 'fortnightly reminders' do
    let!(:frequency) { 'fortnightly' }
  
    before do
      reminder1.update_column(:starting_at, reminder_time - [2.weeks, 4.weeks].sample)
      reminder2.update_column(:starting_at, reminder_time - [2.weeks, 4.weeks].sample)
    end
    
    it 'sends emails for each fortnightly reminder' do
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(2)
      expect(reminders_sender.sent_notifications).to include(reminder1, reminder2)
    end

    it 'does not send emails for non-fortnightly reminders' do
      reminder2.update_column(:frequency, (Reminder::VALID_FREQUENCIES - [frequency]).sample)

      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder1)
      expect(reminders_sender.sent_notifications).to_not include(reminder2)
    end

    it 'does not send emails not having starting date on the same week and fortnight as reminder time' do
      reminder1.update_column(:starting_at, reminder_time - [(2.weeks + 2.days), 1.week].sample)

      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder2)
      expect(reminders_sender.sent_notifications).to_not include(reminder1)
    end
  end # fortnightly

  context 'monthly reminders (by date)' do
    let!(:frequency) { 'monthly_by_date' }
  
    before do
      reminder1.update_column(:starting_at, reminder_time - rand(1..10).months)
      reminder2.update_column(:starting_at, reminder_time - rand(1..10).months)
    end
    
    it 'sends emails for each monthly reminder' do
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(2)
      expect(reminders_sender.sent_notifications).to include(reminder1, reminder2)
    end

    it 'does not send emails for non-monthly_by_date reminders' do
      reminder2.update_column(:frequency, (Reminder::VALID_FREQUENCIES - [frequency]).sample)

      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder1)
      expect(reminders_sender.sent_notifications).to_not include(reminder2)
    end

    it 'does not send emails not having starting date on the same week and fortnight as reminder time' do
      reminder1.update_column(:starting_at, reminder_time - rand(1..10).months + 2.days)

      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder2)
      expect(reminders_sender.sent_notifications).to_not include(reminder1)
    end
  end # monthly_by_date

  context 'monthly reminders (by 1st weekday of month)' do
    let!(:frequency) { 'monthly_by_weekday' }

    let!(:starting_at) {  Time.zone.now.beginning_of_month - 2.months + 1.day }
    let!(:reminder_time) do
      first_weekday_of_month = (starting_at + rand(1..10).month).beginning_of_month
      while first_weekday_of_month.wday != starting_at.wday
        first_weekday_of_month += 1.day
      end
      first_weekday_of_month
    end
  
    before do
      reminder1.update_column(:starting_at, starting_at)
      reminder2.update_column(:starting_at, starting_at)
    end
    
    it 'sends emails for each monthly reminder' do
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(2)
      expect(reminders_sender.sent_notifications).to include(reminder1, reminder2)
    end

    it 'does not send emails for non-monthly_by_weekday reminders' do
      reminder2.update_column(:frequency, (Reminder::VALID_FREQUENCIES - [frequency]).sample)

      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder1)
      expect(reminders_sender.sent_notifications).to_not include(reminder2)
    end

    it 'does not send emails not having starting date on the same week and fortnight as reminder time' do
      reminder1.update_column(:starting_at, starting_at + 2.days)

      expect(Admin::Emails::SendReminderEmail).to_not receive(:new).with(reminder: reminder1)
      expect(Admin::Emails::SendReminderEmail).to receive(:new).with(reminder: reminder2)

      reminders_sender = subject
      expect(reminders_sender).to be_success
      expect(reminders_sender.sent_notifications.size).to eq(1)
      expect(reminders_sender.sent_notifications).to include(reminder2)
      expect(reminders_sender.sent_notifications).to_not include(reminder1)
    end
  end # monthly_by_weekday

end