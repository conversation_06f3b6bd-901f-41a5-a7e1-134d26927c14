require 'rails_helper'

RSpec.describe Admin::ListReminders, type: :service, admin: true, users: true, reminders: true do

  let!(:reminder1) { create(:reminder, :random, title: 'reminder1') }
  let!(:reminder2) { create(:reminder, :random, title: 'reminder2') }
  let!(:reminder3) { create(:reminder, :random, title: 'reminder3') }

  it 'lists all reminders by default' do
    reminders = Admin::ListReminders.new.call

    expect(reminders).to include(reminder1, reminder2, reminder3)
  end

  it 'filters reminders by their active status' do
    reminder2.update_column(:active, false)

    lister_options = { active_only: true }
    reminders = Admin::ListReminders.new(options: lister_options).call

    expect(reminders).to include(reminder1, reminder3) # active by default
    expect(reminders).to_not include(reminder2)
  end

  context 'filter by query' do
    it 'filters reminders by their title' do
      lister_options = { query: reminder2.title }
      reminders = Admin::ListReminders.new(options: lister_options).call

      expect(reminders).to include(reminder2)
      expect(reminders).to_not include(reminder1, reminder1)
    end

    it 'filters reminders by their message' do
      lister_options = { query: reminder3.message }
      reminders = Admin::ListReminders.new(options: lister_options).call

      expect(reminders).to include(reminder3)
      expect(reminders).to_not include(reminder1, reminder2)
    end

    it 'filters reminders by their frequncy or part of frequency' do
      query_frequency = Reminder::VALID_FREQUENCIES.sample
      reminder1.update_column(:frequency, query_frequency)
      [reminder2, reminder3].each{|reminder| reminder.update_column(:frequency, (Reminder::VALID_FREQUENCIES - [query_frequency]).sample) }

      lister_options = { query: query_frequency.split('_').last }
      reminders = Admin::ListReminders.new(options: lister_options).call

      expect(reminders).to include(reminder1)
      expect(reminders).to_not include(reminder2, reminder3)
    end

    context 'for customer reminder' do
      let!(:customer) { create(:customer_profile, :random, customer_name: Faker::Name.name) }

      before do
        reminder2.update_columns(remindable_type: 'CustomerProfile', remindable_id: customer.id)
      end

      it 'filters reminders by the customer name' do
        lister_options = { query: customer.customer_name }
        reminders = Admin::ListReminders.new(options: lister_options).call

        expect(reminders).to include(reminder2)
        expect(reminders).to_not include(reminder1, reminder3)
      end

      context 'with company name' do
        before do
          customer.update_column(:company_name, Faker::Name.name)
          reminder3.update_columns(remindable_type: 'CustomerProfile', remindable_id: customer.id)
        end

        it 'filters reminders by the customer\'s company name' do
          lister_options = { query: customer.company_name }
          reminders = Admin::ListReminders.new(options: lister_options).call

          expect(reminders).to include(reminder2, reminder3)
          expect(reminders).to_not include(reminder1)
        end
      end

      context 'with attached customer company' do
        let!(:company) { create(:company, :random, customer_profiles: [customer]) }

        it 'filters reminders by the customer\'s attached company name' do
          lister_options = { query: company.name }
          reminders = Admin::ListReminders.new(options: lister_options).call

          expect(reminders).to include(reminder2)
          expect(reminders).to_not include(reminder1, reminder3)
        end
      end # with attached company
    end # customer reminder

    context 'for supplier reminder' do
      let!(:supplier) { create(:supplier_profile, :random) }

      before do
        reminder3.update_columns(remindable_type: 'SupplierProfile', remindable_id: supplier.id)
      end

      it 'filters reminders by the supplier name' do
        lister_options = { query: supplier.company_name }
        reminders = Admin::ListReminders.new(options: lister_options).call

        expect(reminders).to include(reminder3)
        expect(reminders).to_not include(reminder1, reminder2)
      end
    end # supplier reminder
  end # filter by query

  context 'filter by Customer Reminder' do
    it 'returns the passed in reminder if accessible' do
      lister_options = { reminder: reminder2 }
      reminders = Admin::ListReminders.new(options: lister_options).call

      expect(reminders).to include(reminder2)
      expect(reminders).to_not include(reminder1, reminder3)
    end

    it 'returns an empty list for non-reminder object' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { reminder: supplier }
      reminders = Admin::ListReminders.new(options: lister_options).call

      expect(reminders).to be_empty
    end
  end

  it 'paginates reminders according to page and limit (default sort of created_at and then ID)' do
    lister_options = { page: 2, limit: 1 }
    reminders = Admin::ListReminders.new(options: lister_options).call

    expect(reminders).to include(reminder2)
    expect(reminders).to_not include(reminder1, reminder3)
  end

end