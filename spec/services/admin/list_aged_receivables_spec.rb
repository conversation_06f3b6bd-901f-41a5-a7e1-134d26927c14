require 'rails_helper'

RSpec.describe Admin::ListAgedReceivables, type: :service, invoices: true, admin: true do

  subject { Admin::ListAgedReceivables.new(options: lister_options).call }

  let!(:admin_user) { create(:user, :admin) }
  let!(:time) { Time.zone.now.beginning_of_month + rand(2..10).days }

  let!(:customer1) { create(:customer_profile, :random, customer_name: 'Customer 1') }
  let!(:invoice11) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time + 10.days) }
  let!(:invoice12) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 10.days) }
  let!(:invoice13) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 1.month - 10.days) }
  let!(:invoice132) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 1.month - 10.days) }
  let!(:invoice14) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 2.months - 10.days) }
  let!(:invoice15) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 3.months - 10.days) }
  let!(:invoice152) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 3.months - 10.days) }
  let!(:invoice16) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer1, due_at: time - 4.months - 10.days) }

  let!(:customer2) { create(:customer_profile, :random, customer_name: 'Customer 2') }
  let!(:invoice21) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time + 10.days) }
  let!(:invoice22) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 10.days) }
  let!(:invoice222) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 10.days) }
  let!(:invoice23) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 1.month - 10.days) }
  let!(:invoice24) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 2.months - 10.days) }
  let!(:invoice25) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 3.months - 10.days) }
  let!(:invoice252) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 3.months - 10.days) }
  let!(:invoice26) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 4.months - 10.days) }
  let!(:invoice262) { create(:invoice, :random, payment_status: 'unpaid', customer_profile: customer2, due_at: time - 4.months - 10.days) }

  let!(:lister_options) do
    {
      user: admin_user,
      time: time,
    }
  end

  it 'lists the aged receivables for customers with unpaid invoices' do
    aged_receivables = subject

    expect(aged_receivables).to be_present
    expect(aged_receivables.size).to eq(2)
    expect(aged_receivables.keys).to include(customer1, customer2)
    expect(aged_receivables.values.flatten(1).sample).to be_a(Admin::ListAgedReceivables::CustomerTimedInvoices)
  end

  it 'only lists invoices for customers with unpaid invoices' do
    customer1.invoices.each do |invoice|
      invoice.update_column(:status, %w[partial paid].sample)
    end

    aged_receivables = subject

    expect(aged_receivables).to be_present
    expect(aged_receivables.size).to eq(1)
    expect(aged_receivables.keys).to include(customer2)
    expect(aged_receivables.keys).to_not include(customer1)
  end

  it 'only lists invoices for customers with invoices' do
    customer2.invoices.each do |invoice|
      invoice.update_column(:do_not_notify, true)
    end

    aged_receivables = subject

    expect(aged_receivables).to be_present
    expect(aged_receivables.size).to eq(1)
    expect(aged_receivables.keys).to include(customer1)
    expect(aged_receivables.keys).to_not include(customer2)
  end

  it 'only lists invoices for customers with confirmed invoices' do
    customer1.invoices.each do |invoice|
      invoice.update_column(:status, %w[amended voided deleted].sample)
    end

    aged_receivables = subject

    expect(aged_receivables).to be_present
    expect(aged_receivables.size).to eq(1)
    expect(aged_receivables.keys).to include(customer2)
    expect(aged_receivables.keys).to_not include(customer1)
  end

  it 'only lists invoices for customers based on passed in query with customer name' do
    lister_options_with_query = { query: customer1.customer_name }.merge(lister_options)
    aged_receivables = Admin::ListAgedReceivables.new(options: lister_options_with_query).call

    expect(aged_receivables).to be_present
    expect(aged_receivables.size).to eq(1)
    expect(aged_receivables.keys).to include(customer1)
    expect(aged_receivables.keys).to_not include(customer2)
  end

  it 'only lists invoices for customers with due at after the XERO Sync Threshold' do
    customer1.invoices.each do |invoice|
      invoice.update_column(:due_at, Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE) - rand(1..10).days)
    end

    aged_receivables = subject

    expect(aged_receivables).to be_present
    expect(aged_receivables.size).to eq(1)
    expect(aged_receivables.keys).to include(customer2)
    expect(aged_receivables.keys).to_not include(customer1)
  end

  context 'time frame based invoices' do
    let!(:aged_receivables) { subject }
    let!(:customer1_aged_receivables) { aged_receivables[customer1] }
    let!(:customer2_aged_receivables) { aged_receivables[customer2] }

    it 'contains summary for each time frame' do
      expect(customer1_aged_receivables).to be_present
      expect(customer1_aged_receivables.map(&:time_frame)).to include(*Admin::ListAgedReceivables::TIME_FRAMES_MAP.keys)

      expect(customer2_aged_receivables).to be_present
      expect(customer2_aged_receivables.map(&:time_frame)).to include(*Admin::ListAgedReceivables::TIME_FRAMES_MAP.keys)
    end

    it 'contain summary for current invoices' do
      customer1_current_aged_receivables = customer1_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :current }
      expect(customer1_current_aged_receivables).to be_present
      expect(customer1_current_aged_receivables.invoices).to include(invoice11)
      expect(customer1_current_aged_receivables.total).to eq([invoice11].sum(&:amount_price))

      customer2_current_aged_receivables = customer2_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :current }
      expect(customer2_current_aged_receivables).to be_present
      expect(customer2_current_aged_receivables.invoices).to include(invoice21)
      expect(customer2_current_aged_receivables.total).to eq([invoice21].sum(&:amount_price))
    end

    it 'contain summary for invoices due in the last month' do
      customer1_within_1_month_aged_receivables = customer1_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :within_1_month }
      expect(customer1_within_1_month_aged_receivables).to be_present
      expect(customer1_within_1_month_aged_receivables.invoices).to include(invoice12)
      expect(customer1_within_1_month_aged_receivables.total).to eq([invoice12].sum(&:amount_price))

      customer2_within_1_month_aged_receivables = customer2_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :within_1_month }
      expect(customer2_within_1_month_aged_receivables).to be_present
      expect(customer2_within_1_month_aged_receivables.invoices).to include(invoice22, invoice222)
      expect(customer2_within_1_month_aged_receivables.total).to eq([invoice22, invoice222].sum(&:amount_price))
    end

    it 'contains summary for invoices due at least a month' do
      customer1_more_than_1_month_aged_receivables = customer1_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :more_than_1_month }
      expect(customer1_more_than_1_month_aged_receivables).to be_present
      expect(customer1_more_than_1_month_aged_receivables.invoices).to include(invoice13, invoice132)
      expect(customer1_more_than_1_month_aged_receivables.total).to eq([invoice13, invoice132].sum(&:amount_price))

      customer2_more_than_1_month_aged_receivables = customer2_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :more_than_1_month }
      expect(customer2_more_than_1_month_aged_receivables).to be_present
      expect(customer2_more_than_1_month_aged_receivables.invoices).to include(invoice23)
      expect(customer2_more_than_1_month_aged_receivables.total).to eq([invoice23].sum(&:amount_price))
    end

    it 'contains summary for invoices due at least for 2 months' do
      customer1_more_than_2_months_aged_receivables = customer1_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :more_than_2_months }
      expect(customer1_more_than_2_months_aged_receivables).to be_present
      expect(customer1_more_than_2_months_aged_receivables.invoices).to include(invoice14)
      expect(customer1_more_than_2_months_aged_receivables.total).to eq([invoice14].sum(&:amount_price))

      customer2_more_than_2_month_aged_receivables = customer2_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :more_than_2_months }
      expect(customer2_more_than_2_month_aged_receivables).to be_present
      expect(customer2_more_than_2_month_aged_receivables.invoices).to include(invoice24)
      expect(customer2_more_than_2_month_aged_receivables.total).to eq([invoice24].sum(&:amount_price))
    end

    it 'contains summary for invoices due at least for 3 months' do
      customer1_more_than_3_months_aged_receivables = customer1_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :more_than_3_months }
      expect(customer1_more_than_3_months_aged_receivables).to be_present
      expect(customer1_more_than_3_months_aged_receivables.invoices).to include(invoice15, invoice152)
      expect(customer1_more_than_3_months_aged_receivables.total).to eq([invoice15, invoice152].sum(&:amount_price))

      customer2_more_than_3_month_aged_receivables = customer2_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :more_than_3_months }
      expect(customer2_more_than_3_month_aged_receivables).to be_present
      expect(customer2_more_than_3_month_aged_receivables.invoices).to include(invoice25, invoice252)
      expect(customer2_more_than_3_month_aged_receivables.total).to eq([invoice25, invoice252].sum(&:amount_price))
    end

    it 'contains summary for invoices due older than 4 months' do
      customer1_older_aged_receivables = customer1_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :older }
      expect(customer1_older_aged_receivables).to be_present
      expect(customer1_older_aged_receivables.invoices).to include(invoice16)
      expect(customer1_older_aged_receivables.total).to eq([invoice16].sum(&:amount_price))

      customer2_older_aged_receivables = customer2_aged_receivables.detect{|aged_receivable| aged_receivable.time_frame == :older }
      expect(customer2_older_aged_receivables).to be_present
      expect(customer2_older_aged_receivables.invoices).to include(invoice26, invoice262)
      expect(customer2_older_aged_receivables.total).to eq([invoice26, invoice262].sum(&:amount_price))
    end
  end

end