require 'rails_helper'

RSpec.describe Admin::Emails::SendReminderEmail, type: :service, emails: true, customers: true, invoices: true, notifications: true, reminders: true do
  include Rails.application.routes.url_helpers

  subject { Admin::Emails::SendReminderEmail.new(reminder: reminder).call }

  let!(:reminder) { create(:reminder, :random) }
  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :orders_email).and_return('orders-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :accounts_email).and_return('accounts-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
  end

  it 'returns the sent email' do
    reminder_sender = subject

    expect(reminder_sender).to be_success
    expect(reminder_sender.sent_notification).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Admin::Emails::SendReminderEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

    reminder_sender = subject
    expect(reminder_sender).to be_success
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject:  "Yordar - TASK REMINDER: #{reminder.title}",
        cc: anything,
        email_options: anything,
        email_variables: anything
      )

    subject
  end

  context 'Reminder recipients' do
    it 'sends the email to the default admin recipients (and cc)' do
      reminder.update_column(:recipients, %w[account_manager pantry_manager].sample)

      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        recipient: 'orders-email',
        subject: anything,
        cc: nil,
        email_options: anything,
        email_variables: anything
      )

      subject
    end

    it 'sends the email to the correct accounts recipients (and cc)' do
      reminder.update_column(:recipients, 'accounts_team')

      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        recipient: 'accounts-email',
        subject: anything,
        cc: nil,
        email_options: anything,
        email_variables: anything
      )

      subject
    end
  end # reminder recipients

  it 'sends an email with the correct email options' do
    email_ref = "#{Admin::Emails::SendReminderEmail::EMAIL_TEMPLATE}-#{reminder.id}-#{Time.zone.now.to_s(:date_spreadsheet)}"
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        cc: anything,
        email_options: { fk_id: reminder.id, ref: email_ref },
        email_variables: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct email data' do
      expected_email_variables = {
        reminder: anything,
        remindable: anything,

        header_color: :black
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end

    it 'sends email with the correct reminder details' do
      expected_reminder_details = {
        frequency: reminder.frequency,
        title: reminder.title,
        message: reminder.message
      }
      expected_email_variables = {
        remindable: anything,
        header_color: anything,

        reminder: deep_struct(expected_reminder_details)
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          cc: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end
  end # email variables

  context 'Customer Reminder' do
    let!(:customer) { create(:customer_profile, :random) }

    before do
      reminder.update_columns(remindable_type: 'CustomerProfile', remindable_id: customer.id)
    end

    it 'sends the email to the correct default recipients (and cc)' do
      expect(::Emails::Send).to receive(:new).with(template_name: anything,
        recipient: 'orders-email',
        subject: anything,
        cc: nil,
        email_options: anything,
        email_variables: anything
      )

      subject
    end

    it 'send the email with the appropriate subject' do
      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject:  "Yordar - TASK REMINDER for #{customer.name} - #{reminder.title}",
          cc: anything,
          email_options: anything,
          email_variables: anything
        )

      subject
    end

    context 'email_variables' do
      it 'sends email with the correct remindable details' do
        expected_remindable_details = {
          name: customer.name,
          company_name: customer.company_name,
          admin_url: customers_admin_url(customer_name: customer.email, host: 'default-host')
        }
        expected_email_variables = {
          reminder: anything,
          header_color: anything,

          remindable: deep_struct(expected_remindable_details)
        }

        expect(Emails::Send).to receive(:new).with(
            template_name: anything,
            recipient: anything,
            subject: anything,
            cc: anything,
            email_options: anything,
            email_variables: expected_email_variables
          )

        subject
      end
    end # email variables

    context 'Manager Email Recipients' do
      let!(:manager_type) { %w[account_manager pantry_manager].sample }

      let!(:manager1) { create(:customer_profile, :random, :with_user) }
      let!(:access_permission1) { create(:access_permission, :random, admin: manager1, customer_profile: customer, scope: manager_type) }

      let!(:manager2) { create(:customer_profile, :random, :with_user) }
      let!(:access_permission2) { create(:access_permission, :random, admin: manager2, customer_profile: customer, scope: manager_type) }

      before do
        reminder.update_column(:recipients, manager_type)
        print "      *#{manager_type}"
      end

      it 'sends the email to the correct managers based on reminder recipients (and cc)' do
        expect(::Emails::Send).to receive(:new).with(template_name: anything,
          recipient: [manager1, manager2].map(&:email_recipient).join(';'),
          subject: anything,
          cc: 'orders-email',
          email_options: anything,
          email_variables: anything
        )

        subject
      end
    end # manager email recipients
  end # customer reminder

  context 'with email sender failure' do
    before do
      unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
      allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

      # mock error logger
      allow_any_instance_of(Admin::Emails::SendReminderEmail).to receive(:log_errors).and_return(true)
    end

    it 'logs a notification error with the email sender errors' do
      reminder_sender = Admin::Emails::SendReminderEmail.new(reminder: reminder)

      expected_error_message = "Failed to send Reminder email for remindable #{reminder&.remindable_type} ID: #{reminder&.remindable_id} - #{reminder&.id}"
      expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
      expect(reminder_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

      reminder_sender.call
    end
  end # email sender error

end