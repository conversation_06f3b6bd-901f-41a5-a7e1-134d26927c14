require 'rails_helper'

RSpec.describe Admin::Emails::SendSupplierRegistrationEmail, type: :service, emails: true, admin: true, orders: true, woolworths: true do
  include Rails.application.routes.url_helpers

  subject { Admin::Emails::SendSupplierRegistrationEmail.new(supplier: supplier, registration: supplier_registration).call }


  let!(:supplier) { create(:supplier_profile, :random, :with_user) }
  let!(:supplier_registration) do
    SupplierRegistration.new({
      category_group: %w[catering-services kitchen-supplies].sample,
      is_team_supplier: [true, false].sample
    })
  end

  let!(:supplier_category) do
    supplier_registration.category_group == 'catering-services' ? 'Catering' : 'Snacks and Pantry'
  end

  let!(:email_sender) { double(::Emails::Send) }

  before do
    # mock email sender
    allow(::Emails::Send).to receive(:new).and_return(email_sender)
    successfull_response = OpenStruct.new(success?: true, email: 'sent-email')
    allow(email_sender).to receive(:call).and_return(successfull_response)

    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:default_host).and_return('default-host')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :supplier_email).and_return('supplier-email')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :admin_email).and_return('admin-email')
  end

  it 'returns the sent email' do
    sent_email = subject

    expect(sent_email).to eq('sent-email') # from above mock
  end

  it 'send the email with the correct email template' do
    expect(Emails::Send).to receive(:new).with(
        template_name: Admin::Emails::SendSupplierRegistrationEmail::EMAIL_TEMPLATE,
        recipient: anything,
        subject: anything,
        email_options: anything,
        email_variables: anything,
      )

    subject
  end

  it 'send the email with the appropriate subject' do
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: "YORDAR: New #{supplier_category} supplier registration",
        email_options: anything,
        email_variables: anything
      )

    subject
  end

  it 'sends the email to the correct recipients' do
    expect(::Emails::Send).to receive(:new).with(template_name: anything,
      recipient: 'supplier-email; admin-email',
      subject: anything,
      email_options: anything,
      email_variables: anything
    )

    subject
  end

  it 'sends an email with the correct email options' do
    email_ref = Admin::Emails::SendSupplierRegistrationEmail::EMAIL_TEMPLATE
    expect(Emails::Send).to receive(:new).with(
        template_name: anything,
        recipient: anything,
        subject: anything,
        email_options: { fk_id: supplier.id, ref: email_ref },
        email_variables: anything
      )

    subject
  end

  context 'email variables' do
    it 'sends email with the correct supplier data' do
      expected_supplier_data = {
        name: "#{supplier.user.firstname} #{supplier.user.lastname}",
        company_name: supplier.company_name,
        admin_url: suppliers_admin_url(company_name: supplier.email, host: yordar_credentials(:default_host)),
        category: supplier_category,
        is_team_supplier: supplier_registration.category_group == 'catering-services' && supplier_registration.is_team_supplier.present?
      }

      expected_email_variables = {
        supplier: deep_struct(expected_supplier_data),        
      }

      expect(Emails::Send).to receive(:new).with(
          template_name: anything,
          recipient: anything,
          subject: anything,
          email_options: anything,
          email_variables: expected_email_variables
        )

      subject
    end
  end

  context 'errors' do
    context 'with email sender failure' do
      before do
        unsuccessfull_response = OpenStruct.new(success?: false, email: nil, errors: ['email-sender-error'])
        allow(email_sender).to receive(:call).and_return(unsuccessfull_response)

        # mock error logger
        allow_any_instance_of(Admin::Emails::SendSupplierRegistrationEmail).to receive(:log_errors).and_return(true)
      end

      it 'logs a notification error with the email sender errors' do
        failed_order_email_sender = Admin::Emails::SendSupplierRegistrationEmail.new(supplier: supplier, registration: supplier_registration)

        expected_error_message = "Failed to send supplier registration email to admin for #{supplier.id}"
        expected_exception = Notifications::Base::NotificationError.new('email-sender-error')
        expect(failed_order_email_sender).to receive(:log_errors)#.with(exception: expected_exception, message: expected_error_message, sentry: true)

        failed_order_email_sender.call
      end
    end # email sender error
  end

end