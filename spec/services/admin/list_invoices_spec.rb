require 'rails_helper'

RSpec.describe Admin::ListInvoices, type: :service, admin: true, users: true, invoices: true do
  
  let!(:customer1) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer2) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer3) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false, allow_all_customer_access: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true, allow_all_customer_access: false) }
  let(:all_customer_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: true) }

  let(:yordar_admin) { [super_admin, admin].sample }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: false) }

  let!(:invoice1) { create(:invoice, :random, customer_profile: customer1) }
  let!(:invoice2) { create(:invoice, :random, customer_profile: customer2) }
  let!(:invoice3) { create(:invoice, :random, customer_profile: customer3) }

  context 'Access based filtering' do # filtered based on customer access (already tested for Admin::ListCustomers)
    it 'does not lists any invoices for missing user' do
      lister_options =  { for_user: nil, query: [true, false].sample ? invoice1.number : '' }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to be_blank
    end

    it 'super admins, Yordar Admins can list all invoices' do
      lister_options = { for_user: yordar_admin }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice1, invoice2, invoice3)
    end

    it 'User who can access all invoices only list invoices from invoices that are active' do
      customer2.user.update_column(:is_active, false)

      lister_options = { for_user: all_customer_admin }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice1, invoice3)
      expect(invoices).to_not include(invoice2)
    end

    it 'Non admin users cannot list any invoices' do
      lister_options = { for_user: non_admin_user }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to be_blank
    end

    context 'as Customer Team Admin' do
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer2) }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3) }

      before do
        customer_team_admin.profile.update_column(:user_id, non_admin_user.id) # attach non-admin-user record to company team admin profile
        non_admin_user.reload
      end

      it 'only lists invoices of customers the team admin has active access permission to' do
        lister_options = { for_user: non_admin_user }
        invoices = Admin::ListInvoices.new(options: lister_options).call

        expect(invoices).to include(invoice2, invoice3)
        expect(invoices).to_not include(invoice1)
      end

      it 'does not list invoices of customers with non-active access permissions' do
        admin_access_permission1.update_column(:active, false)
        lister_options = { for_user: non_admin_user }
        invoices = Admin::ListInvoices.new(options: lister_options).call

        expect(invoices).to include(invoice3)
        expect(invoices).to_not include(invoice1, invoice2)
      end

      it 'also lists its own invoices' do
        customer_admin_invoice = create(:invoice, :random, customer_profile: customer_team_admin)
        lister_options = { for_user: non_admin_user }
        invoices = Admin::ListInvoices.new(options: lister_options).call

        expect(invoices).to include(customer_admin_invoice)
        # expect(invoices).to include(invoice2, invoice3)
        # expect(invoices).to_not include(invoice1)
      end

      context 'with customer favourites' do
        let!(:favourite_customer2) { create(:favourite_customer, :random, favouriter: non_admin_user, customer_profile: customer2) }

        it 'only lists the invoices for the favourited customers' do
          lister_options = { for_user: non_admin_user, favourites_only: true }
          invoices = Admin::ListInvoices.new(options: lister_options).call

          expect(invoices).to include(invoice2)
          expect(invoices).to_not include(invoice1, invoice3)
        end
      end # with favoutire invoice customers (more like prioritized customers)
    end # As Company Team Admin
  end # Access based filtering

  context 'filter by query' do
    it 'filters invoices by their number' do
      lister_options = { for_user: yordar_admin, query: invoice3.number }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice3)
      expect(invoices).to_not include(invoice1, invoice2)
    end

    it 'filters invoices by their containing order ID' do
      customer2_order = create(:order, :delivered, customer_profile: customer2, update_with_invoice: true, invoice: invoice2)
      lister_options = { for_user: yordar_admin, query: customer2_order.id }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice2)
      expect(invoices).to_not include(invoice1, invoice3)
    end

    it 'filters invoices by their customer name' do
      lister_options = { for_user: yordar_admin, query: customer3.customer_name }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice3)
      expect(invoices).to_not include(invoice1, invoice2)
    end

    context 'company name' do
      let!(:company) { create(:company, :random, customer_profiles: [customer2]) }
      before do
        customer1.update_column(:company_name, Faker::Company.name)
      end

      it 'filters invoices by the customer\'s company name field' do
        lister_options = { for_user: yordar_admin, query: customer1.company_name }
        invoices = Admin::ListInvoices.new(options: lister_options).call

        expect(invoices).to include(invoice1)
        expect(invoices).to_not include(invoice2, invoice3)
      end

      it 'filters invoices by the customer\'s attached company name' do
        lister_options = { for_user: yordar_admin, query: company.name }
        invoices = Admin::ListInvoices.new(options: lister_options).call

        expect(invoices).to include(invoice2)
        expect(invoices).to_not include(invoice1, invoice3)
      end
    end
  end

  context 'filter by paid/unpaid payment status' do
    let!(:payment_status) { %w[paid unpaid].sample }

    before do
      [invoice1, invoice3].each do |invoice|
        invoice.update_column(:payment_status, payment_status)
      end
      invoice2.update_column(:payment_status, (%w[paid unpaid] - [payment_status]).sample)
    end

    it 'filters invoices which by paid/unpaid status' do
      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice1, invoice3)
      expect(invoices).to_not include(invoice2)
    end

    it 'only returns confirmed invoices' do
      invoice1.update_column(:status, %w[amended voided deleted].sample)

      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice3)
      expect(invoices).to_not include(invoice1, invoice2)
    end

    it 'only returns notifiable invoices' do
      invoice3.update_column(:do_not_notify, true)

      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice1)
      expect(invoices).to_not include(invoice2, invoice3)
    end
  end

  context 'filter by overdue payment status' do
    let!(:payment_status) { 'overdue' }

    before do
      [invoice2, invoice3].each do |invoice|
        invoice.update_columns(payment_status: 'unpaid', due_at: Time.zone.now - rand(1..10).days)
      end
      invoice1.update_columns(payment_status: 'unpaid', due_at: Time.zone.now + rand(1..10).days)
    end

    it 'filters invoices which by paid/unpaid status' do
      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice2, invoice3)
      expect(invoices).to_not include(invoice1)
    end

    it 'only returns unpaid invoices' do
      invoice3.update_column(:payment_status, %w[paid partial].sample)

      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice2)
      expect(invoices).to_not include(invoice1, invoice3)
    end

    it 'only returns confirmed invoices' do
      invoice2.update_column(:status, %w[amended voided deleted].sample)

      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice3)
      expect(invoices).to_not include(invoice1, invoice2)
    end

    it 'only returns notifiable invoices' do
      invoice3.update_column(:do_not_notify, true)

      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice2)
      expect(invoices).to_not include(invoice1, invoice3)
    end

    it 'only returns invoices due after the Xero SYNC_THRESHOLD_DATE' do
      invoice2.update_column(:due_at, Time.zone.parse(Xero::API::Base::SYNC_THRESHOLD_DATE) - rand(1..10).days)

      lister_options = { for_user: yordar_admin, payment_status: payment_status }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice3)
      expect(invoices).to_not include(invoice1, invoice2)
    end
  end

  context 'filter by Invoice' do
    it 'returns the passed in invoice if accessible' do
      lister_options = { for_user: yordar_admin, invoice: invoice2 }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice2)
      expect(invoices).to_not include(invoice1, invoice3)
    end

    it 'returns an empty list for non-invoice object' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { for_user: yordar_admin, invoice: supplier }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to be_empty
    end
  end

  context 'filter by favourites' do
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer1) }
    let!(:favourite_customer3) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer3) }

    it 'filters invoices of favourited customers only' do
      lister_options = { for_user: yordar_admin.reload, favourites_only: true }
      invoices = Admin::ListInvoices.new(options: lister_options).call

      expect(invoices).to include(invoice1, invoice3)
      expect(invoices).to_not include(invoice2)
    end
  end

end