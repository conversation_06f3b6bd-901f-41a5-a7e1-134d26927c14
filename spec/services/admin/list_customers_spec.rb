require 'rails_helper'

RSpec.describe Admin::ListCustomers, type: :service, admin: true, users: true, customers: true do
  
  let!(:customer1) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer2) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }
  let!(:customer3) { create(:customer_profile, :random, :with_user, customer_name: Faker::Name.name, company_name: Faker::Company.name) }

  let(:super_admin) { create(:user, :random, super_admin: true, admin: false, allow_all_customer_access: false) }
  let(:admin) { create(:user, :random, super_admin: false, admin: true, allow_all_customer_access: false) }
  let(:all_customer_admin) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: true) }

  let(:yordar_admin) { [super_admin, admin].sample }
  let(:non_admin_user) { create(:user, :random, super_admin: false, admin: false, allow_all_customer_access: false) }

  context 'Access based filtering' do
    it 'does not lists any customers customers for missing user' do
      lister_options =  { for_user: nil, query: [true, false].sample ? customer1.name : '' }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to be_blank
    end

    it 'super admins, Yordar Admins can list all customers' do
      lister_options = { for_user: yordar_admin }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer1, customer2, customer3)
    end

    it 'User who can access all customers only list customers that are active' do
      customer2.user.update_column(:is_active, false)

      lister_options = { for_user: all_customer_admin }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer1, customer3)
      expect(customers).to_not include(customer2)
    end

    it 'Non admin users cannot list any customers' do
      lister_options = { for_user: non_admin_user }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to be_blank
    end

    context 'as Customer Team Admin' do
      let!(:customer_team_admin) { create(:customer_profile, :random, :with_user, company_team_admin: true) }

      let!(:admin_access_permission1) { create(:access_permission, admin: customer_team_admin, customer_profile: customer1) }
      let!(:admin_access_permission2) { create(:access_permission, admin: customer_team_admin, customer_profile: customer3) }

      let!(:customer_team_admin_user) { customer_team_admin.reload.user } # non admin user

      it 'only lists customers that the team admin has active access permission to' do
        lister_options = { for_user: customer_team_admin_user }
        customers = Admin::ListCustomers.new(options: lister_options).call

        expect(customers).to include(customer1, customer3)
        expect(customers).to_not include(customer2)
      end

      it 'does not list customer with non-active access permissions' do
        admin_access_permission1.update_column(:active, false)
        lister_options = { for_user: customer_team_admin_user }
        customers = Admin::ListCustomers.new(options: lister_options).call

        expect(customers).to include(customer3)
        expect(customers).to_not include(customer1, customer2)
      end

      it 'also lists its own customer profile' do
        lister_options = { for_user: customer_team_admin_user }
        customers = Admin::ListCustomers.new(options: lister_options).call

        expect(customers).to include(customer_team_admin)
        # expect(customers).to include(customer1, customer3)
        # expect(customers).to_not include(customer2)
      end

      it 'only lists themselves when there are no active access permissions' do
        [admin_access_permission1, admin_access_permission2].each(&:destroy)

        lister_options = { for_user: customer_team_admin_user }
        customers = Admin::ListCustomers.new(options: lister_options).call

        expect(customers).to include(customer_team_admin)
        expect(customers).to_not include(customer1, customer2, customer3)
      end

      context 'Yordar Admins as Company Team Admins (as Account Managers)' do

        before do
          customer_team_admin_user.update_column(:admin, true) # make CTA a Yordar Admin
        end

        it 'lists all customers by default' do
         lister_options = { for_user: customer_team_admin_user }
         customers = Admin::ListCustomers.new(options: lister_options).call

         expect(customers).to include(customer_team_admin, customer1, customer2, customer3) 
        end

        it 'only lists customers it has access to when asking for my_customers (excluding self)' do
         lister_options = { for_user: customer_team_admin_user, my_customers: true }
         customers = Admin::ListCustomers.new(options: lister_options).call

         expect(customers).to include(customer1, customer3)
         expect(customers).to_not include(customer_team_admin, customer2)
        end
      end
    end # company team admin
  end # filter by access

  context 'filter by query' do
    it 'filters customers by their name' do
      lister_options = { for_user: yordar_admin, query: customer3.customer_name }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer3)
      expect(customers).to_not include(customer1, customer2)
    end

    it 'filters customers by their email' do
      lister_options = { for_user: yordar_admin, query: customer2.user.email }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer2)
      expect(customers).to_not include(customer1, customer3)
    end

    it 'filters customers by the company name' do
      lister_options = { for_user: yordar_admin, query: customer1.company_name }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer1)
      expect(customers).to_not include(customer2, customer3)
    end

    context 'with associated companies' do
      let!(:company1) { create(:company, :random, customer_profiles: [customer1]) }
      let!(:company3) { create(:company, :random, customer_profiles: [customer3]) }

      it 'filters customers by the associated company name' do
        lister_options = { for_user: yordar_admin, query: company3.name }
        customers = Admin::ListCustomers.new(options: lister_options).call

        expect(customers).to include(customer3)
        expect(customers).to_not include(customer1, customer2)
      end
    end
  end

  context 'filter by favourites' do
    let!(:favourite_customer1) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer1) }
    let!(:favourite_customer3) { create(:favourite_customer, :random, favouriter: yordar_admin, customer_profile: customer3) }

    it 'filters customers by the associated company name' do
      lister_options = { for_user: yordar_admin.reload, favourites_only: true }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer1, customer3)
      expect(customers).to_not include(customer2)
    end
  end

  context 'filter by customer' do
    it 'returns the passed in customer if accessible' do
      lister_options = { for_user: yordar_admin, customer: customer2 }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to include(customer2)
      expect(customers).to_not include(customer1, customer3)
    end

    it 'returns an empty list for non-customer profile based customer' do
      supplier = create(:supplier_profile, :random, :with_user)
      lister_options = { for_user: yordar_admin, customer: supplier }
      customers = Admin::ListCustomers.new(options: lister_options).call

      expect(customers).to be_empty
    end
  end

  it 'paginates customers according to page and limit (default sort of ID)' do
    lister_options = { for_user: yordar_admin, page: 2, limit: 1 }
    customers = Admin::ListCustomers.new(options: lister_options).call

    expect(customers).to include(customer2)
    expect(customers).to_not include(customer1, customer3)
  end

end