require 'rails_helper'

RSpec.describe Woolworths::GetAvailableAccount, type: :service, woolworths: true do

  let!(:account1) { create(:woolworths_account, :random, email: '<EMAIL>') }
  let!(:account2) { create(:woolworths_account, :random, email: '<EMAIL>') }
  let!(:account3) { create(:woolworths_account, :random, email: '<EMAIL>') }

  it 'returns the first account that is not connected to any Woolworths order' do
    available_account = Woolworths::GetAvailableAccount.new.call

    expect(available_account).to eq(account1) # first account
    expect(available_account).to_not eq(account2)
    expect(available_account).to_not eq(account3)
  end

  it 'only returns active accounts' do
    account1.update_column(:active, false)
    available_account = Woolworths::GetAvailableAccount.new.call

    expect(available_account).to_not eq(account1) # in-active
    expect(available_account).to eq(account2) # first in line
    expect(available_account).to_not eq(account3)
  end

  context 'with account connected to a woolworths order' do
    let!(:wooworths_order1) { create(:woolworths_order, :random, account: account1, account_in_use: true) }
    let!(:wooworths_order2) { create(:woolworths_order, :random, account: account2, account_in_use: false) }
    let!(:wooworths_order3) { create(:woolworths_order, :random, account: account3, account_in_use: true) }

    it 'returns an account that is connected to a Woolworths order but not in use' do
      available_account = Woolworths::GetAvailableAccount.new.call

      expect(available_account).to eq(account2) # woolworths order 2 used it and is no longer using it
      expect(available_account).to_not eq(account1) # redundant test
      expect(available_account).to_not eq(account3) # redundant test
    end

    it 'does not return an account that is connected to a Woolworths order with account in use false if there is other order that uses it' do
      create(:woolworths_order, :random, account: account2, account_in_use: true)

      available_account = Woolworths::GetAvailableAccount.new.call

      expect(available_account).to_not eq(account2) # woolworths order 4 is using it
      expect(available_account).to_not eq(account1)
      expect(available_account).to_not eq(account3)
    end

    it 'only returns active accounts' do
      account2.update_column(:active, false)
      available_account = Woolworths::GetAvailableAccount.new.call

      expect(available_account).to_not eq(account2) # not active account
      expect(available_account).to_not eq(account1) # redundant test
      expect(available_account).to_not eq(account3) # redundant test
    end
  end
end
