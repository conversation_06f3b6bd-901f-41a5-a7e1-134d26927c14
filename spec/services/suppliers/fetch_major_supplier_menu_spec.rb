require 'rails_helper'

RSpec.describe Suppliers::FetchMajorSupplierMenu, type: :service, suppliers: true, woolworths: true do

  let!(:supplier) { create(:supplier_profile, :random, :with_flags) }
  let!(:non_major_supplier) { create(:supplier_profile, :random) }

  let!(:markup_override_fetcher) { double(Suppliers::FetchMarkupOverride) }
  let!(:recent_orders_fetcher) { double(Suppliers::FetchRecentOrders) }

  let(:company) { create(:company, :random) }
  let(:customer) { create(:customer_profile, :random, company: company) }

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:woolworths, :supplier_profile_id).and_return(supplier.id)
    # expect(supplier).to be_is_major_supplier

    # mock fetching markup override
    allow(Suppliers::FetchMarkupOverride).to receive(:new).and_return(markup_override_fetcher)
    allow(markup_override_fetcher).to receive(:call).and_return(nil)

    # mock fetching recent orders
    allow(Suppliers::FetchRecentOrders).to receive(:new).and_return(recent_orders_fetcher)
    allow(recent_orders_fetcher).to receive(:call).and_return([])
  end

  it 'returns successfully for a valid supplier' do
    supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier).call

    expect(supplier_menu).to be_success
  end

  it 'return with errors if supplier is not valid' do
    supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: nil).call

    expect(supplier_menu).to_not be_success
    expect(supplier_menu.errors).to include('Please select a valid Supplier')
  end

  context 'as a non-searchable supplier' do
    before do
      supplier.update_column(:is_searchable, false)
    end

    it 'returns with an error if the passed in profile is not the actual supplier' do
      profile = [
        nil,
        create(:customer_profile, :random),
        non_major_supplier
      ].sample
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: profile).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('Please select an active Supplier')
    end

    it 'returns successfully when fetching menu for a non-searchable supplier as the supplier' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: supplier).call

      expect(supplier_menu).to be_success
    end
  end # non-searchable supplier

  it 'returns with errors if supplier is not a major supplier' do
    supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: non_major_supplier).call

    expect(supplier_menu).to_not be_success
    expect(supplier_menu.errors).to include('Please select a Major Supplier')
  end

  context 'with customer restrictions' do
    let!(:customer1) { create(:customer_profile, :random) }
    let!(:customer2) { create(:customer_profile, :random) }

    # restrict supplier to customer1
    let!(:customer_supplier_restriction) { create(:customer_profiles_supplier_profile, customer_profile: customer1, supplier_profile: supplier) }

    it 'returns successfully if supplier is visible to passed in profile (customer)' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer1).call

      expect(supplier_menu).to be_success
    end

    it 'returns with errors if passed in profile (customer) is not part of the suppliers restricted customers' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer2).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('You do not have access to this Supplier')
    end

    it 'returns with errors if profile isn\'t passed and the supplier has customer restrictions' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: nil).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('You do not have access to this Supplier')
    end

    it 'returns successfully for passed in profile if supplier does not have any restrictions' do
      supplier.customer_profiles = []
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer2).call

      expect(supplier_menu).to be_success
    end
  end

  context 'admin only supplier' do
    before do
      supplier.supplier_flags.update_column(:admin_only, true)
    end

    it 'returns with errors if supplier is admin only by default (or if is_admin is passed as false)' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, menu_options: [{}, { is_admin: false }, { is_admin: nil }].sample).call

      expect(supplier_menu).to_not be_success
      expect(supplier_menu.errors).to include('You do not have access to this Supplier')
    end

    it 'returns successfully for an admin only supplier if is_admin is passed as true' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, menu_options: { is_admin: true }).call

      expect(supplier_menu).to be_success
    end
  end

  context 'with menu items' do
    let!(:menu_section1) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_item11) { create(:menu_item, :random, menu_section: menu_section1, supplier_profile: supplier) }
    let!(:menu_item12) { create(:menu_item, :random, menu_section: menu_section1, supplier_profile: supplier) }

    let!(:menu_section2) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_item21) { create(:menu_item, :random, menu_section: menu_section2, supplier_profile: supplier) }
    let!(:menu_item22) { create(:menu_item, :random, menu_section: menu_section2, supplier_profile: supplier) }

    let!(:menu_section3) { create(:menu_section, :random, supplier_profile: supplier) }
    let!(:menu_item31) { create(:menu_item, :random, menu_section: menu_section3, supplier_profile: supplier) }
    let!(:menu_item32) { create(:menu_item, :random, menu_section: menu_section3, supplier_profile: supplier) }

    context 'initial load' do
      it 'returns back all valid menu sections' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        expect(section_grouped_menu_items.keys).to include(menu_section1, menu_section2, menu_section3)
        expect(section_grouped_menu_items.values.flatten).to be_blank
      end

      it 'does not return any menu items for any menu section(s)' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        fetched_menu_items = section_grouped_menu_items.values.flatten
        expect(fetched_menu_items).to be_blank
      end

      it 'does not return archived menu sections / menu items' do
        menu_section2.update_column(:archived_at, Time.now)
        menu_item32.update_column(:archived_at, Time.now)
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        expect(section_grouped_menu_items.keys).to_not include(menu_section2)
        expect(section_grouped_menu_items.keys).to include(menu_section3)
      end

      it 'does not return hidden menu sections / menu items' do
        menu_section3.update_column(:is_hidden, true)
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        expect(section_grouped_menu_items.keys).to_not include(menu_section3)
        expect(section_grouped_menu_items.keys).to include(menu_section1, menu_section2)
      end

      it 'does not return the custom menu_section' do
        menu_section2.update_column(:name, 'custom')
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        expect(section_grouped_menu_items.keys).to_not include(menu_section2)
      end
    end

    context 'menu section filtering' do
      it 'gets the items for the passed in menu_section' do
        menu_options = { menu_section: menu_section2 }
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections).to include(menu_section2)
        expect(menu_sections).to_not include(menu_section1, menu_section3)

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item21, menu_item22)
        expect(menu_items).to_not include(menu_item11, menu_item12, menu_item31, menu_item32)
      end

      it 'gets the items for the passed in search query' do
        [menu_item11, menu_item21, menu_item31].each{|item| item.update_column(:name, 'Same Name') }

        menu_options = { query: menu_item21.name }
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections.size).to eq(1)
        expect(menu_sections).to_not include(menu_section1, menu_section2, menu_section3)

        search_menu_section = menu_sections.first
        expect(search_menu_section.name).to eq("Searched for: #{menu_options[:query]}")

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to include(menu_item11, menu_item21, menu_item31)
        expect(menu_items).to_not include(menu_item12, menu_item22, menu_item32)
      end
    end

    context 'with customer favourites' do
      let(:customer) { create(:customer_profile, :random, :with_user) }
      let!(:favourite1) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item11, created_at: Time.zone.now - 1.day) }
      let!(:favourite2) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item22, created_at: Time.zone.now) }

      it 'returns the favourites for the passed in customer (sorted by LIFO)' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.favourite_menu_items).to include(menu_item11, menu_item22)
        expect(supplier_menu.favourite_menu_items).to_not include(menu_item12, menu_item21, menu_item31, menu_item32)

        # sorted
        expect(supplier_menu.favourite_menu_items[0]).to eq(menu_item22)
        expect(supplier_menu.favourite_menu_items[1]).to eq(menu_item11)
      end

      it 'does not return any favourites if profile is not passed' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: nil).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.favourite_menu_items).to be_empty
      end

      it 'does not return any favourites if profile is not a customer' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: [nil, non_major_supplier].sample).call

        expect(supplier_menu).to be_success
        expect(supplier_menu.favourite_menu_items).to be_empty
      end

      it 'returns with a (custom) favourite menu section (with no items)' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        grouped_menu_section = supplier_menu.section_grouped_menu_items.to_h
        favourite_menu_section = grouped_menu_section.keys.detect{|section| section.id == -1 }
        expect(favourite_menu_section).to be_present
        expect(favourite_menu_section.name).to eq('Favourites')
        expect(favourite_menu_section.group_name).to eq('My Favourites')

        expect(grouped_menu_section[favourite_menu_section]).to be_empty
      end

      context 'favourites only' do
        it 'only gets the favourites items (sorted by LIFO)' do
          menu_options = { favourites_only: true }
          supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer, menu_options: menu_options).call

          expect(supplier_menu).to be_success
          section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

          menu_sections = section_grouped_menu_items.keys
          expect(menu_sections.size).to eq(1)
          expect(menu_sections).to_not include(menu_section1, menu_section2, menu_section3)

          favourite_menu_section = menu_sections.first
          expect(favourite_menu_section.name).to eq('Favourites')
          expect(favourite_menu_section.group_name).to eq('My Favourites')

          menu_items = section_grouped_menu_items.values.flatten
          expect(menu_items).to include(menu_item11, menu_item22)
          expect(menu_items).to_not include(menu_item12, menu_item21, menu_item31, menu_item32)

          # sorted
          expect(menu_items[0]).to eq(menu_item22)
          expect(menu_items[1]).to eq(menu_item11)
        end
      end # favourites only
    end # with customer favourites
  end # with menu items

  context 'with supplier markup overrides', markup_overrides: true do
    let!(:markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, overridable: company) }

    before do
      allow(markup_override_fetcher).to receive(:call).and_return(markup_override)
    end

    it 'requests to fetch the markup override for the suppliers and customer' do
      expect(Suppliers::FetchMarkupOverride).to receive(:new).with(supplier: supplier, customer: customer, company: company, required_override: :markup)

      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call
      expect(supplier_menu).to be_success
    end

    it 'returns the supplier markup override for the passed in customer' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.markup_override).to eq(markup_override)
    end

    it 'doesn\'t fetch markup override if passed in profile is not a customer' do
      expect(Suppliers::FetchMarkupOverride).to_not receive(:new)

      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: [nil, supplier].sample).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.markup_override).to be_blank
    end
  end # markup overrides

  context 'with recent orders' do
    let!(:customer) { create(:customer_profile, :random) }
    let!(:order1) { create(:order, :confirmed) }
    let!(:order2) { create(:order, :confirmed) }

    before do
      allow(recent_orders_fetcher).to receive(:call).and_return([order1, order2])
    end

    it 'requests to fetch recent orders for the passed in customer profile and returns them' do
      expect(Suppliers::FetchRecentOrders).to receive(:new).with(supplier: supplier, customer: customer)

      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_present
      expect(supplier_menu.recent_orders.size).to eq(2)

      expect(supplier_menu.recent_orders).to include(order1, order2)
    end

    it 'returns with a (custom) recent orders menu section (with no items)' do
      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call

      expect(supplier_menu).to be_success
      grouped_menu_section = supplier_menu.section_grouped_menu_items.to_h
      recent_orders_menu_section = grouped_menu_section.keys.detect{|section| section.id == -3 }
      expect(recent_orders_menu_section).to be_present
      expect(recent_orders_menu_section.name).to eq('Order Again')
      expect(recent_orders_menu_section.group_name).to eq('Recent Orders')

      expect(grouped_menu_section[recent_orders_menu_section]).to be_empty
    end

    context 'with customer favourites' do
      let(:customer) { create(:customer_profile, :random, :with_user) }
      let!(:menu_item) { create(:menu_item, :random, supplier_profile: supplier) }
      let!(:favourite) { create(:customer_profile_menu_item, customer_profile: customer, menu_item: menu_item) }

      it 'returns recent orders but does not add the (custom) recen orders menu section' do
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer).call

        expect(supplier_menu).to be_success
        grouped_menu_section = supplier_menu.section_grouped_menu_items.to_h
        recent_orders_menu_section = grouped_menu_section.keys.detect{|section| section.id == -3 }
        expect(recent_orders_menu_section).to be_blank
      end
    end

    it 'does not request to fetch recent orders if passed in order is a non-draft order' do
      non_draft_order = create(:order, status: (Order::VALID_ORDER_STATUSES - ['draft']).sample)

      expect(Suppliers::FetchRecentOrders).to_not receive(:new)

      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer, order: non_draft_order).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_blank
    end

    it 'does not request to fetch recent orders if not fetching the full menu' do
      option = %i[query menu_section favourites_only recent_orders_only].sample
      menu_options = {}
      menu_options[option] = case option
      when :menu_section
        create(:menu_section, :random, supplier_profile: supplier)
      when :favourites_only
        true
      else # :query
        'random-string'
      end 
      expect(Suppliers::FetchRecentOrders).to_not receive(:new)

      supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, menu_options: menu_options, profile: customer).call

      expect(supplier_menu).to be_success
      expect(supplier_menu.recent_orders).to be_blank
    end

    context 'recent orders only' do
      it 'returns recent orders section without any items' do
        menu_options = { recent_order_only: true }
        supplier_menu = Suppliers::FetchMajorSupplierMenu.new(supplier: supplier, profile: customer, menu_options: menu_options).call

        expect(supplier_menu).to be_success
        section_grouped_menu_items = supplier_menu.section_grouped_menu_items.to_h

        menu_sections = section_grouped_menu_items.keys
        expect(menu_sections.size).to eq(1)

        favourite_menu_section = menu_sections.first
        expect(favourite_menu_section.id).to eq(-3)
        expect(favourite_menu_section.name).to eq('Order Again')
        expect(favourite_menu_section.group_name).to eq('Recent Orders')

        menu_items = section_grouped_menu_items.values.flatten
        expect(menu_items).to be_blank
      end
    end # recent orders only
  end # recent orders
end
