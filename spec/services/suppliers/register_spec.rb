require 'rails_helper'

RSpec.describe Suppliers::Register, type: :service, suppliers: true do

  let!(:suburb) { create(:suburb, :random) }
  let!(:lead_list_id) { rand(20..30) }

  let!(:registration) do
    SupplierRegistration.new(
      firstname: Faker::Name.first_name,
      lastname: Faker::Name.first_name,
      email: Faker::Internet.email,
      password: SecureRandom.hex(7),
      company_name: Faker::Company.name,
      company_address: Faker::Address.street_address,
      suburb_id: suburb.id,
      bsb_number: Faker::Bank.swift_bic,
      bank_account_number: Faker::Bank.account_number,
      phone: Faker::PhoneNumber.phone_number,
      mobile: Faker::PhoneNumber.phone_number,
      abn_acn: Faker::Company.ein,
      category_group: %w[catering-services kitchen-supplies].sample,
      is_team_supplier: [true, false].sample
    )
  end

  before do
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(anything, anything).and_return('')
    allow_any_instance_of(Object).to receive(:yordar_credentials).with(:active_campaign, :lead_list_id).and_return(lead_list_id.to_s)

    # mock Hubspot syncer
    contact_syncer = delayed_syncer = double(Hubspot::SyncContact)
    allow(Hubspot::SyncContact).to receive(:new).and_return(contact_syncer)
    allow(contact_syncer).to receive(:delay).and_return(delayed_syncer)
    allow(delayed_syncer).to receive(:call).and_return(true)

    # mock email senders
    email_sender = delayed_email_sender = double(Suppliers::Emails::SendWelcomeEmail)
    allow(Suppliers::Emails::SendWelcomeEmail).to receive(:new).and_return(email_sender)
    allow(email_sender).to receive(:delay).and_return(delayed_email_sender)
    allow(delayed_email_sender).to receive(:call).and_return(true)

    # mock admin email sender
    admin_email_sender = delayed_admin_email_sender = double(Admin::Emails::SendSupplierRegistrationEmail)
    allow(Admin::Emails::SendSupplierRegistrationEmail).to receive(:new).and_return(admin_email_sender)
    allow(admin_email_sender).to receive(:delay).and_return(delayed_admin_email_sender)
    allow(delayed_admin_email_sender).to receive(:call).and_return(true)

    # mock event logger
    event_logger = delayed_event_logger = double(EventLogs::Create)
    allow(EventLogs::Create).to receive(:new).and_return(event_logger)
    allow(event_logger).to receive(:delay).and_return(delayed_event_logger)
    allow(delayed_event_logger).to receive(:call).and_return(true)
  end

  it 'creates a new user record with passed in supplier registration' do
    supplier_registration = Suppliers::Register.new(registration: registration).call

    expect(supplier_registration).to be_success

    created_user = supplier_registration.user
    expect(created_user).to be_present
    expect(created_user.firstname).to eq(registration.firstname)
    expect(created_user.lastname).to eq(registration.lastname)
    expect(created_user.email).to eq(registration.email)
    expect(created_user.suburb).to eq(suburb)
  end

  it 'creates a new supplier record with the passed in registration fields' do
    supplier_registration = Suppliers::Register.new(registration: registration).call

    expect(supplier_registration).to be_success
    created_user = supplier_registration.user

    created_supplier = supplier_registration.supplier
    expect(created_supplier).to be_present
    expect(created_supplier).to be_a(SupplierProfile)
    expect(created_supplier.user).to eq(created_user)

    Suppliers::Register::SUPPLIER_FIELDS.each do |field|
      expect(created_supplier.send(field)).to eq(registration.send(field))
    end

    expect(created_supplier.company_address_suburb).to eq(suburb)
    expect(created_supplier.is_searchable).to be_falsey
    expect(created_supplier.uuid).to be_present
  end

  it 'creates a new (associated) supplier flags record' do
    supplier_registration = Suppliers::Register.new(registration: registration).call

    expect(supplier_registration).to be_success
    created_supplier = supplier_registration.supplier

    expect(created_supplier.supplier_flags).to be_present
    expect(created_supplier.supplier_flags).to be_persisted
  end

  it 'sanitizes the params' do
    unsanitized_registration = registration.dup
    unsanitized_registration.firstname = 'name-with-spaces-after   '
    unsanitized_registration.lastname = '  name-with-before-spaces'
    unsanitized_registration.company_name = 'company-with-spaces-after   '

    supplier_registration = Suppliers::Register.new(registration: unsanitized_registration).call

    expect(supplier_registration).to be_success
    created_user = supplier_registration.user
    created_supplier = supplier_registration.supplier

    expect(created_user.firstname).to eq('name-with-spaces-after')
    expect(created_user.lastname).to eq('name-with-before-spaces')

    expect(created_supplier.company_name).to eq('company-with-spaces-after')
    # expect(created_supplier.role).to eq('role-with-before-spaces')
  end

  context 'with supplier (sustainable) flags' do
    let!(:flag_map) { SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.map{|_| [true, false].sample } }

    let!(:registration_with_flags) do
      rego_with_flags = registration.dup
      SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.each_with_index do |field, idx|
        rego_with_flags.send("#{field}=", flag_map[idx])
      end
      rego_with_flags
    end

    it 'sets supplier with the passed in sustainable supplier flags' do
      supplier_registration = Suppliers::Register.new(registration: registration_with_flags).call

      expect(supplier_registration).to be_success
      created_supplier = supplier_registration.supplier
      SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS.each_with_index do |field, idx|
        expect(created_supplier.send(field)).to eq(flag_map[idx])
      end
    end
  end # with supplier (sustainable) flags

  context 'Hubspot Integration', hubspot: true do
    it 'syncs with Hubspot' do
      expect(Hubspot::SyncContact).to receive(:new).with(contact: anything, refresh: true) # passes the newly created user
      expect(Hubspot::SyncContact).to_not receive(:new).with(contact: anything, refresh: true) # called only once

      supplier_registration = Suppliers::Register.new(registration: registration).call
      expect(supplier_registration).to be_success
    end
  end

  it 'sends a welcome email to the supplier' do
    expect(Suppliers::Emails::SendWelcomeEmail).to receive(:new).with(supplier: anything) # passes the newly created customer

    supplier_registration = Suppliers::Register.new(registration: registration).call
    expect(supplier_registration).to be_success
  end

  it 'notifies admin about new supplier registration' do
    expect(Admin::Emails::SendSupplierRegistrationEmail).to receive(:new).with(supplier: anything, registration: registration) # passes the newly created customer

    supplier_registration = Suppliers::Register.new(registration: registration).call
    expect(supplier_registration).to be_success
  end

  it 'logs a `New Registration` event', event_logs: true do
    event_info = { category_group: registration.category_group }
    event_info[:is_team_supplier] = true if registration.category_group == 'catering-services' && registration.is_team_supplier
    expect(EventLogs::Create).to receive(:new).with(event_object: anything, event: 'new-supplier-registration', **event_info) # event object is the created supplier

    supplier_registration = Suppliers::Register.new(registration: registration).call
    expect(supplier_registration).to be_success
  end

  context 'errors' do
    it 'returns an error with an invalid registration' do
      invalid_supplier_registrations = registration
      field = %i[firstname company_name email password].sample
      invalid_supplier_registrations.send("#{field}=", nil)

      supplier_registration = Suppliers::Register.new(registration: invalid_supplier_registrations).call

      expect(supplier_registration).to_not be_success
      # expect(supplier_registration.errors).to include('Cannot register without a valid email and password')
    end

    it 'returns an error if user with passed in emaill already exists' do
      supplier = create(:supplier_profile, :random, :with_user)
      existing_supplier_registration = registration
      existing_supplier_registration.email = supplier.user.email
      supplier_registration = Suppliers::Register.new(registration: existing_supplier_registration).call

      expect(supplier_registration).to_not be_success
      expect(supplier_registration.errors).to include('An account already exists with these details')
    end

    it 'errors and rollbacks all records if the supplier creation failed' do
      # mock a validation failure on supplier (_profile)
      stub_const('Suppliers::Register::SUPPLIER_FIELDS', %i[company_name email company_address phone mobile abn_acn bsb_number bank_account_number lead_mode])
      registration.lead_mode = 'invalid-lead-mode'

      supplier_registration = Suppliers::Register.new(registration: registration).call

      expect(supplier_registration).to_not be_success
      expect(supplier_registration.errors).to include('Lead mode is not included in the list')

      expect(supplier_registration.user).to be_present # creates a user
      expect(supplier_registration.user).to_not be_persisted # but rollbacks its save

      expect(supplier_registration.supplier).to be_blank
    end
  end

end
