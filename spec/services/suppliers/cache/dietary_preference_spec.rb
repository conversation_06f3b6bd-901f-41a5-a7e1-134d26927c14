require 'rails_helper'

RSpec.describe Suppliers::Cache::DietaryPreference, type: :service, supplier: true do

  let!(:preference_supplier) { create(:supplier_profile, :random, :with_flags, is_searchable: true, company_name: 'preference-supplier') }
  let!(:non_preference_supplier) { create(:supplier_profile, :random, :with_flags, is_searchable: true, company_name: 'non-preference-supplier') }

  context 'without any menu items' do
    Suppliers::Cache::DietaryPreference::POSSIBLE_PREFERENCES.each do |preference|
      it "sets has_#{preference} preference to false" do
        preference_setter = Suppliers::Cache::DietaryPreference.new(preference: preference).call

        expect(preference_setter).to be_success
        expect(preference_supplier.reload.send("has_#{preference}_items")).to be_falsey
        expect(non_preference_supplier.reload.send("has_#{preference}_items")).to be_falsey
      end
    end
  end

  Suppliers::Cache::DietaryPreference::POSSIBLE_PREFERENCES.each_with_index do |preference, pidx|
    context "with at least 5 is_#{preference} items" do
      # 4 items for each possible preference flag true
      Suppliers::Cache::DietaryPreference::POSSIBLE_PREFERENCES.each do |flag|        
        4.times do |all_num|
          let!("menu_item-#{pidx}-#{all_num}_pref_#{flag}") { create(:menu_item, :random, supplier_profile: preference_supplier, "is_#{flag}" => true) }
          let!("menu_item-#{pidx}-#{all_num}_non_#{flag}") { create(:menu_item, :random, supplier_profile: non_preference_supplier, "is_#{flag}" => true) }
        end
      end

      # extra item with preference flag = true
      let!("menu_item-#{pidx}-#{preference}") { create(:menu_item, :random, supplier_profile: preference_supplier, "is_#{preference}" => true) }

      it "sets the supplier has_#{preference}_items flag" do
        preference_setter = Suppliers::Cache::DietaryPreference.new(preference: preference).call

        expect(preference_setter).to be_success

        expect(preference_supplier.reload.send("has_#{preference}_items")).to be_truthy        
        expect(non_preference_supplier.reload.send("has_#{preference}_items")).to_not be_truthy
      end
    end # with preference items
  end # POSSIBLE_PREFERENCES

end
