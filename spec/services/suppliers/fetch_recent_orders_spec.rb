require 'rails_helper'

RSpec.describe Suppliers::FetchRecentOrders, type: :service, suppliers: true, orders: true do
  
  let!(:supplier) { create(:supplier_profile, :random) }

  let!(:customer) { create(:customer_profile, :random) }
  let!(:order1) { create(:order, :confirmed, customer_profile: customer, order_type: 'one-off', order_variant: 'general') }
  let!(:order_line1) { create(:order_line, :random, order: order1, supplier_profile: supplier) }

  let!(:order2) { create(:order, :confirmed, customer_profile: customer, order_type: 'one-off', order_variant: 'general') }
  let!(:order_line2) { create(:order_line, :random, order: order2, supplier_profile: supplier) }

  it 'returns the recent orders for the passed in customer orders with supplier items' do
    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(2)
    expect(recent_orders).to include(order1, order2)
  end

  it 'only returns orders for the passed in supplier only' do
    supplier2 = create(:supplier_profile, :random)
    order_line1.update_column(:supplier_profile_id, supplier2.id)

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(1)
    expect(recent_orders).to include(order2)
    expect(recent_orders).to_not include(order1)
  end

  it 'only returns active orders' do
    order2.update_column(:status, (Order::VALID_ORDER_STATUSES - %w[new confirmed amended delivered]).sample)

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(1)
    expect(recent_orders).to include(order1)
    expect(recent_orders).to_not include(order2)
  end

  it 'only returns one-off orders' do
    order1.update_column(:order_type, [nil, 'recurrent'].sample)

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(1)
    expect(recent_orders).to include(order2)
    expect(recent_orders).to_not include(order1)
  end

  it 'only returns normal/general orders' do
    order2.update_column(:order_type, ['team_order', 'recurrring_team_order'].sample)

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(1)
    expect(recent_orders).to include(order1)
    expect(recent_orders).to_not include(order2)
  end

  it 'only returns orders in the last 1 year' do
    order1.update_column(:created_at, (Time.zone.now - 1.year - 1.day)) # before the last year
    order2.update_column(:created_at, (Time.zone.now - 1.year + 1.day)) # within the last year

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(1)
    expect(recent_orders).to include(order2)
    expect(recent_orders).to_not include(order1)
  end

  it 'only returns recently created 2 orders' do
    order3 = create(:order, :confirmed, customer_profile: customer, order_type: 'one-off', order_variant: 'general')
    create(:order_line, :random, order: order3, supplier_profile: supplier)

    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: customer).call

    expect(recent_orders.size).to eq(2)
    expect(recent_orders).to include(order2, order3)
    expect(recent_orders).to_not include(order1)
  end

  it 'doesn\'t return recent orders for a non-suppliers' do
    recent_orders = Suppliers::FetchRecentOrders.new(supplier: [nil, customer].sample, customer: customer).call

    expect(recent_orders).to be_blank
  end

  it 'doesn\'t return recent orders for a non-customers' do
    recent_orders = Suppliers::FetchRecentOrders.new(supplier: supplier, customer: [nil, supplier].sample).call

    expect(recent_orders).to be_blank
  end

end