require 'rails_helper'

RSpec.describe Customers::FetchManagers, type: :service, customers: true, access_permissions: true do

  let!(:manager_type) { %w[account_manager pantry_manager].sample }
  let!(:customer) { create(:customer_profile, :random) }
  let!(:manager1) { create(:customer_profile, :random) }
  let!(:manager2) { create(:customer_profile, :random) }

  before do
    print "    *#{manager_type}*"
  end

  context 'as direct manager access permissions' do
    let!(:access_permission1) { create(:access_permission, admin: manager1, customer_profile: customer, scope: manager_type) }
    let!(:access_permission2) { create(:access_permission, admin: manager2, customer_profile: customer, scope: manager_type) }

    it 'fetches the managers via direct linkage (access permissions with scope or manager_type)' do
      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      expect(managers).to be_present
      expect(managers.size).to eq(2)
      expect(managers.sample).to be_a(CustomerProfile)
      expect(managers).to include(manager1, manager2)
    end

    it 'only returns passed in manager type' do
      access_permission2.update_column(:scope, (AccessPermission::VALID_SCOPES - [manager_type]).sample)

      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      expect(managers.size).to eq(1)
      expect(managers).to_not include(manager2)
      expect(managers).to include(manager1)
    end

    it 'only returns active managers' do
      access_permission1.update_column(:active, false)

      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      expect(managers.size).to eq(1)
      expect(managers).to_not include(manager1)
      expect(managers).to include(manager2)
    end

    it 'only returns a unique list of managers' do
      access_permission2.update_column(:admin_id, manager1.id)

      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      expect(managers.size).to eq(1)
      expect(managers).to include(manager1)
      expect(managers).to_not include(manager2)
    end
  end

  context 'as managers of company team admins and/or pantry managers' do
    let!(:company_team_admin) { create(:customer_profile, :random) }
    let!(:access_permission1) { create(:access_permission, admin: company_team_admin, customer_profile: customer, scope: 'company_team_admin') }

    let!(:pantry_manager) { create(:customer_profile, :random) }
    let!(:access_permission2) { create(:access_permission, admin: pantry_manager, customer_profile: customer, scope: 'pantry_manager') }

    let!(:access_permission3) { create(:access_permission, admin: manager1, customer_profile: company_team_admin, scope: manager_type) }
    let!(:access_permission4) { create(:access_permission, admin: manager2, customer_profile: pantry_manager, scope: manager_type) }

    it 'fetches the managers of their imediate company team admin / pantry manager' do
      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      if manager_type == 'pantry_manager'
        expect(managers.size).to eq(3)
        expect(managers).to include(manager1, manager2, pantry_manager)
      else
        expect(managers.size).to eq(2)
        expect(managers).to include(manager1, manager2)
      end
    end

    it 'only returns managers (with scope of manager type) of company team admin / pantry manager' do
      access_permission3.update_column(:scope, (AccessPermission::VALID_SCOPES - [manager_type]).sample)

      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      if manager_type == 'pantry_manager'
        expect(managers.size).to eq(2)
        expect(managers).to_not include(manager1)
        expect(managers).to include(manager2, pantry_manager)
      else
        expect(managers.size).to eq(1)
        expect(managers).to_not include(manager1)
        expect(managers).to include(manager2)
      end
    end

    it 'only returns active managers of company team admin / pantry manager' do
      access_permission4.update_column(:active, false)

      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      expect(managers).to_not include(manager2)
      expect(managers).to include(manager1)
    end

    it 'only returns managers of active company team admin / pantry manager' do
      access_permission4.update_column(:active, false)

      managers = Customers::FetchManagers.new(customer: customer, type: manager_type).call

      expect(managers).to_not include(manager2)
      expect(managers).to include(manager1)
    end
  end

end