require 'rails_helper'

RSpec.describe <PERSON>minder, type: :model do

  context 'validations' do
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_presence_of(:message) }
    it { is_expected.to validate_presence_of(:frequency) }
    it { is_expected.to validate_inclusion_of(:frequency).in_array(Reminder::VALID_FREQUENCIES) }
    it { is_expected.to validate_presence_of(:starting_at) }
    it { is_expected.to validate_presence_of(:recipients) }
    it { is_expected.to validate_inclusion_of(:recipients).in_array(Reminder::VALID_RECIPIENTS) }

    it 'has a valid factory' do
      factory_reminder = build(:reminder, :random)

      expect(factory_reminder).to be_valid
    end

    it 'has default values' do
      reminder = create(:reminder, :random)

      expect(reminder.active).to be_truthy
    end
  end

  context 'associations' do
    it { is_expected.to belong_to(:remindable) }
  end

  context 'instance methods' do
    let!(:reminder) { create(:reminder, :random) }

    describe '.formatted_frequency' do
      it 'returns the appropriate text based on frequency' do
        <PERSON>minder::VALID_FREQUENCIES.each do |frequency|
          reminder.frequency = frequency
          case frequency
          when 'weekly'
            expect(reminder.formatted_frequency).to eq("Every #{reminder.starting_at.strftime('%A')}")
          when 'fortnightly'
            expect(reminder.formatted_frequency).to eq("Fortnightly on #{reminder.starting_at.strftime('%As')}")
          when 'monthly_by_date'
            expect(reminder.formatted_frequency).to eq("#{reminder.starting_at.day.ordinalize} of each Month")
          when 'monthly_by_weekday'
            expect(reminder.formatted_frequency).to eq("1st #{reminder.starting_at.strftime('%A')} of each Month")
          end
        end
      end
    end

    describe '.sent_emails' do
      let!(:email1) { create(:email, :random, template_name: Admin::Emails::SendReminderEmail::EMAIL_TEMPLATE, fk_id: reminder.id, sent_at: Time.zone.now - rand(1..10).days) }
      let!(:email2) { create(:email, :random, template_name: Admin::Emails::SendReminderEmail::EMAIL_TEMPLATE, fk_id: reminder.id, sent_at: nil) }
      let!(:email3) { create(:email, :random, sent_at: Time.zone.now - rand(1..10).days) }
      let!(:email4) { create(:email, :random, template_name: Admin::Emails::SendReminderEmail::EMAIL_TEMPLATE, fk_id: reminder.id, sent_at: Time.zone.now - rand(1..10).days) }

      it 'returns the emails with the Admin reminder email template and reminder ID' do
        expect(reminder.sent_emails).to include(email1, email4)
        expect(reminder.sent_emails).to_not include(email2) # unsent
        expect(reminder.sent_emails).to_not include(email3) # not belonging to reminder
      end
    end
  end

end
