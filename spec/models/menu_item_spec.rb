require 'rails_helper'

RSpec.describe MenuItem, type: :model do

	context 'validations' do
		it { is_expected.to validate_presence_of(:name) }
		it { is_expected.to validate_length_of(:name).is_at_most(255).with_message('- Item name is too long (max 255)') }
		it { is_expected.to validate_presence_of(:menu_section_id) }
		it { is_expected.to validate_inclusion_of(:freight_type).in_array(MenuItem::VALID_FREIGHT_TYPES) }

		it { is_expected.to validate_numericality_of(:price).is_greater_than_or_equal_to(0).on(:update) }
		it { is_expected.to validate_numericality_of(:minimum_quantity).is_greater_than_or_equal_to(1).on(:update).allow_nil }

		it 'has a valid factory' do
			factory_menu_item = build(:menu_item, :random)

			expect(factory_menu_item).to be_valid
		end
	end

	context 'associations' do
		it { is_expected.to belong_to(:menu_section) }
		it { is_expected.to belong_to(:supplier_profile) }
		it { is_expected.to have_many(:serving_sizes) }
		it { is_expected.to have_many(:active_serving_sizes) }
		it { is_expected.to have_many(:order_lines) }
		it { is_expected.to have_many(:rate_cards) }
		it { is_expected.to have_many(:menu_extra_sections) }
		it { is_expected.to have_many(:menu_extras) }
		it { is_expected.to have_many(:direct_menu_extras) }
		it { is_expected.to have_many(:woolworths_store_availabilities) }
	end

	context 'instance methods' do
		describe '.image?' do
			it 'returns truthy if the menu item contians a value for the image field' do
				menu_item = build(:menu_item, :random, image: [nil, 'image-url.jpg'].sample)

				expect(menu_item.image?).to eq(menu_item.image.present?)
			end
		end

		describe '.ci_image' do
			it 'removes image/upload/ value from the image url' do
				menu_item = build(:menu_item, :random, image: 'image/upload/some-uploaded-image-url.jpg')

				expect(menu_item.image_id).to eq('some-uploaded-image-url.jpg')
			end

			it 'sanitizes the image (url) value containing a hash (#) value' do
				menu_item = build(:menu_item, :random, image: 'some-image-url-with-hash.jpg#hash-value')

				expect(menu_item.image_id).to eq('some-image-url-with-hash.jpg')
			end
		end

		describe '.image_id' do
			it 'removes image/upload/ value from the image url' do
				menu_item = build(:menu_item, :random, image: 'image/upload/some-uploaded-image-url.jpg')

				expect(menu_item.image_id).to eq('some-uploaded-image-url.jpg')
			end

			it 'sanitizes the image (url) value containing a hash (#) value' do
				menu_item = build(:menu_item, :random, image: 'some-image-url-with-hash.jpg#hash-value')

				expect(menu_item.image_id).to eq('some-image-url-with-hash.jpg')
			end

			it 'only returns the part after the whole cloudinary endpoint of https://res.cloudinary.com/yordar-p' do
				menu_item = build(:menu_item, :random, image: 'https://res.cloudinary.com/yordar-p/some-image-with-cloiudinary-url.jpg')

				expect(menu_item.image_id).to eq('some-image-with-cloiudinary-url.jpg')
			end
		end

		describe '.is_visible' do # used for deprecated menu CSV import
			it 'shows the boolean opposite of is_hidden' do
				menu_item = build(:menu_item, :random, is_hidden: [true, false].sample)

				expect(menu_item.is_visible).to eq(!menu_item.is_hidden)
			end
		end

		context 'Price / Cost' do
			let!(:price) { rand(5.22...10.98) }
			let!(:supplier) { create(:supplier_profile, :random, commission_rate: 0.0, markup: 0.0) }
			let!(:menu_item) { create(:menu_item, :random, price: price, supplier_profile: supplier) }

			before do
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :au).and_return(0.1)
				allow_any_instance_of(Object).to receive(:yordar_credentials).with(:yordar, :gst_percent, :nz).and_return(0.15)
			end

			describe '.markup_price' do
				it 'returns the price as saved in the record' do
					expect(menu_item.markup_price.round(2)).to be_within(0.2).of(price.round(2))
				end

				it 'returns the gst inclusive pricing (based on passed in country)' do
					expect(menu_item.markup_price(gst_country: 'au').round(2)).to be_within(0.2).of((price * 1.1).round(2))
					expect(menu_item.markup_price(gst_country: 'nz').round(2)).to be_within(0.2).of((price * 1.15).round(2))
				end

				it 'returns the price for a gst inclusive pricing if item is gst free' do
					menu_item.update_column(:is_gst_free, true)

					expect(menu_item.markup_price(gst_country: %i[au nz].sample).round(2)).to be_within(0.2).of(price.round(2))
				end

				context 'with supplier markup' do
					let!(:markup) { [10, 20, 30].sample }

					before do
						supplier.update_column(:markup, markup)
					end

					it 'returns the price including supplier markup' do
						expect(menu_item.markup_price.round(2)).to be_within(0.2).of((price.to_f * (1 + markup.to_f / 100)).round(2))
					end

					it 'returns the gst inclusive pricing (based on country) including supplier markup' do
						expect(menu_item.markup_price(gst_country: 'au').round(2)).to be_within(0.2).of(((price.to_f * (1 + markup.to_f / 100)) * 1.1).round(2))
						expect(menu_item.markup_price(gst_country: 'nz').round(2)).to be_within(0.2).of(((price.to_f * (1 + markup.to_f / 100)) * 1.15).round(2))
					end

					context 'with supplier markup override' do
						let!(:markup_override) { [15, 25, 35].sample }
						let!(:supplier_markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, markup: markup_override) }

						it 'returns the price including supplier\'s overriden markup' do
							expect(menu_item.markup_price(override: supplier_markup_override).round(2)).to be_within(0.2).of((price.to_f * (1 + markup_override.to_f / 100)).round(2))
						end

						it 'returns the price including supplier\'s default markup if override is not passed' do
							markup_price = menu_item.markup_price(override: nil).round(2)
							expect(markup_price).to_not be_within(0.2).of((price.to_f * (1 + markup_override.to_f / 100)).round(2))
							expect(markup_price).to be_within(0.2).of((price.to_f * (1 + markup.to_f / 100)).round(2))
						end

						it 'returns the gst inclusive pricing (based on country) including supplier markup' do
							expect(menu_item.markup_price(gst_country: 'AU', override: supplier_markup_override).round(2)).to be_within(0.2).of(((price.to_f * (1 + markup_override.to_f / 100)) * 1.1).round(2))
							expect(menu_item.markup_price(gst_country: 'NZ', override: supplier_markup_override).round(2)).to be_within(0.2).of(((price.to_f * (1 + markup_override.to_f / 100)) * 1.15).round(2))
						end
					end # with supplier markup override
				end # with supplier markup
			end # .markup_price

			describe '.cost' do
				it 'returns the price for a menu item of a supplier with no commission (rate)' do
					expect(menu_item.cost.round(2)).to be_within(0.2).of(price.to_f.round(2))
				end

				it 'returns the price - commission for a menu item of a supplier with commission (rate)' do
					commission = [10, 15, 20].sample
					supplier.update_column(:commission_rate, commission)

					expect(menu_item.cost.round(2)).to be_within(0.2).of((price.to_f * (1 - commission.to_f / 100)).round(2))
				end

				context 'with supplier markup override' do
					let!(:commission_override) { [15, 25, 35].sample }
					let!(:supplier_commission_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, commission_rate: commission_override) }

					it 'returns the price - commission override' do
						expect(menu_item.cost(override: supplier_commission_override).round(2)).to be_within(0.2).of((price.to_f * (1 - commission_override.to_f / 100)).round(2))
					end
				end # markup overrides
			end # .cost
		end # Price / Cost

		context 'Price / Cost with promo_price', promotional_item: true do
			let!(:supplier) { create(:supplier_profile, :random, commission_rate: 0.0, markup: 0.0) }
			let!(:price) { rand(5.22...10.98) }
			let!(:promo_price) { rand(5.22...10.98) }
			let!(:menu_item_with_promo) { create(:menu_item, :random, price: price, promo_price: promo_price, supplier_profile: supplier) }

			describe '.markup_price(promotional: true..)' do
				it 'returns the promo price as saved in the record' do
					expect(menu_item_with_promo.markup_price(promotional: true).round(2)).to be_within(0.2).of(promo_price.round(2))
				end

				it 'returns the gst inclusive (based on country) promo pricing' do
					expect(menu_item_with_promo.markup_price(gst_country: 'AU', promotional: true).round(2)).to be_within(0.2).of((promo_price * 1.1).round(2))
					expect(menu_item_with_promo.markup_price(gst_country: 'NZ', promotional: true).round(2)).to be_within(0.2).of((promo_price * 1.15).round(2))
				end

				it 'returns the promo price for a gst inclusive pricing if item is gst free' do
					menu_item_with_promo.update_column(:is_gst_free, true)

					expect(menu_item_with_promo.markup_price(gst_country: %i[au nz].sample, promotional: true).round(2)).to be_within(0.2).of(promo_price.round(2))
				end

				context 'with supplier markup' do
					let!(:markup) { [10, 20, 30].sample }

					before do
						supplier.update_column(:markup, markup)
					end

					it 'returns the promo price including supplier markup' do
						expect(menu_item_with_promo.markup_price(promotional: true).round(2)).to be_within(0.2).of((promo_price.to_f * (1 + markup.to_f / 100)).round(2))
					end

					it 'returns the gst inclusive (based on country) promo pricing including supplier markup' do
						expect(menu_item_with_promo.markup_price(gst_country: 'au', promotional: true).round(2)).to be_within(0.2).of(((promo_price.to_f * (1 + markup.to_f / 100)) * 1.1).round(2))
						expect(menu_item_with_promo.markup_price(gst_country: 'nz', promotional: true).round(2)).to be_within(0.2).of(((promo_price.to_f * (1 + markup.to_f / 100)) * 1.15).round(2))
					end

					context 'with supplier markup override' do
						let!(:markup_override) { [15, 25, 35].sample }
						let!(:supplier_markup_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, markup: markup_override) }

						it 'returns the promo price including supplier\'s overriden markup' do
							expect(menu_item_with_promo.markup_price(override: supplier_markup_override, promotional: true).round(2)).to be_within(0.2).of((promo_price.to_f * (1 + markup_override.to_f / 100)).round(2))
						end

						it 'returns the promo price including supplier\'s default markup if override is not passed' do
							markup_price = menu_item_with_promo.markup_price(override: nil, promotional: true).round(2)
							expect(markup_price).to_not be_within(0.2).of((promo_price.to_f * (1 + markup_override.to_f / 100)).round(2))
							expect(markup_price).to be_within(0.2).of((promo_price.to_f * (1 + markup.to_f / 100)).round(2))
						end

						it 'returns the gst inclusive (based on country) promo pricing including supplier markup' do
							expect(menu_item_with_promo.markup_price(gst_country: 'AU', override: supplier_markup_override, promotional: true).round(2)).to be_within(0.2).of(((promo_price.to_f * (1 + markup_override.to_f / 100)) * 1.1).round(2))
							expect(menu_item_with_promo.markup_price(gst_country: 'NZ', override: supplier_markup_override, promotional: true).round(2)).to be_within(0.2).of(((promo_price.to_f * (1 + markup_override.to_f / 100)) * 1.15).round(2))
						end
					end # with supplier markup override
				end # with supplier markup
			end # .markup_price(promotional: true...)

			describe '.cost' do
				it 'returns the promo price for a menu item of a supplier with no commission (rate)' do
					expect(menu_item_with_promo.cost.round(2)).to be_within(0.2).of(promo_price.to_f.round(2))
				end

				it 'returns the promo price - commission for a menu item of a supplier with commission (rate)' do
					commission = [10, 15, 20].sample
					supplier.update_column(:commission_rate, commission)

					expect(menu_item_with_promo.cost.round(2)).to be_within(0.2).of((promo_price.to_f * (1 - commission.to_f / 100)).round(2))
				end

				context 'with supplier markup override' do
					let!(:commission_override) { [15, 25, 35].sample }
					let!(:supplier_commission_override) { create(:supplier_markup_override, :random, supplier_profile: supplier, commission_rate: commission_override) }

					it 'returns the price - commission override' do
						expect(menu_item_with_promo.cost(override: supplier_commission_override).round(2)).to be_within(0.2).of((promo_price.to_f * (1 - commission_override.to_f / 100)).round(2))
					end
				end # cost with markup overrides
			end # .cost
		end # Price / Cost with promo price

	end # instance methods

end
