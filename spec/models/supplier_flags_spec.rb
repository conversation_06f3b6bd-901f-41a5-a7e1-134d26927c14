require 'rails_helper'

RSpec.describe SupplierFlags, type: :model, suppliers: true do

  describe 'validations' do
    it { is_expected.to validate_inclusion_of(:billing_frequency).in_array(SupplierFlags::VALID_BILLING_FREQUENCIES) }
    it { is_expected.to validate_inclusion_of(:payment_term_days).in_array(SupplierFlags::VALID_TERM_DAYS) }

    it 'has a valid factory' do
      supplier_flags = build(:supplier_flags)

      expect(supplier_flags).to be_valid
    end

    it 'has default boolean value of false' do
      supplier_flags = build(:supplier_flags)

      boolean_fields = (
        SupplierProfile::SUPPLIER_IS_FLAGS +
        SupplierProfile::SUPPLIER_SUSTAINABLE_FLAGS +
        SupplierProfile::SUPPLIER_PROVIDES_FLAGS +
        SupplierProfile::SUPPLIER_HAS_FLAGS +
        SupplierProfile::SUPPLIER_USES_FLAGS +
        SupplierProfile::SUPPLIER_NEED_FLAGS +
        SupplierProfile::SUPPLIER_CAN_FLAGS
      )
      boolean_fields.each do |field|
        expect(supplier_flags.send(field)).to be_falsey
      end
    end

    it 'has default (non-boolean) values set from DB', rgi: true do
      supplier_flags = build(:supplier_flags)
      expect(supplier_flags.billing_frequency).to eq('weekly')
      expect(supplier_flags.payment_term_days).to eq(45)
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:supplier_profile) }
  end

end
