FactoryBot.define do

	factory :user do
		email { Faker::Internet.email }
		password { SecureRandom.hex(7) }
		firstname { Faker::Name.name.split(' ').first }

		trait :confirmed do
			confirmed_at { rand(1.day..10.weeks).ago }
		end

		trait :random do
			admin { false }
			super_admin { false }
			lastname { Faker::Name.name.split(' ').last }
		end

		trait :admin do
			admin { true }
			super_admin { false }
		end

		trait :as_customer do
			admin { false }
			after(:create) do |user|
				customer = create(:customer_profile, :random)
				create(:profile, user: user, profileable: customer)
			end
		end

		trait :as_supplier do
			admin { false }
			after(:create) do |user|
				supplier = create(:supplier_profile, :random)
				create(:profile, user: user, profileable: supplier)
			end
		end

	end

end
