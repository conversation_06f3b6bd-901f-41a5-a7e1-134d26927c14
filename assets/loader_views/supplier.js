import { loadJSView, loadReactView } from './loadView';

// JS Views
const supplierOrders = loadJSView('supplier/supplierOrders');
const supplierOrder = loadJSView('supplier/supplierOrder');
const supplierOrderSummary = loadJSView('supplier/supplierOrderSummary');
const supplierReports = loadJSView('supplier/supplierReports');
const supplierAccountSettings = loadJSView('supplier/supplierAccountSettings');
const supplierInvoices = loadJSView('supplier/supplierInvoices');

// React Views
const supplierMenu = loadReactView('suppliers/menu');
const supplierClosureDates = loadReactView('suppliers/closureDates');
const supplierMinimums = loadReactView('suppliers/minimums')
const supplierDeliveryZones = loadReactView('suppliers/deliveryZones')

const supplierViews = {
  supplierOrders,
  supplierOrder,
  supplierOrderSummary,
  supplierReports,
  supplierAccountSettings,
  supplierInvoices,

  supplierMenu,
  supplierClosureDates,
  supplierMinimums,
  supplierDeliveryZones,
};

export default supplierViews;