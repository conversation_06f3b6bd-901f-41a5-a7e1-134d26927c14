import colors, { rangedColorPalette } from './colors';

export function getMonthsAgoDate(months) {
  const date = new Date();
  const monthsAgo = date.getMonth() - months;
  date.setMonth(monthsAgo);
  return date;
}

export function getReportMonthLabel(month) {
  return new Intl.DateTimeFormat('en-US', { month: 'short', year: 'numeric' }).format(month);
}

export function dateToDateString(date, day) {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;

  return [year, month, day].join('-');
}

// Chart used for customers
export const barOptions = {
  barThickness: 50,
  maintainAspectRatio: false,
  scales: {
    x: {
      stacked: true,
      barPercentage: '10%',
      grid: {
        display: false,
      },
      ticks: {
        font: {
          family: 'Museo Slab',
          size: 14,
        },
      },
    },
    y: {
      stacked: true,
      barPercentage: '10%',
      ticks: {
        font: {
          family: 'Museo Slab',
          size: 14,
        },
        callback(value) {
          return `$${value.toLocaleString()}`;
        },
      },
    },
  },
  plugins: {
    tooltip: {
      mode: 'index',
      // removes a category from tooltip if spend is 0
      filter(tooltipItem) {
        const value = tooltipItem.raw;
        if (value === 0) {
          return false;
        }
        return true;
      },
      callbacks: {
        label: (context) => {
          let label = '';
          if (context.parsed.y) {
            label = `${context.dataset.label}: $${Number(context.parsed.y.toFixed(2)).toLocaleString(undefined, {
              minimumFractionDigits: 2,
            })}`;
          }
          return label;
        },
        // add total spend
        afterBody: (ttItem) => {
          const spends = ttItem.filter((item) => item.dataset.label !== 'Budget');
          const budget = ttItem.filter((item) => item.dataset.label === 'Budget');
          const totalSpend = Number(spends.reduce((acc, val) => acc + Number(val.raw), 0));
          const budgetSpend = Number(budget.reduce((acc, val) => acc + Number(val.raw), 0));
          const underBudget = budgetSpend >= totalSpend;
          const roundedString = (number) => number.toFixed(2).toLocaleString(undefined, { minimumFractionDigits: 2 });
          let budgetSpendText = null;
          if (budgetSpend > 0) {
            budgetSpendText = underBudget
              ? `Remaining Budget Spend: $${roundedString(budgetSpend - totalSpend)}`
              : `Exceeded Budget Spend: $${roundedString(totalSpend - budgetSpend)}`;
          }
          const labels = [`Total Spend: $${roundedString(totalSpend)}`];
          if (budgetSpendText) {
            labels.push(budgetSpendText);
          }
          return labels;
        },
      },
    },
    legend: {
      display: false,
    },
  },
};

// used by Supplier Reports in Admin
export const barOptionsAdmin = {
  barThickness: 50,
  maintainAspectRatio: false,
  scales: {
    x: {
      stacked: true,
      barPercentage: '10%',
      grid: {
        display: false,
      },
      ticks: {
        font: {
          family: 'Museo Slab',
          size: 14,
        },
      },
    },
    y: {
      stacked: true,
      barPercentage: '10%',
      ticks: {
        font: {
          family: 'Museo Slab',
          size: 14,
        },
        callback(value) {
          return `$${value.toLocaleString()}`;
        },
      },
    },
  },
  plugins: {
    tooltip: {
      mode: 'index',
      // removes a category from tooltip if spend is 0
      filter(tooltipItem) {
        const value = tooltipItem.raw;
        if (value === 0) {
          return false;
        }
        return true;
      },
      callbacks: {
        label: (context) => {
          let label = '';
          if (context.parsed.y) {
            label = `${context.dataset.label}: $${Number(context.parsed.y.toFixed(2)).toLocaleString(undefined, {
              minimumFractionDigits: 2,
            })}`;
          }
          return label;
        },
        // add Margin
        afterBody: (ttItem) => {
          const customerSpend = Number(ttItem.find((item) => item.dataset.label == 'Customer Spend').raw);
          const supplierCost = Number(ttItem.find((item) => item.dataset.label == 'Supplier Cost').raw);
          const marginPercent = ((customerSpend - supplierCost)/customerSpend) * 100;
          const labels = [`Margin: ${marginPercent.toFixed(2).toLocaleString(undefined, { minimumFractionDigits: 2 })}%`]
          return labels;
        },
      },
    },
    legend: {
      display: false,
    },
  },
};

export const generateBarData = (reportData) => ({
  labels: reportData.map((data) => data.label),
  datasets: [
    ...(reportData.some((data) => data.budget !== 0)
      ? [
        {
          label: 'Budget',
          type: 'line',
          data: reportData.map((data) => data.budget),
          backgroundColor: reportData.map((data) => {
            const totalSpend = parseFloat(data.catering_spend) + parseFloat(data.snacks_spend);
            return parseFloat(data.budget) <= totalSpend ? '#FA4290' : '#2BFFCF';
          }),
          stack: 'budget',
          borderColor: 'black',
          borderWidth: 1,
          pointRadius: 5,
          pointStyle: reportData.map((data) => {
            const totalSpend = parseFloat(data.catering_spend) + parseFloat(data.snacks_spend);
              if (data.budget === 0) {
                return 'dash';
              }
              return parseFloat(data.budget) <= totalSpend ? 'triangle' : 'circle';
            }),
        },
      ]
      : []),
    {
      label: 'Catering',
      type: 'bar',
      data: reportData.map((data) => data.catering_spend),
      backgroundColor: '#241c15',
      stack: 'spend',
    },
    {
      label: 'Snacks',
      type: 'bar',
      data: reportData.map((data) => data.snacks_spend),
      backgroundColor: '#dedede',
      stack: 'spend',
    },
  ],
});

export const pantryManagerBarOptions = {
  indexAxis: 'y',
  barThickness: 10,
  maintainAspectRatio: false,
  scales: {
    x: {
      stacked: true,
      grid: {
        display: false,
      },
      ticks: {
        font: {
          family: 'Museo Sans',
          size: 14,
        },
        callback(value) {
          return `${value.toLocaleString()} hrs`;
        },
      },
    },
    y: {
      stacked: true,
      categoryPercentage: 0.4,
      barPercentage: 0.8,
      ticks: {
        font: {
          family: 'Museo Sans',
          size: 14,
        },
      },
    },
  },
  plugins: {
    tooltip: {
      mode: 'index',
      // removes a category from tooltip if spend is 0
      filter(tooltipItem) {
        const value = tooltipItem.raw;
        if (value === 0) {
          return false;
        }
        return true;
      },
      displayColors: false,
      callbacks: {
        label: (context) => {
          const labels = [];
          const customerHours = context.dataset.customerHours[context.dataIndex];
          if (customerHours.length) {
            customerHours.forEach((data) => {
              labels.push(`${data.customer}: ${data.hours} hrs`);
            });
          }
          if (customerHours.length > 1) labels.push(`Total: ${context.parsed.x} hrs`);
          return labels;
        },
      },
    },
    legend: {
      display: false,
    },
  },
};

export const generatePantryManagerData = (managerSpends, activePantryManager) => ({
  labels: managerSpends.map((managerSpend) => managerSpend.pantry_manager),
  datasets: [
    {
      label: 'Hours',
      type: 'bar',
      data: managerSpends.map((managerSpend) => managerSpend.hours),
      customerHours: managerSpends.map((managerSpend) => managerSpend.spends),
      backgroundColor: managerSpends.map((managerSpend) => {
        if (activePantryManager) {
          if (managerSpend.pantry_manager == activePantryManager.value) {
            return '#000';
          } else {
            return '#eee';
          }
        } else if (managerSpend.is_unassigned) {
          return '#ec5840';
        } else {
          return '#000';
        }
      })
    },
  ],
});

export const setDoughnutCategoryData = (categorySpend, activeGroup) => {
  if (!categorySpend?.[activeGroup]) {
    return {
      datasets: [
        {
          label: 'Spend',
          data: [-0.01],
          backgroundColor: 'white',
          borderColor: '#999',
          borderWidth: 1,
          labels: ['No Spend'],
        },
      ],
    };
  }
  return {
    datasets: [
      {
        label: 'Spend',
        data: categorySpend?.[activeGroup]?.map((data) => data.value),
        backgroundColor: categorySpend?.[activeGroup]?.map((data) => colors[activeGroup]?.[data.label]),
        borderColor: '#999',
        borderWidth: 1,
        labels: categorySpend?.[activeGroup]?.map((data) => data.label),
      },
    ],
  };
};

export const setDoughnutSupplierData = (activeSpend, isEthical) => {
  if (!activeSpend?.length) {
    return {
      datasets: [
        {
          label: isEthical ? 'Ethical Spend' : 'Spend',
          data: [-0.01],
          backgroundColor: 'white',
          borderColor: '#999',
          borderWidth: 1,
          labels: ['No Spend'],
        },
      ],
    };
  }
  return {
    datasets: [
      {
        label: isEthical ? 'Ethical Spend' : 'Spend',
        data: activeSpend.map((data) => data.value),
        backgroundColor: rangedColorPalette(activeSpend.length, '#FFF0DD', '#3E505B'),
        borderColor: '#555',
        borderWidth: 1,
        labels: activeSpend.map((data) => data.label),
      },
    ],
  };
};

export const doughnutPlugins = [
  {
    beforeDraw(chart) {
      const { width, height, ctx, config } = chart;
      const textYSpacing = 12;
      ctx.restore();
      ctx.font = '16px Museo Slab';
      ctx.textBaseline = 'top';
      const spends = config._config.data.datasets[0].data;      
      if (config._config.data.datasets[0].label == 'Ethical Spend') {
        const labels = config._config.data.datasets[0].labels;
        const maxIdx = spends.reduce((iMax, x, i, arr) => Number(x) > Number(arr[iMax]) ? i : iMax, 0);        
        const maxSpend = Number(spends[maxIdx]);
        const maxLabel = labels[maxIdx];
        const title = maxLabel;
        const spend = `$${maxSpend.toLocaleString()}`;
        const titleX = Math.round((width - ctx.measureText(title).width) / 2);
        const titleY = height / 2 - textYSpacing;
        const spendX = Math.round((width - ctx.measureText(spend).width) / 2);
        const spendY = height / 2 + textYSpacing;
        ctx.fillText(title, titleX, titleY);
        ctx.fillText(spend, spendX, spendY);
      } else {
        const totalSpend = spends.reduce((acc, cur) => Number(cur) + acc, 0);
        const title = 'Total Spend';
        let spend = `$${totalSpend.toLocaleString()}`;
        if (totalSpend < 0) spend = '$0';
        const titleX = Math.round((width - ctx.measureText(title).width) / 2);
        const titleY = height / 2 - textYSpacing;
        const spendX = Math.round((width - ctx.measureText(spend).width) / 2);
        const spendY = height / 2 + textYSpacing;
        ctx.fillText(title, titleX, titleY);
        ctx.fillText(spend, spendX, spendY);
      }      
      ctx.save();
    },
  },
];

export const doughnutOptions = {
  tooltipTemplate: "<%if (label){%><%=label %>: <%}%><%= value + ' %' %>",
  plugins: {
    tooltip: {
      callbacks: {
        title: (segment) => segment[0].dataset.labels[segment[0].dataIndex],
        label: (context) => {
          let label = '';
          if (context.parsed) {
            label = `Spend: $${Number(context.parsed.toFixed(2)).toLocaleString()}`;
          }
          return label;
        },
      },
    },
  },
  responsive: true,
  cutout: '75%',
};
