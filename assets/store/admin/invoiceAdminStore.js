import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { invoiceUpdateFields } from 'utilities/adminHelpers';
import { apiAdminInvoicesPath, apiAdminInvoicePath } from 'routes';

const InvoicePaginationLimit = 20;

const initialState = {
  hasFavourites: false,
  favouritesOnly: true,
  paymentStatus: '',
  invoices: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const invoiceAdminStore = create((set, get) => ({
  ...initialState,
  setFilters: (filters) => set({ ...filters }),
  setFavourites: (favouritesOnly) => {
    const { fetchInvoices } = get();
    setLocalStorage({ favouritesOnly });

    set({ favouritesOnly });
    fetchInvoices({ page: 1 });
  },
  setQuery: (query) => {
    const { hasFavourites, fetchInvoices } = get();
    if (!!query) {
      setLocalStorage({
        ...(!!hasFavourites && { favouritesOnly: false }),
      });
    }
    set({
      query,
      ...(!!query && !!hasFavourites && { favouritesOnly: false }),
    });
    fetchInvoices({ page: 1 });
  },
  setPaymentStatus: (paymentStatus) => {
    const { fetchInvoices } = get();
    setLocalStorage({ paymentStatus });

    set({ paymentStatus });
    fetchInvoices({ page: 1 });
  },
  fetchInvoices: async ({ page }) => {
    const { query, hasFavourites, favouritesOnly, paymentStatus } = get();
    set({ loadingList: page == 1, loadingMore: page > 1 });
    if (page == 1) {
      set({ invoices: [] });
    }
    const { data: invoices } = await axios({
      method: 'GET',
      url: apiAdminInvoicesPath(),
      params: {
        query,
        page,
        ...(!!hasFavourites && !!favouritesOnly && { favourites_only: true }),
        ...(!!paymentStatus && { payment_status: paymentStatus }),
      },
    });

    set((state) => ({
      invoices: page == 1 ? invoices : [...state.invoices, ...invoices],
      page: page + 1,
      hasMore: invoices.length == InvoicePaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  updateInvoice: async ({ invoice }) => {
    const sanitizedInvoice = {}
    invoiceUpdateFields.forEach((field) => (sanitizedInvoice[field] = invoice[field]));
    const { data: updatedInvoice } = await axios({
      method: 'PUT',
      url: apiAdminInvoicePath(invoice),
      data: { invoice: sanitizedInvoice },
      headers: csrfHeaders(),
    });

    set((state) => ({
      invoices: state.invoices.map((invoice) => (invoice.id === updatedInvoice.id ? updatedInvoice : invoice)),
    }));
  },
}));

function setLocalStorage({ ...filter }) {
  const storageKey = 'InvoiceFilters';
  const existingFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));
  delete (existingFilters.query); // only required for a while. TO-DO - remove after a few weeks

  const updatedFilter = { ...existingFilters, ...filter };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

export default invoiceAdminStore;
