import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { apiAdminRemindersPath, apiAdminReminderPath } from 'routes';

const ReminderPaginationLimit = 20;

const initialState = {
  reminders: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const reminderAdminStore = create((set, get) => ({
  ...initialState,
  fetchReminders: async ({ page }) => {
    const { query } = get();
    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page === 1) {
      set({ reminders: [] });
    }
    const { data: reminders } = await axios({
      method: 'GET',
      url: apiAdminRemindersPath(),
      params: {
        query,
        page,
      },
    });

    set((state) => ({
      reminders: page === 1 ? reminders : [...state.reminders, ...reminders],
      page: page + 1,
      hasMore: reminders.length === ReminderPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  createReminder: async ({ reminder }) => {
    try {
      const { data: createdReminder } = await axios({
        method: 'POST',
        url: apiAdminRemindersPath(),
        data: { reminder },
        headers: csrfHeaders(),
      });

      set((state) => ({
        reminders: [createdReminder, ...state.reminders],
      }));
      toast.success(`Create new reminder with title '${createdReminder.title}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Reminder');
      throw new Error('There was an error creating a new reminder');
    }
  },
  updateReminder: async ({ reminder }) => {
    try {
      const { data: updatedReminder } = await axios({
        method: 'PUT',
        url: apiAdminReminderPath(reminder),
        data: { reminder },
        headers: csrfHeaders(),
      });
      set((state) => ({
        reminders: state.reminders.map((reminder) => (reminder.id === updatedReminder.id ? updatedReminder : reminder)),
      }));
      toast.success(`Updated reminder: ${updatedReminder.title}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error updating the reminder');
      throw new Error('There was an error updating the reminder');
    }
  },
  removeReminder: async ({ reminder }) => {
    try {
      await axios({
        method: 'DELETE',
        url: apiAdminReminderPath(reminder),
        headers: csrfHeaders(),
      });
      set((state) => ({
        reminders: state.reminders.filter((stateReminder) => stateReminder.id != reminder.id),
      }));
      toast.success(`Removed reminder: ${reminder.title}`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error removing the reminder');
    }
  },
  setQuery: (query) => set({ query }),
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if (error?.response?.status === 422) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default reminderAdminStore;
