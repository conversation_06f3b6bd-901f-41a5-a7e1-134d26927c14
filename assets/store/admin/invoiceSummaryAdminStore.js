import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { invoiceUpdateFields } from 'utilities/adminHelpers';
import { apiAdminAgedReceivablesPath } from 'routes';

const SummaryPaginationLimit = 20;

const initialState = {
  timeFrame: '',
  agedReceivables: [],
  query: '',
  loadingList: false,
};

const invoiceSummaryAdminStore = create((set, get) => ({
  ...initialState,
  setFilters: (filters) => set({ ...filters }),
  setQuery: (query) => {
    const { fetchAgedReceivables } = get();
 
    set({ query })
    fetchAgedReceivables();
  },
  setTimeFrame: (timeFrame) => {
    const { fetchAgedReceivables } = get();
    setLocalStorage({ timeFrame });

    set({ timeFrame });
    fetchAgedReceivables();
  },
  fetchAgedReceivables: async () => {
    const { query, timeFrame } = get();
    set({ loadingList: true, agedReceivables: [] });
    const { data: agedReceivables } = await axios({
      method: 'GET',
      url: apiAdminAgedReceivablesPath({ format: 'json' }),
      params: {
        ...(!!query && { query }),
        ...(!!timeFrame && { time_frame: timeFrame }),
      },
    });

    set((state) => ({
      agedReceivables,
      loadingList: false,
    }));
  },
}));

function setLocalStorage({ ...filter }) {
  const storageKey = 'InvoiceSummaryFilters';
  const existingFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));

  const updatedFilter = { ...existingFilters, ...filter };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

export default invoiceSummaryAdminStore;
