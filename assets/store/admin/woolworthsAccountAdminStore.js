import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';
import { apiAdminWoolworthsAccountsPath, apiAdminWoolworthsAccountPath } from 'routes';
import { woolworthsAccountUpdateFields } from 'utilities/adminHelpers';

const AccountsPaginationLimit = 20;

const initialState = {
  accounts: [],
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
};

const woolworthsAccountAdminStore = create((set, get) => ({
  ...initialState,
  fetchAccounts: async ({ page }) => {
    set({ loadingList: page === 1, loadingMore: page > 1 });
    if (page === 1) {
      set({ accounts: [] });
    }
    const { data: accounts } = await axios({
      method: 'GET',
      url: apiAdminWoolworthsAccountsPath(),
      params: {
        page,
      },
    });

    set((state) => ({
      accounts: page === 1 ? accounts : [...state.accounts, ...accounts],
      page: page + 1,
      hasMore: accounts.length === AccountsPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  createAccount: async ({ account }) => {
    try {
      const { data: createdAccount } = await axios({
        method: 'POST',
        url: apiAdminWoolworthsAccountsPath(),
        data: { woolworths_account: account },
        headers: csrfHeaders(),
      });

      set((state) => ({
        accounts: [createdAccount, ...state.accounts],
      }));
      toast.success(`Created new Woolworths with email '${createdAccount.email}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      handleError(error, 'There was an error creating a new Account');
      throw new Error('There was an error creating a new account');
    }
  },
  updateAccount: async ({ account, detachFromOrderID }) => {
    try {
      const sanitizedAccount = {}
      woolworthsAccountUpdateFields.forEach((field) => (sanitizedAccount[field] = account[field]));
      const { data: updatedAccount } = await axios({
        method: 'PUT',
        url: apiAdminWoolworthsAccountPath(account),
        data: {
          ...(!detachFromOrderID && { woolworths_account: account }),
          ...(detachFromOrderID && { detach_from_order_id: detachFromOrderID }),
        },
        headers: csrfHeaders(),
      });
      set((state) => ({
        accounts: state.accounts.map((account) => (account.id === updatedAccount.id ? updatedAccount : account)),
      }));
      if (detachFromOrderID) {
        toast.success(`Detached Order from Account: ${updatedAccount.short_name}`, { ...defaultToastOptions, autoClose: 5000 });
      } else {
        toast.success(`Updated account: ${updatedAccount.short_name}`, { ...defaultToastOptions, autoClose: 5000 });
      }
    } catch (error) {
      handleError(error, 'There was an error updating the account');
      throw new Error('There was an error updating the account');
    }
  },
}));

function handleError(error, message = '') {
  let errorMessage = message;
  if ([422, 404].includes(error?.response?.status)) {
    errorMessage = error.response.data.errors.join('. ');
  }
  if (!errorMessage) errorMessage = 'Oops! Something went wrong!. Please try again';
  toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 });
}

export default woolworthsAccountAdminStore;
