import create from 'zustand';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

import {
  apiAdminCustomersPath,
  apiAdminCustomerPath,
  apiAdminCustomerMarkCustomerAsFavouritePath,
  apiAdminCustomerAccessPermissionsPath,
  apiAdminCustomerDeliveryOverridesPath,
  apiAdminCustomerAddAdminableCustomerPath,
  apiAdminUserPath,
  apiAdminUserDeprecateUserPath,
} from 'routes';

import { userUpdateFields } from 'utilities/adminHelpers';

const CustomerPaginationLimit = 20;

const initialState = {
  hasFavourites: false,
  favouritesOnly: true,
  hasMyCustomers: false,
  myCustomers: true,
  customers: [],
  query: '',
  page: 1,
  hasMore: true,
  loadingList: false,
  loadingMore: false,
  editAll: false,
  changedCustomerIDs: [],
};

const customerAdminStore = create((set, get) => ({
  ...initialState,
  setFilters: (filters) => set({ ...filters }),
  setFavourites: (favouritesOnly) => {
    const { fetchCustomers } = get();
    setLocalStorage({ 
      favouritesOnly,
      myCustomers: false,
    });

    set({ 
      favouritesOnly,
      myCustomers: false,
    });
    fetchCustomers({ page: 1 });
  },
  setMyCustomers: (myCustomers) => {
    const { fetchCustomers } = get();
    setLocalStorage({
      myCustomers,
      favouritesOnly: false,
    });

    set({ 
      myCustomers,
      favouritesOnly: false,
    });
    fetchCustomers({ page: 1 });
  },
  setQuery: (query) => {
    const { hasFavourites, hasMyCustomers, fetchCustomers } = get();
    if (!!query) {
      setLocalStorage({
        ...(!!hasFavourites && { favouritesOnly: false }),
        ...(!!hasMyCustomers && { myCustomers: false }),
      });
    }
    set({
      query,
      ...(!!query && !!hasFavourites && { favouritesOnly: false }),
      ...(!!query && !!hasMyCustomers && { myCustomers: false }),
    });
    fetchCustomers({ page: 1 });
  },
  fetchCustomers: async ({ page }) => {
    const { hasFavourites, favouritesOnly, hasMyCustomers, myCustomers, query } = get();
    set({ loadingList: page == 1, loadingMore: page > 1 });
    if (page == 1) {
      set({ customers: [] });
    }

    const { data: customers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: {
        page,
        ...(!!query && { query }),
        ...(!!hasFavourites && !!favouritesOnly && { favourites_only: true }),
        ...(!!hasMyCustomers && !!myCustomers && { my_customers: true }),
      },
    });

    set((state) => ({
      customers: page == 1 ? customers : [...state.customers, ...customers],
      page: page + 1,
      hasMore: customers.length == CustomerPaginationLimit,
      loadingList: false,
      loadingMore: false,
    }));
  },
  updateCustomer: async ({ customer }) => {
    const { data: updatedCustomer } = await axios({
      method: 'PUT',
      url: apiAdminCustomerPath(customer),
      data: { customer_profile: customer },
      headers: csrfHeaders(),
    });

    set((state) => ({
      customers: state.customers.map((customer) => (customer.id === updatedCustomer.id ? updatedCustomer : customer)),
    }));
    toast.success(`Updated customer '${updatedCustomer.name}'`, { ...defaultToastOptions, autoClose: 5000 });
  },
  setEditAll: () => {
    const { editAll } = get();
    set({ editAll: !editAll });
  },
  setSaveAll: () => {
    const { changedCustomerIDs } = get();
    set((state) => ({
      customers: state.customers.map((customer) =>
        changedCustomerIDs.includes(customer.id) ? { ...customer, triggerSave: true } : customer
      ),
      changedCustomerIDs: [],
    }));
  },
  markCustomerAsChanged: ({ customer, remove = false }) => {
    const { changedCustomerIDs } = get();
    if (remove) {
      return set({ changedCustomerIDs: changedCustomerIDs.filter((ID) => ID !== customer.id) });
    }
    if (changedCustomerIDs.includes(customer.id)) return;

    set({ changedCustomerIDs: [...changedCustomerIDs, customer.id] });
  },
  favouriteCustomer: async ({ customer }) => {
    const { data: updatedCustomer } = await axios({
      method: 'POST',
      url: apiAdminCustomerMarkCustomerAsFavouritePath(customer),
      headers: csrfHeaders(),
    });

    set((state) => ({
      customers: state.customers.map((customer) => (customer.id === updatedCustomer.id ? updatedCustomer : customer)),
    }));
  },
  updateCustomerPermissions: async ({ customer, accessPermissions }) => {
    const sanitizedAccessPermissions = accessPermissions.map(({ id, customer_profile_id, scope, active, _delete }) => ({
      id,
      customer_profile_id,
      scope,
      active,
      ...(!!_delete && { _delete: true }),
    }))
    const { data: updatedCustomer } = await axios({
      method: 'POST',
      url: apiAdminCustomerAccessPermissionsPath(customer),
      headers: csrfHeaders(),
      data: { access_permissions: sanitizedAccessPermissions },
    });

    set((state) => ({
      customers: state.customers.map((customer) => (customer.id === updatedCustomer.id ? updatedCustomer : customer)),
    }));
    toast.success(`Updated access permissions for '${customer.name}'`, { ...defaultToastOptions, autoClose: 5000 });
  },
  updateCustomerDeliveryOverrides: async ({ customer, deliveryOverrides }) => {
    const sanitizedDeliveryOverrides = deliveryOverrides.map(({ id, customer_profile_id, supplier_kind, supplier_profile_id, customer_override, supplier_override, active, _delete }) => ({
      id,
      supplier_kind,
      supplier_profile_id,
      customer_override,
      supplier_override,
      active,
      ...(!!_delete && { _delete: true }),
    }))
    const { data: updatedCustomer } = await axios({
      method: 'POST',
      url: apiAdminCustomerDeliveryOverridesPath(customer),
      headers: csrfHeaders(),
      data: { delivery_overrides: sanitizedDeliveryOverrides },
    });

    set((state) => ({
      customers: state.customers.map((customer) => (customer.id === updatedCustomer.id ? updatedCustomer : customer)),
    }));
    toast.success(`Updated delivery overrides for '${customer.name}'`, { ...defaultToastOptions, autoClose: 5000 });
  },
  updateCustomerUser: async ({ profileable, user }) => {
    try {
      const sanitizedUser = {}
      userUpdateFields.forEach((field) => {
        if (user[field]) {
          sanitizedUser[field] = user[field]  
        }
      });

      if (!Object.keys(sanitizedUser).length) return;

      const { data: updatedCustomer } = await axios({
        method: 'PUT',
        url: apiAdminUserPath(profileable, { profileable_type: 'CustomerProfile'}),
        headers: csrfHeaders(),
        data: { user: sanitizedUser },
      });

      set((state) => ({
        customers: state.customers.map((customer) => (customer.id === updatedCustomer.id ? updatedCustomer : customer)),
      }));
      toast.success(`Updated user settings for '${profileable.name}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
    }
  },
  deprecateCustomerUser: async ({ profileable }) => {
    try {
      const { data: updatedCustomer } = await axios({
        method: 'POST',
        url: apiAdminUserDeprecateUserPath(profileable, { profileable_type: 'CustomerProfile'}),
        headers: csrfHeaders(),
      });
      set((state) => ({
        customers: state.customers.map((customer) => (customer.id === updatedCustomer.id ? updatedCustomer : customer)),
      }));
      toast.warning('User successfully deprecated!', { ...defaultToastOptions, autoClose: 10000 });
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
    }
  },
  createAdminableCustomer: async ({ newCustomer, customerUUID }) => {
    try {
      const { data: createdCustomer } = await axios({
        method: 'POST',
        url: apiAdminCustomerAddAdminableCustomerPath(customerUUID),
        data: { customer: newCustomer },
        headers: csrfHeaders(),
      });

      set((state) => ({
        customers: [createdCustomer, ...state.customers],
      }));
      toast.success(`Create new customer named '${createdCustomer.name}'`, { ...defaultToastOptions, autoClose: 5000 });
    } catch (error) {
      const {
        data: { errors },
      } = error.response;
      errors.map((errorMessage) => toast.error(errorMessage, { ...defaultToastOptions, autoClose: 10000 }));
      throw error;
    }
  },
}));

function setLocalStorage({ ...filter }) {
  const storageKey = 'CustomerFilters';
  const existingFilters = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify({}));
  delete (existingFilters.query); // only required for a while. TO-DO - remove after a few weeks

  const updatedFilter = { ...existingFilters, ...filter };
  localStorage.setItem(storageKey, JSON.stringify(updatedFilter));
}

export default customerAdminStore;
