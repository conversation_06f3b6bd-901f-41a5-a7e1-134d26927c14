import { render } from 'react-dom';
import { Provider } from 'react-redux';

import App from 'components/supplier/closure_dates/App';
import store from 'store/configureClosureStore';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function supplierClosures($el, props) {
  const { isAdmin, ...closureProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin }}>
        <appContext.Provider value={closureProps}>
          <App />
        </appContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
