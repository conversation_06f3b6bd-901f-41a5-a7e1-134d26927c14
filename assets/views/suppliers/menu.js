import { render } from 'react-dom';
import { Provider } from 'react-redux';

import MenuApp from 'components/supplier/menu/MenuApp';
import store from 'store/configureMenuStore';
import { menuContext } from 'contexts/menuContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function supplierMenu($el, props) {
  const { isAdmin, canManageMenu, ...menuProps } = props;
  render(
    <Provider store={store}>
      <adminContext.Provider value={{ isAdmin, canManageMenu }}>
        <menuContext.Provider value={menuProps}>
          <MenuApp />
        </menuContext.Provider>
      </adminContext.Provider>
    </Provider>,
    $el
  );
}
