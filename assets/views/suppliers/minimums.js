import { render } from 'react-dom';

import MininumsApp from 'components/supplier/minimums/MininumsApp';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';

const defaultLeadTimes = () => {
  const leadTimes = [];
  for (let i=6; i<=18; i++) {
    const hour = i < 10 ? `0${i}` : i
    leadTimes.push(`${hour}:00`)
    if (i !==18)
      leadTimes.push(`${hour}:30`)
  };
  return leadTimes;
}

export default function supplierMinimums(el, props) {
  const { isAdmin, ...minimumProps } = props;
  minimumProps.potentialLeadTimes = defaultLeadTimes();
  render(
    <adminContext.Provider value={{ isAdmin }}>
      <appContext.Provider value={minimumProps}>
        <MininumsApp />
      </appContext.Provider>
    </adminContext.Provider>,
    el
  );
}
