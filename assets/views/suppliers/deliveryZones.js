import { render } from 'react-dom';

import DeliveryZoneApp from 'components/supplier/delivery_zones/DeliveryZoneApp';
import appContext from 'contexts/appContext';
import adminContext from 'contexts/adminContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function supplierDeliveryZones(el, props) {
  const { isAdmin, ...deliveryZoneProps } = props;
  render(
    <adminContext.Provider value={{ isAdmin }}>
      <appContext.Provider value={deliveryZoneProps}>
        <DeliveryZoneApp />
      </appContext.Provider>
    </adminContext.Provider>,
    el
  );
}
