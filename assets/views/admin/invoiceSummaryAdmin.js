import { render } from 'react-dom';

import InvoiceSummaryAdminApp from 'components/admin/invoice_summary/InvoiceSummaryAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function invoiceSummaryAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <InvoiceSummaryAdminApp />
    </appContext.Provider>,
    el
  );
}
