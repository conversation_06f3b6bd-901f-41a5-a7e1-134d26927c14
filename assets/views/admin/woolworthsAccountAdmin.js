import { render } from 'react-dom';

import WoolworthsAccountAdminApp from 'components/admin/woolworths/WoolworthsAccountAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';

export default function woolworthsAccountAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <WoolworthsAccountAdminApp />
    </appContext.Provider>,
    el
  );
}
