import { render } from 'react-dom';

import ReminderAdminApp from 'components/admin/reminder/ReminderAdminApp';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';
import 'react-toastify/dist/ReactToastify.css';
import 'react-datepicker/dist/react-datepicker.css';

export default function reminderAdmin(el, props) {
  render(
    <appContext.Provider value={props}>
      <ReminderAdminApp />
    </appContext.Provider>,
    el
  );
}
