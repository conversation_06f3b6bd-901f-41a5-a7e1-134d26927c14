import { useState, useRef, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import AsyncSelect from 'react-select/async';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import reminderAdminStore from 'store/admin/reminderAdminStore';
import axios from 'axios';
import debounce from 'debounce-promise';
import { apiAdminCustomersPath } from 'routes';

import { reminderFrequencies, reminderRecipients } from 'utilities/adminHelpers';

const ReminderForm = ({ reminder, showForm }) => {
  const [localReminder, setLocalReminder] = useState(reminder);
  const { remindable } = localReminder;

  const messageRef = useRef(null);
  useEffect(() => {
    if (messageRef && messageRef.current) {
      messageRef.current.style.height = 'auto';
      messageRef.current.style.height = `${messageRef.current.scrollHeight + 10}px`;
    }
  }, [localReminder.message]);

  const { createReminder, updateReminder } = reminderAdminStore(
    (state) => ({
      createReminder: state.createReminder,
      updateReminder: state.updateReminder,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localReminder.id) {
        await updateReminder({
          reminder: localReminder,
        });
      } else {
        await createReminder({
          reminder: localReminder,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleDateChange = (name, datetime) => {
    const selectedDate = moment(datetime, 'dddd, MMM DD, YYYY')?.format('YYYY/MM/DD');
    setLocalReminder((state) => ({ ...state, [name]: selectedDate }));
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalReminder((state) => ({ ...state, [field]: value }));
  };

  const handleCustomerSelect = (selectedCustomer) => {
    const remindable = {
      name: selectedCustomer.name,
      email: selectedCustomer.email,
    }
    setLocalReminder((state) => ({ ...state, remindable, remindable_type: 'CustomerProfile', remindable_id: selectedCustomer.id }));
  };

  const promiseCustomerOptions = debounce(async (query) => {
    if (!query || query.length < 3) return [];

    const { data: responseCustomers } = await axios({
      method: 'GET',
      url: apiAdminCustomersPath(),
      params: { query },
    });

    return responseCustomers.map((customer) => {
      let label = `${customer.name} [${customer.email}]`;
      return {
        value: customer.id,
        label,
        id: customer.id,
        name: customer.name,
        email: customer.email,
      };
    });
  }, 1000);

  return (
    <>
      <div className="overlay show" onClick={() => showForm(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div>
            <h3>{localReminder.id ? 'Edit' : 'Create'} Reminder</h3>
          </div>

          <div className="mb-2">
            <label>Customer</label>
            <AsyncSelect
              className="form-input"
              cacheOptions
              defaultOptions
              placeholder="Select Customers"
              loadOptions={promiseCustomerOptions}
              onChange={handleCustomerSelect}
              value={{ label: localReminder.remindable_type == 'CustomerProfile' && localReminder.remindable?.name ? `${localReminder.remindable.name} [${localReminder.remindable.email}]` : '' }}
              styles={{
                control: (baseStyles, state) => ({
                  ...baseStyles,
                  zIndex: 5,
                }),
              }}
            />
          </div>

          <div>
            <label>Title</label>
            <input type="text" className="form-input" name="title" value={localReminder.title} onChange={handleChange} />
          </div>
          
          <div>
            <label>Message</label>
            <textarea
              ref={messageRef}
              className="form-input"
              name="message"
              value={localReminder.message}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>Frequency</label>
            <select name='frequency' onChange={handleChange} className='form-input'>
              {Object.keys(reminderFrequencies).map((frequency) => (
                <option key={`reminder-${localReminder.id}-type-${frequency}`} value={frequency} selected={frequency === localReminder.frequency}>
                  {reminderFrequencies[frequency]}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label>Starting From </label>
            <DatePicker
              selected={localReminder.starting_at ? new Date(localReminder.starting_at) : ''}
              onChange={(datetime) => handleDateChange('starting_at', datetime)}
              name="close_from"
              dateFormat="dd-MM-yyyy"
              className="form-input"
              autoComplete="off"
            />
          </div>

          <div>
            <label>Send To</label>
            <select name='recipients' onChange={handleChange} className='form-input'>
              {Object.keys(reminderRecipients).map((recipients) => (
                <option key={`reminder-${localReminder.id}-type-${recipients}`} value={recipients} selected={recipients === localReminder.recipients}>
                  {reminderRecipients[recipients]}
                </option>
              ))}
            </select>
          </div>

          <div className="between-flex mt-1 admin-sidebar-buttons">
            <a className="button tinyx" onClick={handleSave}>{localReminder.id ? 'Update' : 'Create'}</a>
            <a className="button tinyx gray-btn" onClick={() => showForm(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReminderForm;
