import { useRef, useEffect, useContext } from 'react';

// store
import shallow from 'zustand/shallow';
import reminderAdminStore from 'store/admin/reminderAdminStore';
import appContext from 'contexts/appContext';

const ReminderSearch = () => {
  const isMounted = useRef(false);

  const { hasFavourites } = useContext(appContext);

  const { fetchReminders, query, setQuery } = reminderAdminStore(
    (state) => ({
      fetchReminders: state.fetchReminders,
      query: state.query,
      setQuery: state.setQuery,
    }),
    shallow
  );

  useEffect(() => {
    if (query && query.length < 3) return;
    if (isMounted.current) {
      const delayDebounceFn = setTimeout(() => {
        fetchReminders({
          page: 1,
          hasFavourites,
        });
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [query]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by reminder title, message, frequency, remindable name or company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={query}
        onChange={(event) => setQuery(event.target.value)}
      />
    </div>
  );
};

export default ReminderSearch;
