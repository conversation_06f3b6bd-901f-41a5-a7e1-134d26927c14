import { useState } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

// store
import shallow from 'zustand/shallow';
import reminderAdminStore from 'store/admin/reminderAdminStore';

import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import ReminderForm from './ReminderForm';
import ReminderEmails from './ReminderEmails';

const Reminder = ({ reminder, index }) => {
  const circleColor = getCircleIconColor(index);
  const [isEdit, setIsEdit] = useState(false);
  const [viewEmails, setViewEmails] = useState(false);
  const { remindable: customer } = reminder;

  const { removeReminder } = reminderAdminStore(
    (state) => ({
      removeReminder: state.removeReminder,
    }),
    shallow
  );

  return (
    <>
      <div className="list-item">
        <div className="list-flex-1 invoice-header">
          <span className="circle-icon" style={{ background: circleColor }}>
            {customer?.name ? customer.name[0] : '/'}
          </span>
        </div>
        <div className="list-flex-2">
          <span style={{ fontWeight: 'bold' }}>
            {customer?.name ? `${customer.name} [${customer.email}]` : 'Generic'}
          </span>
        </div>
        <div className="list-flex-2">{reminder.formatted_recipients}</div>
        <div className="list-flex-2">{reminder.title}</div>
        <div className="list-flex-2">{reminder.message}</div>
        <div className="list-flex-2 text-center">{reminder.formatted_frequency}</div>
        <div className="list-flex-2 text-center">{reminder.starting_at}</div>
        <div className="list-flex-1 text-center">
          <div className="between-flex" style={{ justifyContent: 'space-evenly' }}>
            <a className="icon-edit mr-1" onClick={() => setIsEdit(!isEdit)} />
            <a className="icon-trash remove" onClick={() => removeReminder({ reminder })} />
          </div>
          <a onClick={() => setViewEmails(!viewEmails)}>View Emails</a>
        </div>
      </div>
      {isEdit && <ReminderForm reminder={reminder} showForm={setIsEdit} />}
      {viewEmails && <ReminderEmails reminder={reminder} setViewEmails={setViewEmails} />}
    </>
  );
};

Reminder.propTypes = {
  reminder: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Reminder;
