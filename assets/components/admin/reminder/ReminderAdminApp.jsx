import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import reminderAdminStore from 'store/admin/reminderAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewReminder from './NewReminder';
import <PERSON>minder from './Reminder';
import ReminderSearch from './ReminderSearch';
import ReminderSkeleton from './ReminderSkeleton';
import NoReminderNotice from './NoReminderNotice';

const ReminderAdminApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { fetchReminders, reminders, loadingList, loadingMore, page, hasMore, query } = reminderAdminStore(
    (state) => ({
      fetchReminders: state.fetchReminders,
      reminders: state.reminders,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchReminders({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <ReminderSearch />
      <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span className="list-flex-2">Reminder For</span>
          <span className="list-flex-2">Recipients</span>
          <span className="list-flex-2">Title</span>
          <span className="list-flex-2">Message</span>
          <span className="list-flex-2 text-center">Frequency</span>
          <span className="list-flex-2 text-center">Starting From</span>
          <span className="list-flex-1" />
        </div>
      </div>
      {!loadingList && !loadingMore && !query && <NewReminder />}
      <NoReminderNotice isLoading={loadingList || loadingMore} reminders={reminders} />
      {reminders.map((reminder, idx) => (
        <Reminder key={`reminder-${reminder.id}`} reminder={reminder} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <ReminderSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default ReminderAdminApp;
