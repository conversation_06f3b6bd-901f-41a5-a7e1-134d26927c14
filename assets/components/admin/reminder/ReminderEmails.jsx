import { useState, useEffect } from 'react'; 
import axios from 'axios';
import { apiAdminReminderPath } from 'routes';

const ReminderEmails = ({ reminder, setViewEmails }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [emails, setEmails] = useState([]);

  useEffect(async () => {
    setIsLoading(true);

    try {
      const { data: fetchedReminder } = await axios({
        method: 'GET',
        url: apiAdminReminderPath(reminder),
      });

      setEmails(fetchedReminder.emails);  
    } catch (err) {
      // do nothing
    }
    setIsLoading(false);
  },[])

  return (
    <>
      <div className="overlay show" onClick={() => setViewEmails(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div>
            <h3>{reminder.title} - Emails</h3>
          </div>
          {isLoading && <p>Fetching Emails....</p>}
          {!isLoading && !!emails.length && (
            <ul className="bullet-list">
              {emails.map((email, idx) => <li>Sent {email.sent_at} </li>)}
            </ul>
          )}
          {!isLoading && !emails.length && <p>No reminder emails sent!</p>}
          <div className="between-flex mt-1 admin-sidebar-buttons">
            <a className="button gray-btn" onClick={() => setViewEmails(false)}>
              Close
            </a>
          </div>
        </div>
      </div>
    </>
  )
}

export default ReminderEmails;