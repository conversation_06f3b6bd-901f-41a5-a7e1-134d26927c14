import { useState } from 'react';

// components
import ReminderForm from './ReminderForm';

const initialReminder = {
  title: '',
  message: '',
  remindable_type: '',
  remindable_id: '',
  remindable: { name: '', email: ''},
  frequency: 'weekly',
  recipients: 'account_manager'
};

const NewReminder = () => {
  const [isNew, setIsNew] = useState(false);

  return (
    <>
      <div className="list-item">
        <a onClick={() => setIsNew(true)}>Add new Reminder</a>
      </div>
      {isNew && <ReminderForm reminder={initialReminder} showForm={setIsNew} />}
    </>
  );
};

export default NewReminder;
