import { useContext, useEffect } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import customerAdminStore from 'store/admin/customerAdminStore';
import appContext from 'contexts/appContext';

// components
import Customer from './Customer';
import CustomerActions from './CustomerActions';
import CustomerSkeleton from './CustomerSkeleton';
import CustomerFilters from './CustomerFilters';
import NewCustomer from './NewCustomer';
import { ToastContainer } from 'react-toastify';

const CustomerAdminApp = () => {
  const { hasFavourites, hasMyCustomers, canManageCustomers, customerName, canAddCustomers } = useContext(appContext);

  const { setFilters, fetchCustomers, customers, loadingList, loadingMore, page, hasMore } = customerAdminStore(
    (state) => ({
      setFilters: state.setFilters,
      fetchCustomers: state.fetchCustomers,
      customers: state.customers,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
    }),
    shallow
  );

  useEffect(() => {
    const existingFilters = JSON.parse(localStorage.getItem('CustomerFilters') || JSON.stringify({}));

    setFilters({
      hasFavourites,
      favouritesOnly: !hasMyCustomers && !!hasFavourites,
      hasMyCustomers,
      myCustomers: !!hasMyCustomers,
      query: customerName || '',
      ...existingFilters,
    });
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchCustomers({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <CustomerActions />
      <div className="sticky-container">
        <CustomerFilters />
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span className="list-flex-2">Name</span>
          <span className="list-flex-2">Company</span>
          <span className="list-flex-3">Contact</span>
          <span className="list-flex-1">{canManageCustomers ? 'Flags' : 'Role'}</span>
          <span className="list-flex-2" />
        </div>
      </div>

      {canAddCustomers && <NewCustomer />}

      {customers.map((customer, idx) => (
        <Customer key={`customer-${customer.id}`} customer={customer} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <CustomerSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default CustomerAdminApp;
