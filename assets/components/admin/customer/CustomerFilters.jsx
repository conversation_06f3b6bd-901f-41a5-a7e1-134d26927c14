import { useContext } from 'react';
import shallow from 'zustand/shallow';

import customerAdminStore from 'store/admin/customerAdminStore';
import appContext from 'contexts/appContext';

const CustomerFilters = () => {
  const { hasFavourites, hasMyCustomers } = useContext(appContext);

  const { favouritesOnly, setFavourites, myCustomers, setMyCustomers } = customerAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
      myCustomers: state.myCustomers,
      setMyCustomers: state.setMyCustomers,
    }),
    shallow
  );

  if (!hasFavourites && !hasMyCustomers) {
    return null;
  }

  return (
    <div className="order-list-options">
      <span style={{ paddingRight: '8px' }}>Filters: </span>
      {!!hasMyCustomers && (
        <label className="drop-text admin-order-option between-flex">
          <input
            type="checkbox"
            name="show-favourites"
            className="checkbox-content"
            checked={myCustomers}
            onChange={(e) => setMyCustomers(e.target.checked)}
          />
          <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
          My Users Only (as Account Manager)
        </label>
      )}
      {!!hasFavourites && (
        <label className="drop-text admin-order-option between-flex">
          <input
            type="checkbox"
            name="show-favourites"
            className="checkbox-content"
            checked={favouritesOnly}
            onChange={(e) => setFavourites(e.target.checked)}
          />
          <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
          Favourite Customers Only
        </label>
      )}
    </div>
  );
};

export default CustomerFilters;
