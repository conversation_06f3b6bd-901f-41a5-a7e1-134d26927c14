import PropTypes from 'prop-types';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const InvoiceCustomer = ({ agedReceivable, index, setActiveSummary }) => {
  const circleColor = getCircleIconColor(index);

  return (
    <div className="list-item" onClick={() => setActiveSummary(agedReceivable)}>
      <div className="list-flex-1 invoice-header">
        <span
          data-tip
          data-for={`invoice-${agedReceivable.id}-orders`}
          className="circle-icon"
          style={{ background: circleColor }}
        >
          {agedReceivable.company_name ? agedReceivable.company_name[0] : '-'}
        </span>
      </div>
      <div className="list-flex-2">
        {agedReceivable.company_name}
        <br />({agedReceivable.customer_name})
        {!!agedReceivable.accounting_software && (
          <>
            <br /><strong>[{agedReceivable.accounting_software.toUpperCase()}]</strong>
          </>
        )}
      </div>
      <div className="list-flex-2 text-center">{agedReceivable.current ? `$${agedReceivable.current}` : '-'}</div>
      <div className="list-flex-2 text-center">{agedReceivable.within_1_month ? `$${agedReceivable.within_1_month}` : '-'}</div>
      <div className="list-flex-2 text-center">{agedReceivable.more_than_1_month ? `$${agedReceivable.more_than_1_month}` : '-'}</div>
      <div className="list-flex-2 text-center">{agedReceivable.more_than_2_months ? `$${agedReceivable.more_than_2_months}` : '-'}</div>
      <div className="list-flex-2 text-center">{agedReceivable.more_than_3_months ? `$${agedReceivable.more_than_3_months}` : '-'}</div>
      <div className="list-flex-2 text-center">{agedReceivable.older ? `$${agedReceivable.older}` : '-'}</div>
      <div className="list-flex-2 text-center">{agedReceivable.total ? `$${agedReceivable.total}` : '-'}</div>
    </div>
  );
};

InvoiceCustomer.propTypes = {
  agedReceivable: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  setActiveSummary: PropTypes.func.isRequired,
};

export default InvoiceCustomer;
