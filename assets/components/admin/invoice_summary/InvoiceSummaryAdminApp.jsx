import { useState, useEffect } from 'react';

// store
import shallow from 'zustand/shallow';
import invoiceSummaryAdminStore from 'store/admin/invoiceSummaryAdminStore';

// components
import SummaryActions from './SummaryActions';
import InvoiceSummaryList from './InvoiceSummaryList';
import NoSummaryNotice from './NoSummaryNotice';
import SummarySkeleton from './SummarySkeleton';

const InvoiceSummaryAdminApp = () => {
  const { setFilters, fetchAgedReceivables, agedReceivables, loadingList } = invoiceSummaryAdminStore(
    (state) => ({
      setFilters: state.setFilters,
      fetchAgedReceivables: state.fetchAgedReceivables,
      agedReceivables: state.agedReceivables,
      loadingList: state.loadingList,
    }),
    shallow
  );

  useEffect(() => {
    const existingFilters = JSON.parse(localStorage.getItem('InvoiceSummaryFilters') || JSON.stringify({}));

    setFilters({
      ...existingFilters,
    });
    fetchAgedReceivables();
  }, []);

  return (
    <>
      <SummaryActions />
      <InvoiceSummaryList agedReceivables={agedReceivables} />
      <NoSummaryNotice isLoading={loadingList} agedReceivables={agedReceivables} />
      <div>{loadingList && <SummarySkeleton />}</div>
    </>
  );
};

export default InvoiceSummaryAdminApp;
