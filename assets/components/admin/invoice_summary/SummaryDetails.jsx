const SummaryDetails = ({ agedReceivable, setActiveSummary }) => {

  return (
    <div className="order-show__details" style={{ marginBottom: 0 }}>
      <div className="order-show-details-section">
        <h6 className="order-show-detail-title order">
          {agedReceivable.customer_name}
          {!!agedReceivable.company_name && (
            <>
              <br />({agedReceivable.company_name})
            </>
          )}
        </h6>
        {!!agedReceivable.email && (
          <p>
            <strong>Email: </strong>
            <a href={`mailto:${agedReceivable.email}`}>{agedReceivable.email}</a>
          </p>
        )}
        {!!agedReceivable.phone && (
          <p>
            <strong>Phone: </strong>
            <a href={`tel:${agedReceivable.phone}`}>{agedReceivable.phone}</a>
          </p>
        )}
      </div>

      {!!agedReceivable.invoices?.length && (
        <div className="order-show-details-section">
          <p>
            <strong>Invoices ({agedReceivable.invoices.length})</strong>
          </p>
          <ul className='m-0'>
            {agedReceivable.invoices.map((invoice) => (
              <li>
                <p>
                  <a href={invoice.show_path} target='blank'>
                    <strong>#{invoice.number}</strong>
                  </a>
                </p>
                <p>
                  <strong>Dates: </strong>
                  <span>{invoice.dates.join(' - ')}</span>
                </p>
                <p>
                  <strong>Due: </strong>
                  <span>{invoice.due_at}</span>
                </p>
                <p>
                  <strong>Total: </strong>
                  <span>{invoice.total}</span>
                </p>
                <hr />
              </li>
            ))}
          </ul>

          <p className="between-flex grey">
            Total Pending
            <span>{agedReceivable.total}</span>
          </p>
        </div>  
      )}

      <div className="order-show-details-section">
        {!!agedReceivable.customer_invoices_path && (
          <a className="button mb-1-2 hollow" style={{ width: '100%' }} href={agedReceivable.customer_invoices_path} target="blank">
            Access Profile
          </a>  
        )}
        <a className="button gray-btn" style={{ width: '100%' }} onClick={() => setActiveSummary(null)}>
          Close
        </a>
      </div>
    </div>
  );
};

export default SummaryDetails;
