import { useState, useEffect } from 'react';

import shallow from 'zustand/shallow';
import invoiceSummaryAdminStore from 'store/admin/invoiceSummaryAdminStore';

// components
import SummaryDetails from './SummaryDetails';
import InvoiceCustomer from './InvoiceCustomer';
import { invoiceSummaryOptions } from 'utilities/adminHelpers';

const InvoiceSummaryList = ({ agedReceivables }) => {
  const { timeFrame } = invoiceSummaryAdminStore(
    (state) => ({
      timeFrame: state.timeFrame,
    }),
    shallow
  );

  const [activeSummary, setActiveSummary] = useState(null);
  const [sortBy, setSortBy] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  
  const handleSortChange = (sortField) => {
    if (sortField === 'customerName') {
      setSortBy(null);
      setSortDirection(null);
    } else if (timeFrame && sortField !== 'total' && sortField != timeFrame) {
      // cannot sort by filtered out time frame
      return;
    } else if (sortBy === sortField) {
      const newDirection = ['DESC', 'ASC'].filter((option) => option !== sortDirection)[0];
      setSortDirection(newDirection);
    } else {
      setSortBy(sortField);
      setSortDirection('DESC');
    }
  }

  useEffect(() => {
    if (sortBy && sortBy !== 'total' && timeFrame !== sortBy) {
      setSortBy(null);
      setSortDirection(null);  
    }
  }, [timeFrame])

  const sortedAgedReceivables = [...agedReceivables].sort((a, b) => {    
    if (sortDirection === 'DESC') {
      return Number(b[sortBy]) - Number(a[sortBy])
    } else if (sortDirection === 'ASC') {
      return Number(a[sortBy]) - Number(b[sortBy])
    } else {
      return 0;
    }
  });

  return (
    <>
      {!!activeSummary && (
        <>
          <div className="overlay show" onClick={() => setActiveSummary(null)} />
          <div className={`sidebar-overlay ${activeSummary ? 'open' : 'closed'}`}>
            <div className="admin-order-slider">
              <SummaryDetails agedReceivable={activeSummary} setActiveSummary={setActiveSummary} />
            </div>
          </div>
        </>
      )}
      <div className="sticky-container">
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span
            className="list-flex-2"
            onClick={() => handleSortChange('customerName')}
          >
            Customer
          </span>
          {Object.keys(invoiceSummaryOptions).map((option) => (
            <span
              className='list-flex-2 text-center'
              onClick={() => handleSortChange(option)}
            >
              {invoiceSummaryOptions[option]}
              {sortBy === option && <span> ({sortDirection})</span>}
            </span>  
          ))}
          <span 
            className='list-flex-2 text-center'
            onClick={() => handleSortChange('total')}
          >
            Total
            {sortBy === 'total' && <span> ({sortDirection})</span>}
          </span>
        </div>
      </div>
      {sortedAgedReceivables.map((agedReceivable, idx) => (
        <InvoiceCustomer
          key={`aged-receivable-${agedReceivable.id}`}
          agedReceivable={agedReceivable}
          index={idx}
          setActiveSummary={setActiveSummary}
        />
      ))}
    </>
  )
};

export default InvoiceSummaryList;