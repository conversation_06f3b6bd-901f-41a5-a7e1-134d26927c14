import { useRef, useEffect, useState } from 'react';

// store
import shallow from 'zustand/shallow';
import invoiceSummaryAdminStore from 'store/admin/invoiceSummaryAdminStore';

import { apiAdminAgedReceivablesPath } from 'routes';
import { invoiceSummaryOptions } from 'utilities/adminHelpers';

const SummaryActions = () => {
  const isMounted = useRef(false);
  const { query, setQuery, timeFrame, setTimeFrame } = invoiceSummaryAdminStore(
    (state) => ({
      query: state.query,
      setQuery: state.setQuery,
      timeFrame: state.timeFrame,
      setTimeFrame: state.setTimeFrame,
    }),
    shallow
  );

  const [localQuery, setLocalQuery] = useState(query);

  useEffect(() => {
    if (localQuery && localQuery.length < 3) return;
    if (isMounted.current && query !== localQuery) {
      const delayDebounceFn = setTimeout(() => {
        setQuery(localQuery);
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [localQuery]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by number, order id, customer name or company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={localQuery}
        onChange={(event) => setLocalQuery(event.target.value)}
      />

      <select className="form-input" value={timeFrame} onChange={(event) => setTimeFrame(event.target.value)} style={{ maxWidth: '150px' }}>
        <option value={''}>All</option>
        {Object.keys(invoiceSummaryOptions).map((option) => <option key={`notification-severity-${option}`} value={option}>{invoiceSummaryOptions[option]}</option>)}
      </select>

      <a className="button small" href={apiAdminAgedReceivablesPath({ format: 'csv' })}>
        Download Summary
      </a>
    </div>
  );
};

export default SummaryActions;
