import { useState } from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';
import AccountForm from './AccountForm';

import shallow from 'zustand/shallow';
import woolworthsAccountAdminStore from 'store/admin/woolworthsAccountAdminStore';

const Account = ({ account, index }) => {
  const circleColor = getCircleIconColor(index);
  const [isEdit, setIsEdit] = useState(false);
  const { order } = account;

  const { updateAccount } = woolworthsAccountAdminStore(
    (state) => ({
      updateAccount: state.updateAccount,
    }),
    shallow
  );

  const handleActiveChange = async (e) => {
    try {
      await updateAccount({
        account: {
          ...account,
          active: e.target.checked,
        },
      });
    } catch (error) {
      // do nothing
    }
  }

  const handleDetach = async () => {
    try {
      await updateAccount({
        account,
        detachFromOrderID: account.order.id,
      });
    } catch (error) {
      // do nothing
    }
  }

  return (
    <>
      <div className="list-item">
        <div className="list-flex-2 invoice-header">
          <span className="circle-icon" style={{ background: circleColor }}>
            {account.short_name}
          </span>
          <strong>{account.email}</strong>
        </div>
        <div className='list-flex-1 text-center'>
          <label>
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="active"
                checked={account.active}
                onChange={handleActiveChange}
                id={`account-${account.id}-active`}
              />
              <span className="section-toggle__switch" />
            </div>
          </label>
        </div>

        <div className="list-flex-2">
          {!!order?.id && (
            <>
              <strong>Customer:</strong> {order.customer_name}{!!order.company_name ? ` (${order.company_name})` : ''}<br />
              <strong>Order #{order.id}</strong><br />        
              <strong>Status:</strong> {order.status}<br />
              <strong>Created at:</strong> {order.created_at}<br />
              <strong>Delivery at:</strong> {order.delivery_at}<br />
              <strong>Delivery:</strong> {order.delivery_address}<br />
              <strong>Order lines:</strong> {order.order_lines_count}<br />
              <a className='button alert-btn tiny' onClick={handleDetach}>Detach</a>
            </>
          )}
          {!order?.id && 'FREE'}
        </div>
        <div className="list-flex-2 text-center">
          <a className="icon-edit" onClick={() => setIsEdit(!isEdit)} />
        </div>
      </div>
      {isEdit && <AccountForm account={account} showForm={setIsEdit} />}
    </>
  );
};

Account.propTypes = {
  account: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Account;
