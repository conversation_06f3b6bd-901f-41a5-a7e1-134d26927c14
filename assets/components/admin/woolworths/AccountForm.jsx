import { useState } from 'react';
import moment from 'moment';

// store
import shallow from 'zustand/shallow';
import woolworthsAccountAdminStore from 'store/admin/woolworthsAccountAdminStore';

import { statesOptions, contryCodeOptions } from 'utilities/adminHelpers';

const AccountForm = ({ account, showForm }) => {
  const [localAccount, setLocalAccount] = useState(account);

  const { createAccount, updateAccount } = woolworthsAccountAdminStore(
    (state) => ({
      createAccount: state.createAccount,
      updateAccount: state.updateAccount,
    }),
    shallow
  );

  const handleSave = async () => {
    try {
      if (localAccount.id) {
        await updateAccount({
          account: localAccount,
        });
      } else {
        await createAccount({
          account: localAccount,
        });
      }
      showForm(false);
    } catch (error) {
      // do nothing
    }
  };

  const handleChange = (event) => {
    const field = event.target.name;
    const value = event.target.type == 'checkbox' ? event.target.checked : event.target.value;
    setLocalAccount((state) => ({ ...state, [field]: value }));
  };

  return (
    <>
      <div className="overlay show" onClick={() => setIsEdit(false)} />
      <div className="sidebar-overlay open">
        <div style={{ width: '400px', padding: '16px' }}>
          <div>
            <h3>Woolworths Account</h3>
          </div>

          <div>
            <label>Email</label>
            <input type="email" name="email" className="form-input" value={localAccount.email || ''} onChange={handleChange} autoComplete='off' />
          </div>
          <div>
            <label>Password</label>
            <input type="password" name="password" className="form-input" value={localAccount.password || ''} onChange={handleChange} autoComplete='off' />
          </div>
          <div>
            <label>
              <input type="checkbox" name="active" checked={localAccount.active} onChange={handleChange} />
              Is Active
            </label>
          </div>
          <div>
            <a className="button tiny mr-1-2" onClick={handleSave}>
              Save
            </a>
            <a className="button tiny gray-btn" onClick={() => showForm(false)}>
              Cancel
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default AccountForm;
