import { useState } from 'react';

// components
import AccountForm from './AccountForm';

const initialAccount = {
  email: 'woolworth<NN>@yordar.com.au',
  password: '',
  active: true,
};

const NewAccount = () => {
  const [isNew, setIsNew] = useState(false);

  return (
    <>
      <div className="list-item">
        <a onClick={() => setIsNew(true)}>Add new Account</a>
      </div>
      {isNew && <AccountForm account={initialAccount} showForm={setIsNew} />}
    </>
  );
};

export default NewAccount;
