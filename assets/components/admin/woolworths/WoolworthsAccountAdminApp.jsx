import { useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import woolworthsAccountAdminStore from 'store/admin/woolworthsAccountAdminStore';
import appContext from 'contexts/appContext';

// components
import { ToastContainer } from 'react-toastify';
import NewAccount from './NewAccount';
import Account from './Account';
import AccountSkeleton from './AccountSkeleton';
import NoAccountNotice from './NoAccountNotice';

const WoolworthsAccountAdminApp = () => {
  const { hasFavourites } = useContext(appContext);

  const { fetchAccounts, accounts, loadingList, loadingMore, page, hasMore, query } = woolworthsAccountAdminStore(
    (state) => ({
      fetchAccounts: state.fetchAccounts,
      accounts: state.accounts,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
      query: state.query,
    }),
    shallow
  );

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchAccounts({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      <div style={{ position: 'sticky', top: 0, zIndex: 999 }}>
        <div className="item-list__headings sticky">
          <span className="list-flex-2">Email</span>
          <span className="list-flex-1 text-center">Active</span>
          <span className="list-flex-2">Usage</span>
          <span className="list-flex-2 text-center">Actions</span>
        </div>
      </div>
      {!loadingList && !loadingMore && !query && <NewAccount />}
      <NoAccountNotice isLoading={loadingList || loadingMore} accounts={accounts} />
      {accounts.map((account, idx) => (
        <Account key={`account-${account.id}`} account={account} index={idx} />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <AccountSkeleton />}</div>
      <ToastContainer />
    </>
  );
};

export default WoolworthsAccountAdminApp;
