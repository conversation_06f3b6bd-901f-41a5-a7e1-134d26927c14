import { useRef, useEffect, useState } from 'react';

// store
import shallow from 'zustand/shallow';
import invoiceAdminStore from 'store/admin/invoiceAdminStore';

import { invoiceStatusOptions } from 'utilities/adminHelpers';

const InvoiceActions = () => {
  const isMounted = useRef(false);
  const { query, setQuery, paymentStatus, setPaymentStatus } = invoiceAdminStore(
    (state) => ({
      query: state.query,
      setQuery: state.setQuery,
      paymentStatus: state.paymentStatus,
      setPaymentStatus: state.setPaymentStatus,
    }),
    shallow
  );

  const [localQuery, setLocalQuery] = useState(query);

  useEffect(() => {
    if (localQuery && localQuery.length < 3) return;
    if (isMounted.current && query !== localQuery) {
      const delayDebounceFn = setTimeout(() => {
        setQuery(localQuery);
      }, 1000);
      return () => clearTimeout(delayDebounceFn);
    }
    isMounted.current = true;
  }, [localQuery]);

  return (
    <div className="between-flex">
      <input
        className="search-input form-input"
        placeholder="Search by number, order id, customer name or company name"
        style={{ maxWidth: '500px' }}
        type="search"
        value={localQuery}
        onChange={(event) => setLocalQuery(event.target.value)}
      />

      <select className="form-input" value={paymentStatus} onChange={(event) => setPaymentStatus(event.target.value)} style={{ maxWidth: '150px' }}>
        <option value={''}>All Invoices</option>
        {Object.keys(invoiceStatusOptions).map((option) => <option key={`notification-severity-${option}`} value={option}>{invoiceStatusOptions[option]}</option>)}
      </select>
    </div>
  );
};

export default InvoiceActions;
