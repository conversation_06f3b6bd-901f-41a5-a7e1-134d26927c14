import { useState, useEffect, useContext } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';

// store
import shallow from 'zustand/shallow';
import invoiceAdminStore from 'store/admin/invoiceAdminStore';
import appContext from 'contexts/appContext';

// components
import Invoice from './Invoice';
import InvoiceActions from './InvoiceActions';
import InvoiceSkeleton from './InvoiceSkeleton';
import InvoiceFilters from './InvoiceFilters';
import NoInvoiceNotice from './NoInvoiceNotice';
import InvoiceDetails from './InvoiceDetails';

const InvoiceAdminApp = () => {
  const { hasFavourites, canManageInvoices } = useContext(appContext);
  const [activeInvoice, setActiveInvoice] = useState(null);

  const { setFilters, fetchInvoices, invoices, loadingList, loadingMore, page, hasMore } = invoiceAdminStore(
    (state) => ({
      setFilters: state.setFilters,
      fetchInvoices: state.fetchInvoices,
      invoices: state.invoices,
      loadingList: state.loadingList,
      loadingMore: state.loadingMore,
      page: state.page,
      hasMore: state.hasMore,
    }),
    shallow
  );

  useEffect(() => {
    const existingFilters = JSON.parse(localStorage.getItem('InvoiceFilters') || JSON.stringify({}));

    setFilters({
      hasFavourites,
      favouritesOnly: !!hasFavourites,
      ...existingFilters,
    });
  }, []);

  const [sentryRef] = useInfiniteScroll({
    loading: loadingMore,
    hasNextPage: hasMore,
    onLoadMore: async () => {
      if (!loadingList && !loadingMore) {
        await fetchInvoices({
          page,
          hasFavourites,
        });
      }
    },
  });

  return (
    <>
      {!!activeInvoice && (
        <>
          <div className="overlay show" onClick={() => setActiveInvoice(null)} />
          <div className={`sidebar-overlay ${activeInvoice ? 'open' : 'closed'}`}>
            <div className="admin-order-slider">
              <InvoiceDetails invoice={activeInvoice} setActiveInvoice={setActiveInvoice} />
            </div>
          </div>
        </>
      )}
      <InvoiceActions />
      <div className="sticky-container">
        <InvoiceFilters />
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span className="list-flex-2">Customer</span>
          <span className="list-flex-1">Number</span>
          <span className="list-flex-3 text-center">Dates</span>
          <span className="list-flex-2 text-center">Status</span>
          {canManageInvoices && <span className="list-flex-1 text-center">Do Not Notify</span>}
          <span className="list-flex-2 text-center">Documents</span>
        </div>
      </div>
      <NoInvoiceNotice isLoading={loadingList || loadingMore} invoices={invoices} />
      {invoices.map((invoice, idx) => (
        <Invoice
          key={`invoice-${invoice.id}`}
          invoice={invoice}
          index={idx}
          setActiveInvoice={setActiveInvoice}
        />
      ))}
      <div ref={sentryRef}>{(loadingList || loadingMore) && <InvoiceSkeleton />}</div>
    </>
  );
};

export default InvoiceAdminApp;
