import { useState, useContext, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

// store
import shallow from 'zustand/shallow';
import invoiceAdminStore from 'store/admin/invoiceAdminStore';
import appContext from 'contexts/appContext';

// components
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const Invoice = ({ invoice, index, setActiveInvoice }) => {
  const isMounted = useRef(false);
  const circleColor = getCircleIconColor(index);
  const [localInvoice, setLocalInvoice] = useState(invoice);

  const { canManageInvoices } = useContext(appContext);

  const { updateInvoice } = invoiceAdminStore(
    (state) => ({
      updateInvoice: state.updateInvoice,
    }),
    shallow
  );

  useEffect(() => {
    if (isMounted.current) {
      updateInvoice({
        invoice: localInvoice,
      });
    }
    isMounted.current = true;
  }, [localInvoice.do_not_notify]);

  const handleChange = (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setLocalInvoice((state) => ({ ...state, [event.target.name]: value }));
  };

  return (
    <div className="list-item">
      <div className="list-flex-1 invoice-header">
        <span
          data-tip
          data-for={`invoice-${invoice.id}-orders`}
          className="circle-icon"
          style={{ background: circleColor }}
        >
          {invoice.customer?.name ? invoice.customer?.name[0] : '-'}
        </span>
      </div>
      <div className="list-flex-2">
        <a onClick={() => setActiveInvoice(invoice)}>
          {invoice.customer?.name}
          {!!invoice.customer?.company_name && (
            <>
              <br />({invoice.customer.company_name})
            </>
          )}
        </a>
      </div>
      <div className="list-flex-1">
        <a onClick={() => setActiveInvoice(invoice)}>
          #{invoice.number}
        </a>
      </div>
      <div className="list-flex-3 text-center">
        {invoice.dates.join(' - ')}
        {!!invoice.due_date && (
          <>
            <br />
            <span className="text-center">Due: {invoice.due_date}</span>
          </>
        )}
      </div>
      <div className="list-flex-2 text-center">
        {invoice.total} - {invoice.payment_status}
        {invoice.is_overdue && (
          <>
            <br />
            <span className="invoice-overdue">Overdue by: {invoice.due_distance}</span>
          </>
        )}
      </div>
      {canManageInvoices && (
        <div className="list-flex-1 text-center">
          <div className="section-toggle">
            <input
              id={`invoice-${invoice.id}-notify`}
              type="checkbox"
              name="do_not_notify"
              checked={localInvoice.do_not_notify}
              onChange={handleChange}
            />
            <label htmlFor={`invoice-${invoice.id}-notify`} className="section-toggle__switch" />
          </div>
        </div>
      )}
      <div className="list-flex-2 text-center">
        {invoice.document && <a href={invoice.document.url}>{invoice.document.name}</a>}
        {invoice.failed_payment && (
          <>
            <br />
            <span className="invoice-overdue">Failed payment</span>
          </>
        )}
      </div>
    </div>
  );
};

Invoice.propTypes = {
  invoice: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};

export default Invoice;
