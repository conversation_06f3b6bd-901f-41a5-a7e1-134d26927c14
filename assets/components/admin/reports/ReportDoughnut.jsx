import { useState, useEffect } from 'react';
import { Doughnut } from 'react-chartjs-2';
import {
  doughnutPlugins,
  doughnutOptions,
  setDoughnutCategoryData,
  setDoughnutSupplierData,
} from 'utilities/graph-options';

const ReportDoughnut = ({ report, title, fields }) => {
  const [activeField, setActiveField] = useState(fields[0]);
  const categoryMap = { category: 'snacks', supplier: 'suppliers' };

  useEffect(() => {
    if (!activeField) return;
    // Switch active doughnut to the other one on load if it has no data and the other does
    const targetSpend =
      title === 'category' ? report?.category_spend?.[activeField] : report?.ethical_spend;
    const defaultSpend = title === 'category' ? report?.category_spend?.[categoryMap[title]] : report?.supplier_spend;

    if (!targetSpend?.length && defaultSpend?.length) {
      setActiveField(categoryMap[title]);
    }
  }, []);

  const setDoughnutData = () => {
    if (title === 'category') return setDoughnutCategoryData(report?.category_spend, activeField);
    const supplierSpend = activeField === 'ethical' ? report?.ethical_spend : report?.supplier_spend
    return setDoughnutSupplierData(supplierSpend, activeField === 'ethical');
  };

  const doughnutData = setDoughnutData();

  return (
    <div className="reporting-doughnut">
      <h3 data-for={`title-${title}`}>{title} Spend</h3>
      <div style={{ marginBottom: '12px' }}>
        {fields.map((field) => (
          <a
            key={field}
            className={`category-spend ${activeField === field ? 'active' : ''}`}
            onClick={() => setActiveField(field)}
          >
            {field.replace(/^\w/, (c) => c.toUpperCase())}
          </a>
        ))}
      </div>
      <div className="doughnut-container">
        <Doughnut data={doughnutData} options={doughnutOptions} plugins={doughnutPlugins} />
      </div>
    </div>
  );
};

export default ReportDoughnut;
