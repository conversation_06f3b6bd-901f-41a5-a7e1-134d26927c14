import { useEffect } from 'react';
import { Doughnut } from 'react-chartjs-2';
import {
  setDoughnutCategoryData,
  doughnutPlugins,
  doughnutOptions,
  setDoughnutSupplierData,
} from 'utilities/graph-options';
import useReportsStore from 'store/configureReportsStore';

const ReportDoughnut = ({ title, fields }) => {
  const { report, activeDoughnut, setActiveDoughnut } = useReportsStore((state) => state);
  const categoryMap = { category: 'snacks', supplier: 'suppliers' };

  useEffect(() => {
    // Switch active doughnut to the other one on load if it has no data and the other does
    const targetSpend =
      title === 'category' ? report?.category_spend?.[activeDoughnut?.category] : report?.ethical_spend;
    const defaultSpend = title === 'category' ? report?.category_spend?.[categoryMap[title]] : report?.supplier_spend;

    if (!targetSpend?.length && defaultSpend?.length) {
      setActiveDoughnut({ [title]: categoryMap[title] });
    }
  }, []);

  const setDoughnutData = () => {
    if (title === 'category') return setDoughnutCategoryData(report?.category_spend, activeDoughnut?.category);
    const supplierSpend = activeDoughnut.supplier === 'ethical' ? report?.ethical_spend : report?.supplier_spend
    return setDoughnutSupplierData(supplierSpend, activeDoughnut.supplier === 'ethical');
  };

  const doughnutData = setDoughnutData();

  return (
    <div className="reporting-doughnut">
      <h3 data-for={`title-${title}`}>{title} Spend</h3>
      <div style={{ marginBottom: '12px' }}>
        {fields.map((field) => (
          <a
            key={field}
            className={`category-spend ${activeDoughnut[title] === field ? 'active' : ''}`}
            onClick={() => setActiveDoughnut({ [title]: field })}
          >
            {field.replace(/^\w/, (c) => c.toUpperCase())}
          </a>
        ))}
      </div>
      <div className="doughnut-container">
        <Doughnut data={doughnutData} options={doughnutOptions} plugins={doughnutPlugins} />
      </div>
    </div>
  );
};

export default ReportDoughnut;
