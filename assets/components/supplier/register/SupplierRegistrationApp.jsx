import { useState, useRef } from 'react';
import axios from 'axios';
import { csrfHeaders } from 'utilities/csrfHeaders';
import { supplierRegistrationsPath } from 'routes';

// errors
import { ToastContainer, toast } from 'react-toastify';
import { defaultToastOptions } from 'utilities/toastHelpers';

import Banner from './Banner';
import BasicInfo from './steps/BasicInfo';
import CompanyDetails from './steps/CompanyDetails';
import BusinessCertifications from './steps/BusinessCertifications';
import FinalDetails from './steps/FinalDetails';
import SuccessPanel from './steps/SuccessPanel';

import { validateStep as validateStepUtil } from './utils/validation';

// Initial form data
const initialFormData = {
  // Basic Info
  firstname: '',
  lastname: '',
  email: '',

  // Company Details
  company_name: '',
  abn_acn: '',
  company_address: '',
  suburb: '',
  suburb_id: '',
  bsb_number: '',
  bank_account_number: '',
  phone: '',
  mobile: '',

  // Final Details
  category_group: '',
  is_team_supplier: false,
  password: '',

  // Certifications
  is_environmentally_accredited: false,
  is_indigenous_owned: false,
  is_registered_charity: false,
  is_socially_responsible: false,
  is_female_owned: false,
  is_lgbtqi_owned: false,
  is_rainforest_alliance_certified: false,
};

// Step configuration
const stepConfig = [
  {
    component: BasicInfo,
    title: "Let's start with the basics",
    text: 'Expand your customer base. Deliver to some of the top companies in Australia. Simplify and streamline your billing. The Yordar platform provides it all.',
    buttonText: 'Add Company Details',
  },
  {
    component: CompanyDetails,
    title: "How's business",
    text: 'Expand your customer base. Deliver to some of the top companies in Australia. Simplify and streamline your billing. The Yordar platform provides it all.',
    buttonText: 'Business Certifications',
  },
  {
    component: BusinessCertifications,
    title: 'Business certifications',
    text: 'Tell us about your business certifications and characteristics. This helps us match you with customers who value sustainability and social responsibility.',
    buttonText: 'Final Details',
  },
  {
    component: FinalDetails,
    title: 'The finish line',
    text: 'Expand your customer base. Deliver to some of the top companies in Australia. Simplify and streamline your billing. The Yordar platform provides it all.',
    buttonText: 'Send Request',
  },
  {
    component: SuccessPanel,
    text: 'Your registration is complete! Our team will review your application and be in touch soon. Thank you for choosing to partner with us.',
    title: 'Registration complete',
  },
];

const SupplierRegistrationApp = () => {
  const [step, setStep] = useState(0);
  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaValid, setCaptchaValid] = useState(false);
  const recaptchaRef = useRef(null);

  const updateFormData = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: null,
      }));
    }
  };

  const validateStep = (stepNumber) => {
    const { isValid, errors: validationErrors } = validateStepUtil(stepNumber, formData);
    setErrors(validationErrors);
    return isValid;
  };

  const prevStep = () => {
    setStep((prev) => Math.max(prev - 1, 0));
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    // Handle step navigation or final submission
    if (step < 3) {
      // Navigate to next step if current step is valid
      if (validateStep(step)) {
        setStep((prev) => prev + 1);
      }
    } else if (step === 3) {
      // Final step - submit the form
      if (!validateStep(step) || !captchaValid) return;

      setIsSubmitting(true);

      try {
        const recaptchaResponse = recaptchaRef.current ? recaptchaRef.current.getValue() : '';
        const data = await axios({
          method: 'POST',
          url: supplierRegistrationsPath({ format: 'json' }),
          data: {
            supplier_registration: formData,
            'g-recaptcha-response': recaptchaResponse,
          },
          headers: csrfHeaders(),
        });

        // Handle success - navigate to success step
        setStep(4);
      } catch (error) {
        const errorMessages = error.response?.data?.errors || ['Registration failed. Please try again.'];
        toast.error(errorMessages.join('.'), { ...defaultToastOptions, autoClose: 10000 });

        // Reset reCAPTCHA on error
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
          setCaptchaValid(false);
        }
        setIsSubmitting(false);
      } finally {
        setIsSubmitting(false);
      }
    }
    // Success step (step 4) - no action needed, just display
  };

  const getStepProps = (stepIndex) => {
    const baseProps = { formData, errors, updateFormData };

    const stepSpecificProps = {
      0: {},
      1: {},
      2: {},
      3: { setCaptchaValid, recaptchaRef },
      4: {}, // Success step doesn't need any special props
    };

    return { ...baseProps, ...stepSpecificProps[stepIndex] };
  };

  const renderStep = () => {
    const currentStepConfig = stepConfig[step];
    if (!currentStepConfig) return null;

    const { component: StepComponent } = currentStepConfig;
    const stepProps = getStepProps(step);

    return <StepComponent {...stepProps} />;
  };

  const getSubmitButtonText = () => {
    if (isSubmitting) return 'Sending Request...';

    const currentStepConfig = stepConfig[step];
    return currentStepConfig.buttonText;
  };

  const isFormValid = () => {
    // Success step is always valid
    if (step === 4) return true;

    if (step === 3) {
      return captchaValid && !Object.values(errors).some((error) => error);
    }

    return !Object.values(errors).some((error) => error);
  };

  return (
    <div className="customer-area has-large-gutter">
      <div className="auth-container">
        <div className="auth-card">
          <div className="auth-card__illustration">
            <Banner step={step} stepConfig={stepConfig[step]} />
          </div>
          <div className="authorization-module">
            <form onSubmit={handleFormSubmit} className="authorization-form" noValidate>
              {renderStep()}

              <div className="row">
                {step > 0 && step < 4 && (
                  <div className="small-3 columns">
                    <button
                      type="button"
                      onClick={prevStep}
                      className="button no-min-width hollow"
                      disabled={isSubmitting}
                    >
                      Back
                    </button>
                  </div>
                )}
                {step < 4 && (
                  <div className={step > 0 ? 'small-9 columns' : 'small-12 columns'} style={{ textAlign: 'center' }}>
                    <button
                      type="submit"
                      className={`button no-min-width ${step === 0 ? 'supplier-register-btn' : ''}`}
                      disabled={!isFormValid() || isSubmitting}
                      id={step === 2 ? 'sign-up-button' : undefined}
                      style={{ minWidth: '100%' }}
                    >
                      {getSubmitButtonText()}
                    </button>
                  </div>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
};

export default SupplierRegistrationApp;
