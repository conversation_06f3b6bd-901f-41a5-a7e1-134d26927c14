import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

import { supplierCertifications } from 'utilities/supplierHelpers';

const BusinessCertifications = ({ formData, errors, updateFormData }) => {

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    updateFormData(name, type === 'checkbox' ? checked : value);
  };

  return (
    <div>
      <h3 className="login-heading">Business Certifications (Optional)</h3>

      <div className="row">
        <div className="small-12 columns">
          <p className="help-text">Select any certifications or characteristics that apply to your business:</p>
        </div>
      </div>

      {supplierCertifications.map((certification) => (
        <div className="row">
          <div className="small-12 columns mb-1 no-gutter">
            <label className="mt-1-2">
              <div className="section-toggle gutter">
                <input
                  type="checkbox"
                  name={certification.field}
                  checked={formData[certification.field]}
                  onChange={handleChange}
                  id={certification.field}
                />
                <span className="section-toggle__switch" />
              </div>
              {certification.label}
              <span
                className="whats-this ml-1 bottom tooltip-dash"
                data-tip={certification.tooltip}
                data-for="sustainable-tooltip"
              >
                What's This?
              </span>
            </label>
          </div>
        </div>
      ))}

      {errors.general && (
        <div className="row">
          <div className="small-12 columns">
            <label className="form-error is-visible">{errors.general}</label>
          </div>
        </div>
      )}

      {/* React Tooltips */}
      <ReactTooltip id="sustainable-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
    </div>
  );
};

BusinessCertifications.propTypes = {
  formData: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
  updateFormData: PropTypes.func.isRequired,
};

export default BusinessCertifications;
