import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import ReCAP<PERSON><PERSON> from 'react-google-recaptcha';

const RECAPTCHA_SITE_KEY = '6LclBLgqAAAAAD5e8Lteldrdtlj46c95qp0mssUn';

const FinalDetails = ({ formData, errors, updateFormData, setCaptchaValid, recaptchaRef }) => {
  const teamOrderTooltip =
    'Our Individual Ordering service is designed to streamline meal delivery by requiring our food vendors to provide meals that are individually packed and labeled, often with specific employee names. This service requires low minimum order quantities per item. If you have individual packaged meals on your menu and would like to opt in to servicing our clients in this capacity please select yes.';

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    updateFormData(name, type === 'checkbox' ? checked : value);
  };

  const handleCategoryGroupChange = (categoryGroup) => {
    updateFormData('category_group', categoryGroup);
    if (categoryGroup === 'kitchen-supplies') {
      updateFormData('is_team_supplier', false);
    }
  };

  const handleCaptchaChange = (value) => {
    setCaptchaValid(!!value);
  };

  return (
    <div>
      <h3 className="login-heading">Final Details</h3>

      <div className="row">
        <div className="small-12 columns">
          <label>What services are you providing?*</label>
          <div className="supplier-category-groups">
            <span
              className={`button small mr-1 ${formData.category_group === 'catering-services' ? '' : 'hollow'}`}
              onClick={() => handleCategoryGroupChange('catering-services')}
              style={{ cursor: 'pointer' }}
            >
              Catering Services
            </span>
            <span
              className={`button small ${formData.category_group === 'kitchen-supplies' ? '' : 'hollow'}`}
              onClick={() => handleCategoryGroupChange('kitchen-supplies')}
              style={{ cursor: 'pointer' }}
            >
              Pantry Supplies
            </span>
          </div>
          {errors.category_group && <label className="form-error is-visible">{errors.category_group}</label>}
        </div>
      </div>

      {formData.category_group === 'catering-services' && (
        <div className="row team-supplier-container">
          <div className="small-12 columns mb-1">
            <label>
              Would you like to be a part of Individual Ordering?
              <span
                className="whats-this ml-1 bottom tooltip-dash"
                style={{ float: 'none' }}
                data-tip={teamOrderTooltip}
                data-for="team-order-tooltip"
              >
                What's This?
              </span>
            </label>
            <label className="mt-1-2">
              NO
              <div className="section-toggle gutter">
                <input
                  type="checkbox"
                  name="is_team_supplier"
                  checked={formData.is_team_supplier}
                  onChange={handleChange}
                  id="is-team-supplier"
                />
                <span className="section-toggle__switch" />
              </div>
              YES
            </label>
          </div>
          <ReactTooltip id="team-order-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
        </div>
      )}

      <div className="row">
        <div className="small-12 columns">
          <label>Password*</label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            className={`validate form-input required ${errors.password ? 'is-invalid-input' : ''}`}
            required
          />
          {errors.password && <label className="form-error is-visible">{errors.password}</label>}
        </div>
      </div>

      <div className="row">
        <div className="large-12 columns">
          <ReCAPTCHA ref={recaptchaRef} sitekey={RECAPTCHA_SITE_KEY} onChange={handleCaptchaChange} />
        </div>
      </div>

      {errors.general && (
        <div className="row">
          <div className="small-12 columns">
            <label className="form-error is-visible">{errors.general}</label>
          </div>
        </div>
      )}
    </div>
  );
};

FinalDetails.propTypes = {
  formData: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
  updateFormData: PropTypes.func.isRequired,
  setCaptchaValid: PropTypes.func.isRequired,
  // recaptchaRef: PropTypes.shape({ current: PropTypes.instanceOf(Element) }).isRequired,
};

export default FinalDetails;
