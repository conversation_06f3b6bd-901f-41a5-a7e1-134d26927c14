const SuccessPanel = () => (
  <div>
    <div className="row">
      <div className="small-12 columns text-center">
        <div style={{ marginBottom: '1.5rem' }}>
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" fill="#10B981" />
            <path d="m9 12 2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </div>

        <h4 style={{ marginBottom: '1rem', fontWeight: 'bold' }}>Thank you for registering with Yorda<PERSON>!</h4>

        <p style={{ marginBottom: '1rem', fontSize: '16px' }}>
          Your supplier registration has been successfully submitted. The Yordar team has been notified and will review
          your application. Our team will review your registration details and verify your business
          information/certifications
        </p>
        <div style={{ marginTop: '1.5rem' }}>
          <p style={{ fontSize: '15px' }}>
            <strong>Questions?</strong> Contact us at{' '}
            <a href="mailto:<EMAIL>" style={{ color: '#000', textDecoration: 'underline' }}>
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
);

export default SuccessPanel;
