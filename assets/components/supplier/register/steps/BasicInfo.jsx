import PropTypes from 'prop-types';

const BasicInfo = ({ formData, errors, updateFormData }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    updateFormData(name, value);
  };

  return (
    <div>
      <h3 className="login-heading">Supplier Registration</h3>

      <div className="row">
        <div className="small-12 medium-6 columns">
          <label className="input-label">First Name</label>
          <input
            type="text"
            name="firstname"
            value={formData.firstname}
            onChange={handleChange}
            className={`validate input ${errors.firstname ? 'is-invalid-input' : ''}`}
            placeholder="First name"
            required
          />
          {errors.firstname && <label className="form-error is-visible">{errors.firstname}</label>}
        </div>

        <div className="small-12 medium-6 columns">
          <label className="input-label">Last Name</label>
          <input
            type="text"
            name="lastname"
            value={formData.lastname}
            onChange={handleChange}
            className={`validate input ${errors.lastname ? 'is-invalid-input' : ''}`}
            placeholder="Last name"
            required
          />
          {errors.lastname && <label className="form-error is-visible">{errors.lastname}</label>}
        </div>
      </div>

      <div className="row">
        <div className="small-12 columns">
          <label className="input-label">Email*</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`validate input ${errors.email ? 'is-invalid-input' : ''}`}
            placeholder="Enter your email address"
            required
          />
          {errors.email && <label className="form-error is-visible">{errors.email}</label>}
        </div>
      </div>
    </div>
  );
};

BasicInfo.propTypes = {
  formData: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
  updateFormData: PropTypes.func.isRequired,
};

export default BasicInfo;
