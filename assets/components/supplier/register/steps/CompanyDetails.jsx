import { useContext } from 'react';
import PropTypes from 'prop-types';
import AsyncSelect from 'react-select/async';

import appContext from 'contexts/appContext';
import { fetchSuburbByTerm } from 'utilities/locationSelection';

const CompanyDetails = ({ formData, errors, updateFormData }) => {
  const { countryCode } = useContext(appContext);

  const handleChange = (e) => {
    const { name, value } = e.target;
    updateFormData(name, value);
  };

  const handleSuburbSelection = (suburb) => {
    updateFormData('suburb_id', suburb?.id || '');
    updateFormData('suburb', suburb?.label || '');
  };

  const promiseSuburbOptions = async (term) => {
    if (!term || term.length < 3) return [];
    const suburbs = await fetchSuburbByTerm({
      term,
      countryCode: countryCode || 'AU',
    });
    return suburbs;
  };

  const selectedSuburb =
    formData.suburb_id && formData.suburb ? { id: formData.suburb_id, label: formData.suburb } : null;

  return (
    <div>
      <h3 className="login-heading">Company Details</h3>

      <div className="row">
        <div className="small-12 medium-6 columns">
          <label className="input-label">Company Name*</label>
          <input
            type="text"
            name="company_name"
            value={formData.company_name}
            onChange={handleChange}
            className={`required validate form-input ${errors.company_name ? 'is-invalid-input' : ''}`}
            placeholder="Awesome Inc."
            required
          />
          {errors.company_name && <label className="form-error is-visible">{errors.company_name}</label>}
        </div>

        <div className="small-12 medium-6 columns">
          <label className="input-label">ACN / ABN</label>
          <input
            type="text"
            name="abn_acn"
            value={formData.abn_acn}
            onChange={handleChange}
            className="validate form-input"
          />
        </div>
      </div>

      <div className="row">
        <div className="small-12 medium-6 columns">
          <label className="input-label">Company Address*</label>
          <input
            type="text"
            name="company_address"
            value={formData.company_address}
            onChange={handleChange}
            className={`required validate form-input ${errors.company_address ? 'is-invalid-input' : ''}`}
            placeholder="123 Main St"
            required
          />
          {errors.company_address && <label className="form-error is-visible">{errors.company_address}</label>}
        </div>

        <div className="small-12 medium-6 columns">
          <label className="input-label">Suburb*</label>
          <AsyncSelect
            className={`form-input ${errors.suburb ? 'is-invalid-input' : ''}`}
            cacheOptions
            defaultOptions
            value={selectedSuburb}
            loadOptions={promiseSuburbOptions}
            onChange={handleSuburbSelection}
            placeholder="Enter Suburb or Postcode"
            styles={{
              control: (baseStyles) => ({
                ...baseStyles,
                height: '37px',
                minHeight: '37px',
                border: 'none',
                backgroundColor: 'transparent',
                '&:hover': {
                  border: 'none',
                },
                fontSize: '14px',
                boxShadow: 'none',
              }),
              valueContainer: (baseStyles) => ({
                ...baseStyles,
                height: '35px',
                padding: '0 8px',
              }),
              input: (baseStyles) => ({
                ...baseStyles,
                margin: '0',
                padding: '0',
              }),
              indicatorsContainer: (baseStyles) => ({
                ...baseStyles,
                height: '35px',
              }),
              placeholder: (baseStyles) => ({
                ...baseStyles,
                color: '#aaaaaa',
              }),
            }}
          />
          {errors.suburb && <label className="form-error is-visible">{errors.suburb}</label>}
        </div>
      </div>

      <div className="row">
        <div className="small-12 medium-6 columns">
          <label>BSB Number</label>
          <input
            type="text"
            name="bsb_number"
            value={formData.bsb_number}
            onChange={handleChange}
            className="validate form-input"
          />
        </div>

        <div className="small-12 medium-6 columns">
          <label>Bank Account Number</label>
          <input
            type="text"
            name="bank_account_number"
            value={formData.bank_account_number}
            onChange={handleChange}
            className="validate form-input"
          />
        </div>
      </div>

      <div className="row">
        <div className="small-12 medium-6 columns">
          <label>Phone Number*</label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className={`required validate form-input ${errors.phone ? 'is-invalid-input' : ''}`}
            required
          />
          {errors.phone && <label className="form-error is-visible">{errors.phone}</label>}
        </div>

        <div className="small-12 medium-6 columns">
          <label>Mobile Number</label>
          <input
            type="tel"
            name="mobile"
            value={formData.mobile}
            onChange={handleChange}
            className="validate form-input"
          />
        </div>
      </div>
    </div>
  );
};

CompanyDetails.propTypes = {
  formData: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
  updateFormData: PropTypes.func.isRequired,
};

export default CompanyDetails;
