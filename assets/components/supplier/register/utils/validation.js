// Validation rules
export const validationRules = {
  required:
    (message = 'This field is required') =>
    (value) => {
      if (!value || !value.toString().trim()) {
        return message;
      }
      return null;
    },

  email:
    (message = 'Please enter a valid email address') =>
    (value) => {
      const emailPattern = /^([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})$/i;
      if (value && !emailPattern.test(value)) {
        return message;
      }
      return null;
    },

  minLength: (minLength, message) => (value) => {
    if (value && value.length < minLength) {
      return message || `Must be at least ${minLength} characters`;
    }
    return null;
  },
};

// Validation schemas for each step
export const validationSchemas = {
  0: {
    // Basic Info
    firstname: [validationRules.required('Please enter your first name')],
    lastname: [validationRules.required('Please enter your last name')],
    email: [validationRules.required('Please enter a valid email address'), validationRules.email()],
  },
  1: {
    // Company Details
    company_name: [validationRules.required('Please enter your company name')],
    company_address: [validationRules.required('Please enter your company address')],
    suburb_id: [validationRules.required('Please select a suburb')],
    phone: [validationRules.required('You need to add a phone number')],
  },
  2: {
    // Business Certifications - no required fields, all optional
  },
  3: {
    // Final Details
    category_group: [validationRules.required('Please select a service type')],
    password: [
      validationRules.required('Please enter a password'),
      validationRules.minLength(8, 'Password must be at least 8 characters'),
    ],
  },
};

// Validate a single field
export const validateField = (value, rules) => {
  for (const rule of rules) {
    const error = rule(value);
    if (error) return error;
  }
  return null;
};

// Validate a step
export const validateStep = (stepNumber, formData) => {
  const schema = validationSchemas[stepNumber];
  if (!schema) return { isValid: true, errors: {} };

  const errors = {};

  Object.entries(schema).forEach(([fieldName, rules]) => {
    const value = formData[fieldName];
    const error = validateField(value, rules);
    if (error) {
      // Handle special case for suburb_id -> suburb error key
      const errorKey = fieldName === 'suburb_id' ? 'suburb' : fieldName;
      errors[errorKey] = error;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
