namespace :yordar do

  task notify_pending_orders: :environment do
    # only run this task at 12 every day
    if Time.zone.now.to_s(:hour_only) == '12'

      puts "=========== Starting yordar.notify_pending_orders at #{Time.zone.now} ==========="
      notifications_sender = Admin::Notifications::NotifyPendingOrders.new(time: Time.zone.now).call
      if notifications_sender.pending_orders.present?
        puts "Notified Admin about #{notifications_sender.pending_orders.size} pending custom/quote orders"
      end

      notifications_sender = Customers::Notifications::NotifyPendingOrders.new(time: Time.zone.now).call
      if notifications_sender.pending_orders.present?
        puts "Notified customers about #{notifications_sender.pending_orders.size} pending quote orders"
      end

      puts "=========== Completed yordar.notify_pending_orders at #{Time.zone.now} ==========="
    end
  end

  task closure_emails: :environment do
    now = Time.zone.now
    yordar_last_open_day = (Time.zone.parse(yordar_credentials(:yordar, :closure_start_date)) - 1.day) # Friday
    closure_task = case now.to_s(:date)
    when (yordar_last_open_day - eval(yordar_credentials(:supplier_closure_email_threshold))).to_s(:date) # 4 weeks from closure
      'send_supplier_closure_emails'
    when (yordar_last_open_day - eval(yordar_credentials(:customer_closure_email_threshold))).to_s(:date) # 3 weeks from closure
      'send_customer_closure_emails'
    when (yordar_last_open_day - eval(yordar_credentials(:final_closure_email_threshold))).to_s(:date) # 10 days from closure
      'skip_orders_in_closure_period'
    end
    if closure_task.present?
      Rake::Task["yordar:#{closure_task}"].execute
    end
  end

  task send_supplier_closure_emails: :environment do |task|
    puts "=========== Starting yordar.send_supplier_closure_emails at #{Time.zone.now} ==========="
    notifications_sender = Suppliers::Notifications::SendInitialClosureEmails.new.call

    puts "Customer initial closure email (sent) . . . . . . . #{notifications_sender.sent_notifications.size}"
    if notifications_sender.errors.present?
      @errors.push({
        task: task.name,
        error_list: notifications_sender.errors
      })
    end
    puts "=========== Completed yordar.send_supplier_closure_emails at #{Time.zone.now} ==========="
  end

  task send_customer_closure_emails: :environment do |task|
    puts "=========== Starting yordar.send_customer_closure_emails at #{Time.zone.now} ==========="
    notifications_sender = Customers::Notifications::SendClosureEmails.new(version: 'initial').call

    puts "Customer initial closure email (sent) . . . . . . . #{notifications_sender.sent_notifications.size}"
    if notifications_sender.errors.present?
      @errors.push({
        task: task.name,
        error_list: notifications_sender.errors
      })
    end
    puts "=========== Completed yordar.send_customer_closure_emails at #{Time.zone.now} ==========="
  end

  task skip_orders_in_closure_period: :environment do |task|
    puts "=========== Starting yordar.skip_orders_in_closure_period at #{Time.zone.now} ==========="
    customer_notifications_sender = Customers::Notifications::SendClosureEmails.new(version: 'final').call

    puts "Customer final closure emails (sent) . . . . . . . #{customer_notifications_sender.sent_notifications.size}"
    if !customer_notifications_sender.success?
      @errors.push({
        task: task.name,
        error_list: customer_notifications_sender.errors
      })
    end

    supplier_notifications_sender = Suppliers::Notifications::SendFinalClosureEmails.new.call

    puts "Supplier final closure emails (sent) . . . . . . . #{supplier_notifications_sender.sent_notifications.size}"
    if !supplier_notifications_sender.success?
      @errors.push({
        task: task.name,
        error_list: supplier_notifications_sender.errors
      })
    end

    order_skipper = Orders::SkipClosureOrders.new.call

    if !order_skipper.success?
      @errors.push({
        task: task.name,
        error_list: order_skipper.errors
      })
    end
    puts "=========== Completed yordar.skip_orders_in_closure_period at #{Time.zone.now} ==========="
  end

  task send_daily_errors_email: :environment do
    puts "=========== Starting yordar.send_daily_errors_email at #{Time.zone.now} ==========="
    if @errors.present?
      puts "Total number of errors: #{@errors.count}"
      puts "send_daily_errors_email errors: #{@errors.inspect}"
      if @errors.count > 0
        Admin::Emails::SendRakeTaskErrorsEmail.new(errors: @errors).call
      end
    end
    puts "=========== Completed yordar.send_daily_errors_email at #{Time.zone.now} ==========="
  end

  # can run it as yordar:sync_holidays[2020]
  desc 'Task to sync Holidays with the Calendarific API'
  task :sync_holidays, [:year] => :environment do |_, args|
    year = args[:year].try(:to_i)
    holidays = Holidays::SyncWithAPI.new(year: year).call
    puts "Imported #{holidays.size} holidays"
  end

  desc 'Create Holiday Notifications for the month'
  task :log_calendar_events, [:month,:year] => :environment do |task, args|
    today = Date.today
    month = args[:month].present? && (1..12).include?(args[:month].to_i) ? args[:month].to_i : today.month
    year = args[:year].present? ? args[:month].to_i : today.year

    is_forced = args[:month].present? || args[:year].present?
    if !is_forced && today != today.beginning_of_month
      puts "**** yordar.create_holiday_notifications task only runs at the start of each month (today: #{today.to_s(:date_verbose)}) ****"
      next
    end

    puts "=========== Starting yordar.create_holiday_notifications at #{Time.zone.now} ==========="
    calendar_month = Date.today.beginning_of_month.change(month: month, year: year)
    puts "Generating Calendar Events for #{calendar_month.to_s(:date_verbose)}"

    events_generator = Holidays::LogCalendarEvents.new(month: calendar_month).call
    if events_generator.success?
      puts "Generted #{events_generator.events.size} Calendar events"
    else
      @errors.push({
        task: task.name,
        error_list: events_generator.errors
      })
    end
    puts "=========== Completed yordar.create_holiday_notifications at #{Time.zone.now} ==========="
  end

  desc 'Send Admin Reminders'
  task :send_admin_reminders, [:reminder_on,:frequency] => :environment do |_, args|
    is_forced = args[:reminder_on].present? || args[:frequency].present?
    reminder_on = args[:reminder_on].present? ? Time.zone.parse(args[:reminder_on]) : Time.zone.now
    frequencies = args[:frequency].present? ? (Reminder::VALID_FREQUENCIES & [args[:frequency]]) : Reminder::VALID_FREQUENCIES

    # only runs at 10am unless forced
    if reminder_on.hour != 10 && !is_forced
      next

    end
    
    frequencies.each do |frequency|
      puts "Sending #{frequency} reminders on #{reminder_on.to_s(:date_verbose)}"
      notifications_sender = Admin::Notifications::SendReminders.new(time: reminder_on, frequency: frequency).call
      if !notifications_sender.success?
        Raven.capture_exception(RakeTaskError.new("There was an error sending #{frequency} reminders"),
          extra: { errors: notifications_sender.errors },
          transaction: 'rake yordar:send_admin_reminders'
        )
      end
      if notifications_sender.sent_notifications.present?
        puts "Sent #{notifications_sender.sent_notifications.size} reminder(s)"
      end
    end
  end

end
