{"name": "yordar-app", "private": true, "devDependencies": {"babel-eslint": "^10.1.0", "cypress": "^3.8.1", "eslint": "^7.12.1", "eslint-config-airbnb": "^18.2.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "prettier": "^2.1.2", "webpack-dev-server": "^3.11.2"}, "scripts": {"open-cypress": "yarn cypress open", "lint": "eslint . --ext .js,.jsx"}, "dependencies": {"@babel/preset-react": "^7.14.5", "@hassanmojab/react-modern-calendar-datepicker": "^3.1.7", "@rails/actioncable": "^7.1.3-2", "@rails/webpacker": "5.4.2", "@stripe/react-stripe-js": "^1.6.0", "@stripe/stripe-js": "^1.21.2", "@szhsin/react-menu": "^4.2.4", "axios": "^0.23.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "chart.js": "^4.0.1", "countup.js": "^2.8.0", "date-fns": "^3.6.0", "debounce-promise": "^3.1.2", "domready": "^1.0.8", "foundation-sites": "^6.6.3", "highcharts": "^9.1.2", "imports-loader": "^3.0.0", "jquery": "^3.6.0", "jquery-ui": "^1.12.1", "jquery-ui-timepicker-addon": "^1.6.3", "js-cookie": "^3.0.0", "moment": "^2.29.3", "prop-types": "^15.7.2", "react": "^17.0.2", "react-chartjs-2": "4.0.1", "react-datepicker": "^4.5.0", "react-dnd": "^14.0.4", "react-dnd-html5-backend": "^14.0.2", "react-dom": "^17.0.2", "react-dropdown": "^1.9.2", "react-google-autocomplete": "^2.6.1", "react-google-recaptcha": "^3.1.0", "react-infinite-scroll-hook": "^4.0.1", "react-on-visible": "^1.6.0", "react-places-autocomplete": "^7.3.0", "react-redux": "^7.2.5", "react-responsive-modal": "^6.1.0", "react-select": "^5.1.0", "react-toastify": "9.0.3", "react-tooltip": "^4.2.21", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.3.0", "select2": "^4.0.13", "viewloader": "^2.0.2", "webpack": "^4.46.0", "webpack-cli": "^3.3.12", "zustand": "^4.1.5"}}